# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **Bodhi Browser** project - a secure browser extension and JavaScript library for connecting web applications to local Large Language Model (LLM) services. The project consists of three main components in a monorepo structure:

1. **bodhi-browser-ext**: Chrome extension that bridges web pages and local LLM servers
2. **bodhi-js**: JavaScript/TypeScript library (@bodhiapp/bodhijs) providing a typed API for web applications
3. **mock-llm-server**: Testing infrastructure that provides OpenAI-compatible endpoints

## Development Commands

### Root Level Commands (Makefile)
- `make` or `make all` - Default target that builds and tests all components
- `make setup` - Install dependencies for all components using exact versions (npm ci)
- `make install` - Install dependencies using npm install
- `make build` - Build all components
- `make test` - Run all tests
- `make lint` - Run ESLint checks on all components
- `make lint-fix` - Fix ESLint and formatting issues automatically

### Component-Specific Commands

#### bodhi-js (JavaScript Library)
- `make bodhi-js` - Build and test the library (equivalent to: setup, lint-fix, build, test)
- `make bodhi-js-build` - Build the library only
- `make bodhi-js-test` - Run library tests
- `make bodhi-js-validate` - Run validation (lint checks)

#### bodhi-browser-ext (Chrome Extension)
- `make ext` - Build and test the extension (equivalent to: setup, lint-fix, validate, build, test)
- `make ext-build` - Build the extension only
- `make ext-test` - Run extension tests
- `make ext-test-brave` - Run tests with Brave browser

#### Individual Component Access
- `cd bodhi-js && npm run build` - Build the library directly
- `cd bodhi-browser-ext && npm run build` - Build the extension directly
- `cd bodhi-js && npm test` - Run library tests directly
- `cd bodhi-browser-ext && npm test` - Run extension tests directly

### Testing Commands
- `make ci.test` - Run all tests in CI mode
- `cd bodhi-browser-ext && npm run test:ci` - Run extension tests in CI mode
- `cd bodhi-js && npm run validate` - Run library validation

## Architecture Overview

### System Architecture
The Bodhi Browser system uses a three-layer architecture:

```
Web Page ↔ bodhijs Library ↔ Chrome Extension ↔ Local LLM Server
```

### Component Communication
- **Web Page to Extension**: Uses `window.bodhiext` API created by inject script
- **Message Flow**: window.postMessage → Content Script → Background Script (Service Worker) → HTTP requests to local server
- **Streaming**: Long-lived Chrome runtime connections with ReadableStream/AsyncIterator patterns

### Key Technical Details
- **Extension Scripts**: background.ts (service worker), content.ts (injected bridge), inject.ts (creates window.bodhiext)
- **Message Format**: Unified request/response structure with unique requestIds
- **Streaming Implementation**: Server-Sent Events (SSE) parsing with chunk-by-chunk processing
- **Default Backend**: http://localhost:1135 (configurable via extension UI)

## Code Architecture

### bodhi-js Library Structure
- **Entry Point**: `src/index.ts` - Main exports and documentation
- **Core Classes**: `src/core.ts` - BodhiPlatform and BodhiExtClient classes
- **Onboarding**: `src/onboarding/` - Modal system for extension setup
- **Types**: `src/types.ts` - TypeScript interfaces for API and state management
- **Build**: Rollup builds to CommonJS, ES modules, and TypeScript definitions

### Extension Architecture
- **UI Framework**: Next.js with static export for Chrome compatibility
- **Scripts**: Webpack build with TypeScript for extension scripts
- **Communication**: Chrome runtime messaging with structured error handling
- **Configuration**: Chrome storage API for backend URL settings

### Testing Infrastructure
- **Framework**: Vitest with Playwright for real browser automation
- **Mock Server**: OpenAI-compatible endpoints for testing API flows
- **Test Categories**: Unit tests, integration tests, extension-to-extension communication tests
- **Browser Support**: Chrome/Chromium with extension loaded during tests

## Development Guidelines

### Task Completion Verification
- Always run `make lint-fix` if you encounter linting errors
- For bodhi-js changes: Run `make bodhi-js` to ensure full validation
- For extension changes: Run `make ext` to ensure full validation  
- For repository-wide changes: Run `make all` as final verification
- Individual tests: `cd [component] && npm test` for focused testing

### Code Style
- **Indentation**: 2 spaces throughout codebase
- **TypeScript**: Strict typing in bodhi-js, type safety in extension
- **Naming**: Clear, descriptive variable and function names
- **Comments**: Document complex logic, especially in message passing and streaming code
- **Patterns**: Follow existing patterns within each component

### Key Files to Understand
- `/ai-docs/context/architecture.md` - Detailed system architecture
- `/.cursor/rules/default.mdc` - Project-specific development guidelines
- `/bodhi-js/src/index.ts` - Library API surface and usage examples
- `/bodhi-browser-ext/src-ext/background.ts` - Extension service worker implementation
- Component-specific Makefiles for build processes

### Testing Philosophy
- Prefer integration tests over heavily mocked unit tests
- Use real browser automation for extension testing
- Each test should provide actual value, not just coverage
- Mock LLM server automatically handles OpenAI-compatible test scenarios

## Common Workflows

### Adding New Features
1. Identify which component(s) need changes
2. Make changes following existing patterns
3. Run component-specific validation: `make [component-name]`
4. Run full repository validation: `make all`
5. Verify tests pass and code follows established patterns

### Debugging Extension Issues
- Check browser console for `[Bodhi/...]` prefixed logs
- Use extension's settings UI to verify backend URL configuration
- Test with mock-llm-server for isolated debugging
- Use `make ext-test` for automated browser testing

### Library Development
- Use `cd bodhi-js && npm run dev` for watch mode during development
- Test against extension with test app: `make bodhi-js-test`
- Verify TypeScript definitions are properly exported

This project prioritizes secure, reliable communication between web applications and local AI services while maintaining a clean developer experience.