{"permissions": {"allow": ["<PERSON><PERSON>(make:*)", "Bash(npx vitest run:*)", "Bash(npm run build:*)", "Bash(ls:*)", "Bash(npm test)", "Bash(npx vitest list:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm run lint:*)", "Bash(npx tsc:*)", "Bash(npm run copy-modal:*)", "mcp__ide__executeCode", "Bash(python test_modal_scroll.py:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_evaluate", "WebFetch(domain:github.com)", "Bash(npm install:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__playwright__browser_close", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(npm test:*)"]}, "enableAllProjectMcpServers": false}