/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Chat Page Layout */
.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  background-color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Header */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left h1 {
  color: #1a73e8;
  font-size: 24px;
  font-weight: 600;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
}

/* Model Selector */
.model-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.model-label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.model-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  min-width: 200px;
  cursor: pointer;
}

.model-select:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.model-select:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.model-loading,
.model-error {
  padding: 8px 12px;
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
}

.model-error {
  color: #dc2626;
}

/* New Chat Button */
.new-chat-button {
  padding: 8px 16px;
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-chat-button:hover:not(:disabled) {
  background-color: #1557b0;
}

.new-chat-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Error Banner */
.error-banner {
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  padding: 12px 24px;
  color: #dc2626;
}

/* Main Chat Area */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Chat Interface */
.chat-interface {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  scroll-behavior: smooth;
}

/* Welcome Message */
.welcome-message {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.welcome-message h3 {
  font-size: 24px;
  margin-bottom: 12px;
  color: #374151;
}

.welcome-message p {
  font-size: 16px;
}

/* Messages */
.message {
  margin-bottom: 24px;
  max-width: 80%;
  animation: fadeIn 0.3s ease-in;
}

.message.user {
  margin-left: auto;
  margin-right: 0;
}

.message.assistant {
  margin-left: 0;
  margin-right: auto;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-role {
  font-weight: 600;
  font-size: 14px;
}

.message.user .message-role {
  color: #1a73e8;
}

.message.assistant .message-role {
  color: #059669;
}

.message-time {
  font-size: 12px;
  color: #6b7280;
}

.message-content {
  padding: 16px;
  border-radius: 12px;
  line-height: 1.5;
  word-wrap: break-word;
}

.message.user .message-content {
  background-color: #1a73e8;
  color: white;
}

.message.assistant .message-content {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.message.streaming .message-content {
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  position: relative;
}

/* Typing indicators */
.typing-cursor {
  animation: blink 1s infinite;
  color: #1a73e8;
  font-weight: bold;
}

.thinking {
  color: #6b7280;
  font-style: italic;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Chat Footer */
.chat-footer {
  padding: 24px;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
}

/* Chat Input */
.chat-input {
  width: 100%;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  resize: none;
  overflow-y: auto;
  line-height: 1.4;
}

.message-input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.message-input:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.send-button {
  padding: 12px 24px;
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
  min-height: 44px;
}

.send-button:hover:not(:disabled) {
  background-color: #1557b0;
}

.send-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* System Status */
.system-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40px;
  text-align: center;
}

.system-status.checking {
  color: #6b7280;
}

.system-status.error {
  color: #dc2626;
}

.system-status h2 {
  font-size: 28px;
  margin-bottom: 16px;
}

.system-status p {
  font-size: 16px;
  margin-bottom: 24px;
  max-width: 500px;
}

.retry-button {
  padding: 12px 24px;
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #1557b0;
}

/* Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Boundary */
.error-boundary {
  padding: 40px;
  text-align: center;
  color: #dc2626;
}

.error-boundary h2 {
  margin-bottom: 16px;
}

.error-boundary p {
  margin-bottom: 24px;
}

.error-details {
  margin: 24px 0;
  text-align: left;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.error-details summary {
  cursor: pointer;
  padding: 8px;
  background-color: #f3f4f6;
  border-radius: 4px;
}

.error-details pre {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  margin-top: 8px;
}

.reload-button {
  padding: 12px 24px;
  background-color: #dc2626;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.reload-button:hover {
  background-color: #b91c1c;
}

/* Error Popup */
.error-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-popup {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.error-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #fee2e2;
}

.error-popup-header h3 {
  margin: 0;
  color: #dc2626;
  font-size: 18px;
  font-weight: 600;
}

.error-popup-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-popup-close:hover {
  color: #374151;
}

.error-popup-content {
  padding: 20px;
  color: #374151;
  line-height: 1.5;
}

.error-popup-content p {
  margin: 0;
}

.error-popup-footer {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

.error-popup-button {
  padding: 8px 16px;
  background-color: #dc2626;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-popup-button:hover {
  background-color: #b91c1c;
}

/* Authentication Styles */

/* Loading Screen */
.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f9fafb;
}

.loading-screen .loading-spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
}

.loading-screen p {
  color: #6b7280;
  font-size: 16px;
}

/* Login Page */
.login-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 40px;
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.login-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.login-header p {
  color: #6b7280;
  margin: 0 0 32px 0;
  font-size: 16px;
}

.login-content h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.login-content p {
  color: #6b7280;
  margin: 0 0 32px 0;
  font-size: 14px;
}

.login-button {
  width: 100%;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.login-footer {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.login-footer p {
  color: #9ca3af;
  font-size: 12px;
  margin: 0;
}

/* Auth Callback */
.auth-callback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 20px;
}

.auth-callback-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.auth-processing, .auth-success, .auth-error {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.auth-processing h2, .auth-success h2, .auth-error h2 {
  margin: 16px 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.auth-processing p, .auth-success p, .auth-error p {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.auth-success h2 {
  color: #059669;
}

.auth-error h2 {
  color: #dc2626;
}

.success-icon, .error-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.success-icon {
  background-color: #059669;
}

.error-icon {
  background-color: #dc2626;
}

/* User Profile */
.user-profile {
  position: relative;
}

.user-menu {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.user-button:hover:not(:disabled) {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.user-name {
  color: #374151;
  font-weight: 500;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chevron {
  transition: transform 0.2s;
  color: #6b7280;
}

.chevron.open {
  transform: rotate(180deg);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
}

.user-info {
  padding: 16px;
}

.user-details {
  text-align: left;
}

.user-name-full {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  font-size: 14px;
}

.user-email {
  color: #6b7280;
  font-size: 12px;
}

.dropdown-separator {
  margin: 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

.logout-button {
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logout-button:hover:not(:disabled) {
  background-color: #fef2f2;
}

.logout-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.logout-spinner {
  width: 14px;
  height: 14px;
  border: 1.5px solid transparent;
  border-top: 1.5px solid #dc2626;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .header-center {
    justify-content: flex-start;
  }

  .model-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .model-select {
    min-width: 100%;
  }

  .message {
    max-width: 95%;
  }

  .messages-container {
    padding: 16px;
  }

  .chat-footer {
    padding: 16px;
  }

  .input-container {
    flex-direction: column;
    gap: 8px;
  }

  .send-button {
    width: 100%;
  }
} 