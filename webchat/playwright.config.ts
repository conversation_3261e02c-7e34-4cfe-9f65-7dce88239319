import { defineConfig, devices } from '@playwright/test';

/**
 * Standardized Playwright configuration for webchat
 * Handles browser automation for web application testing with extension integration
 */
export default defineConfig({
  // Test directory
  testDir: './tests',

  // Global test timeout
  timeout: 60 * 1000, // Longer timeout for integration tests

  // Expect timeout for assertions
  expect: {
    timeout: 5 * 1000,
  },

  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,

  // Retry on CI only
  retries: process.env.CI ? 2 : 0,

  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : undefined,

  // Reporter to use
  reporter: [
    ['html'],
    ['list'],
    process.env.CI ? ['github'] : ['line'],
  ],

  // Shared settings for all tests
  use: {
    // Global timeout for all Playwright actions
    actionTimeout: 10 * 1000,

    // Collect trace when retrying the failed test
    trace: 'on-first-retry',

    // Take screenshot on failure
    screenshot: 'only-on-failure',

    // Record video on failure
    video: 'retain-on-failure',

    // Browser context options
    contextOptions: {
      // Ignore HTTPS errors
      ignoreHTTPSErrors: true,
    },
  },

  // Configure projects for major browsers
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Chrome-specific args for extension testing
        launchOptions: {
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
          ],
        },
      },
    },
  ],

  // Global setup and teardown
  globalSetup: require.resolve('./tests/setup.ts'),
}); 