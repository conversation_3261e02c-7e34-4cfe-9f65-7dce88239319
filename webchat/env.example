# OAuth Configuration for Webchat App
# 
# NOTE: OAuth configuration is now handled in src/utils/constants.ts
# No environment variables are needed for OAuth setup.
# 
# Current configuration:
# - AUTH_URL: https://nightly-id.getbodhi.app/realms/bodhi
# - CLIENT_ID: app-e11ed4e1-8f68-4389-91f1-a31ef839ad4f
# - Scope: openid email profile roles scope_user_user
# - Redirect URI: {window.location.origin}/auth
# - PKCE is enabled by default for security
# - Keycloak v23 compatible endpoints are used
#
# To modify OAuth settings, edit src/utils/constants.ts 