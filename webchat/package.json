{"name": "webchat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest run", "test:ui": "vitest --ui", "test:e2e": "vitest run --config vitest.e2e.config.ts", "test:e2e:watch": "vitest --config vitest.e2e.config.ts"}, "dependencies": {"@bodhiapp/bodhijs": "file:../bodhi-js", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0"}, "devDependencies": {"@bodhiapp/app-bindings": "^0.0.13", "@eslint/js": "^9.13.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/fs-extra": "^11.0.4", "@types/node": "^20.11.24", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "dotenv": "^17.1.0", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "fs-extra": "^11.2.0", "globals": "^15.11.0", "jsdom": "^25.0.1", "playwright": "^1.54.0", "playwright-core": "^1.54.0", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^5.4.10", "vitest": "^2.1.8"}}