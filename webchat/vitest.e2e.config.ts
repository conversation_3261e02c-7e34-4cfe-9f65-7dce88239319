import { defineConfig } from 'vitest/config';
import { config } from 'dotenv';

// Load .env.test file for test environment variables
config({ path: '.env.test' });

export default defineConfig({
  test: {
    include: ['tests/**/*.test.ts'],
    exclude: ['**/node_modules/**'],
    environment: 'node',

    // Standardized timeout configuration
    testTimeout: 60000, // 60 seconds for e2e tests
    hookTimeout: 30000, // 30 seconds for setup/teardown hooks

    setupFiles: ['./tests/setup.ts'],
    sequence: {
      hooks: 'list', // Run hooks in order to maintain compatibility with Jest
      setupFiles: 'list', // Execute setup files in sequence
    },

    globals: true,

    // Run only one file at a time for e2e tests
    fileParallelism: false, // Ensure files run sequentially

    // Retry configuration for CI stability
    retry: process.env.CI ? 2 : 0,

    // Reporter configuration
    reporter: process.env.CI ? ['github', 'verbose'] : ['verbose'],
  },
}); 