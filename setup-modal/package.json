{"name": "bodhi-platform-modal", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "build:fast": "node ../scripts/build-fast.mjs . 'npm run build' src package.json vite.config.ts", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "preview": "npx vite preview", "test": "vitest run", "test:watch": "vitest --watch", "test:ui": "vitest --ui"}, "dependencies": {"clsx": "latest", "lucide-react": "^0.441.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "tailwind-merge": "latest"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "jsdom": "^24.0.0", "postcss": "latest", "tailwindcss": "3.4.17", "terser": "^5.43.1", "typescript": "^5.5.4", "vite": "^5.2.0", "vite-plugin-singlefile": "^2.3.0", "vitest": "^1.3.1"}}