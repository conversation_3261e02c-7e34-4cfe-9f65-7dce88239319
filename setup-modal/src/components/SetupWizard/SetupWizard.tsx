import { StepIndicator } from '@/components/SetupWizard/StepIndicator';
import { ExtensionSetup } from '@/components/SetupWizard/Steps/ExtensionSetup';
import { PlatformCheck } from '@/components/SetupWizard/Steps/PlatformCheck';
import { ServerSetup } from '@/components/SetupWizard/Steps/ServerSetup';
import { SuccessState } from '@/components/SetupWizard/Steps/SuccessState';
import iconBase64Data from '@/icon.txt?raw';
import { MODAL_OUT_CLOSE, MODAL_OUT_REFRESH, ModalOutMessageType, SetupStateOrNull, SetupStep } from '@/lib/types';
import { AlertTriangle, Loader2, RefreshCw, X } from 'lucide-react';
import { useEffect, useState } from 'react';
interface SetupWizardProps {
  setupState: SetupStateOrNull;
  sendAction: (actionType: ModalOutMessageType, data?: unknown) => void;
}
export function SetupWizard({
  setupState,
  sendAction
}: SetupWizardProps) {
  const [currentStep, setCurrentStep] = useState<SetupStep>(SetupStep.PLATFORM_CHECK);
  const [isRefreshing, setIsRefreshing] = useState(false);
  // Determine initial step based on state - always start with platform check
  const determineInitialStep = (state: SetupStateOrNull): SetupStep => {
    if (!state) return SetupStep.PLATFORM_CHECK;

    const { extension, server, env, browsers, os } = state;

    // Check if platform is properly detected and supported
    const isBrowserSupported = browsers.some(b => b.id === env.browser && b.status === 'supported');
    const isOSSupported = os.some(o => o.id === env.os && o.status === 'supported');

    // Always show platform check first if platform is not fully supported
    if (!isBrowserSupported || !isOSSupported) {
      return SetupStep.PLATFORM_CHECK;
    }

    // Only proceed to other steps if platform is confirmed supported
    if (extension.status !== 'ready') return SetupStep.EXTENSION_SETUP;
    if (server.status !== 'ready') return SetupStep.SERVER_SETUP;
    return SetupStep.COMPLETE;
  };

  // Set initial step on state changes (including refresh)
  useEffect(() => {
    const initialStep = determineInitialStep(setupState);
    setCurrentStep(initialStep);
  }, [setupState]);

  const handleStepClick = (step: SetupStep) => {
    // All steps are accessible for manual navigation
    setCurrentStep(step);
  };
  const handleRefresh = () => {
    setIsRefreshing(true);
    sendAction(MODAL_OUT_REFRESH);

    // Reset refreshing state after a delay (parent should handle actual state updates)
    setTimeout(() => {
      setIsRefreshing(false);
    }, 2000);
  };

  const handleClose = () => {
    sendAction(MODAL_OUT_CLOSE);
  };

  // Check if platform is not supported (only if we have state)
  const isPlatformNotSupported = setupState ? (() => {
    const { env, browsers, os } = setupState;
    const isBrowserSupported = browsers.some(b => b.id === env.browser && b.status === 'supported');
    const isOSSupported = os.some(o => o.id === env.os && o.status === 'supported');
    return !isBrowserSupported || !isOSSupported;
  })() : false;

  return <div className="flex flex-col h-screen">
    <div className="p-4 border-b flex items-center justify-between flex-shrink-0">
      <div className="flex items-center">
        <img src={`data:image/png;base64,${iconBase64Data.trim()}`} alt="Bodhi Logo" className="w-8 h-8 mr-3" />
        <h2 className="text-xl font-semibold text-gray-900">
          Bodhi Platform Setup
        </h2>
        {!setupState && (
          <div className="ml-3" data-testid="loading-indicator" title="Loading setup data...">
            <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
          </div>
        )}
        {isPlatformNotSupported && (
          <div className="ml-3" data-testid="platform-not-supported-indicator" title="Platform not supported - setup may not work">
            <AlertTriangle className="w-4 h-4 text-red-500" />
          </div>
        )}
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick={handleRefresh}
          className={`p-1.5 rounded-full hover:bg-gray-100 ${isRefreshing ? 'bg-blue-50' : ''}`}
          title="Refresh detection"
          data-testid="refresh-button"
          data-refreshing={isRefreshing}
          disabled={isRefreshing}
        >
          <RefreshCw className={`w-4 h-4 text-gray-600 hover:text-blue-600 ${isRefreshing ? 'animate-spin text-blue-600' : ''}`} />
        </button>
        <button
          onClick={handleClose}
          className="p-1.5 rounded-full hover:bg-gray-100 hover:bg-red-50"
          title="Close modal"
          data-testid="close-button"
        >
          <X className="w-4 h-4 text-gray-600 hover:text-red-600" />
        </button>
      </div>
    </div>
    <div className="flex flex-col flex-1 p-4 min-h-0">
      <div className="flex-shrink-0">
        <StepIndicator currentStep={currentStep} setupState={setupState} onStepClick={handleStepClick} />
      </div>
      {/* Scrollable content area with proper height constraints */}
      <div className="mt-6 flex-1 overflow-y-auto min-h-0 w-full">
        {!setupState ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Loader2 className="w-8 h-8 text-blue-500 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading setup data...</p>
            </div>
          </div>
        ) : (
          <>
            {currentStep === SetupStep.PLATFORM_CHECK && <PlatformCheck setupState={setupState} />}
            {currentStep === SetupStep.EXTENSION_SETUP && <ExtensionSetup setupState={setupState} />}
            {currentStep === SetupStep.SERVER_SETUP && <ServerSetup setupState={setupState} />}
            {currentStep === SetupStep.COMPLETE && <SuccessState setupState={setupState} sendAction={sendAction} />}
          </>
        )}
      </div>
    </div>
  </div>;
}