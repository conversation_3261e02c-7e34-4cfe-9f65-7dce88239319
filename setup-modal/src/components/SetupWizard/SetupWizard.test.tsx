import { SetupWizard } from '@/components/SetupWizard/SetupWizard';
import { SetupStep } from '@/lib/types';
import {
  createAllSystemsReadyState,
  createExtensionNotInstalledState,
  createMockState,
  createResourceAdminServerState,
  createServerNotReadyState,
  createSetupServerState,
  createUnreachableExtensionState,
  createUnsupportedPlatformState,
} from '@/test/mock-factories';
import { render, screen } from '@/test/test-utils';
import userEvent from '@testing-library/user-event';

const mockSendAction = vi.fn();

describe('SetupWizard - Step Navigation', () => {
  beforeEach(() => {
    mockSendAction.mockClear();
  });

  test('should show loading state when setupState is null', () => {
    render(<SetupWizard setupState={null} sendAction={mockSendAction} />);

    expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
    expect(screen.getByText('Loading setup data...')).toBeInTheDocument();
  });

  // Parameterized tests for step navigation logic
  describe('Step Navigation Logic', () => {
    const stepNavigationTestCases = [
      {
        name: 'unsupported browser (firefox) with macos -> Platform Check',
        state: createUnsupportedPlatformState('firefox', 'macos'),
        expectedStep: SetupStep.PLATFORM_CHECK,
        expectedText: 'Platform Compatibility Check',
      },
      {
        name: 'unsupported OS (linux) with chrome -> Platform Check',
        state: createUnsupportedPlatformState('chrome', 'linux'),
        expectedStep: SetupStep.PLATFORM_CHECK,
        expectedText: 'Platform Compatibility Check',
      },
      {
        name: 'both unsupported (firefox + linux) -> Platform Check',
        state: createUnsupportedPlatformState('firefox', 'linux'),
        expectedStep: SetupStep.PLATFORM_CHECK,
        expectedText: 'Platform Compatibility Check',
      },
      {
        name: 'supported platform + extension not-installed -> Extension Setup',
        state: createExtensionNotInstalledState(),
        expectedStep: SetupStep.EXTENSION_SETUP,
        expectedText: 'Extension is not installed',
      },
      {
        name: 'supported platform + extension unreachable -> Extension Setup',
        state: createMockState({
          extension: createUnreachableExtensionState(),
        }),
        expectedStep: SetupStep.EXTENSION_SETUP,
        expectedText: 'Could not connect to extension',
      },
      {
        name: 'supported platform + extension ready + server unreachable -> Server Setup',
        state: createServerNotReadyState(),
        expectedStep: SetupStep.SERVER_SETUP,
        expectedText: 'Server connection refused',
      },
      {
        name: 'supported platform + extension ready + server in setup -> Server Setup',
        state: createMockState({
          server: createSetupServerState(),
        }),
        expectedStep: SetupStep.SERVER_SETUP,
        expectedText: 'Server requires initial setup',
      },
      {
        name: 'supported platform + extension ready + server resource-admin -> Server Setup',
        state: createMockState({
          server: createResourceAdminServerState(),
        }),
        expectedStep: SetupStep.SERVER_SETUP,
        expectedText: 'Server in resource admin mode',
      },
      {
        name: 'all systems ready -> Complete',
        state: createAllSystemsReadyState(),
        expectedStep: SetupStep.COMPLETE,
        expectedText: 'All Systems Ready!',
      },
    ];

    test.each(stepNavigationTestCases)(
      'should navigate to correct step: $name',
      ({ state, expectedStep, expectedText }: { state: any, expectedStep: SetupStep, expectedText: string }) => {
        render(<SetupWizard setupState={state} sendAction={mockSendAction} />);

        // Should not show loading indicator when state is provided
        expect(screen.queryByTestId('loading-indicator')).not.toBeInTheDocument();

        // Should show expected step content
        expect(screen.getByText(expectedText)).toBeInTheDocument();

        // Should have correct step indicator active
        const stepElement = screen.getByTestId(`step-${expectedStep}`);
        expect(stepElement).toBeInTheDocument();
      }
    );
  });

  describe('Platform Not Supported Indicator', () => {
    const platformNotSupportedCases = [
      {
        name: 'unsupported browser shows not supported indicator',
        state: createUnsupportedPlatformState('firefox', 'macos'),
        shouldShow: true,
      },
      {
        name: 'unsupported OS shows not supported indicator',
        state: createUnsupportedPlatformState('chrome', 'linux'),
        shouldShow: true,
      },
      {
        name: 'both unsupported shows not supported indicator',
        state: createUnsupportedPlatformState('firefox', 'linux'),
        shouldShow: true,
      },
      {
        name: 'supported platform does not show not supported indicator',
        state: createAllSystemsReadyState(),
        shouldShow: false,
      },
    ];

    test.each(platformNotSupportedCases)(
      '$name',
      ({ state, shouldShow }: { state: any, shouldShow: boolean }) => {
        render(<SetupWizard setupState={state} sendAction={mockSendAction} />);

        const indicator = screen.queryByTestId('platform-not-supported-indicator');

        if (shouldShow) {
          expect(indicator).toBeInTheDocument();
        } else {
          expect(indicator).not.toBeInTheDocument();
        }
      }
    );
  });

  describe('User Interactions', () => {
    test('should call sendAction when refresh button clicked', async () => {
      const user = userEvent.setup();
      render(<SetupWizard setupState={createMockState()} sendAction={mockSendAction} />);

      const refreshButton = screen.getByTestId('refresh-button');
      await user.click(refreshButton);

      expect(mockSendAction).toHaveBeenCalledWith('modal_out:refresh');
    });

    test('should call sendAction when close button clicked', async () => {
      const user = userEvent.setup();
      render(<SetupWizard setupState={createMockState()} sendAction={mockSendAction} />);

      const closeButton = screen.getByTestId('close-button');
      await user.click(closeButton);

      expect(mockSendAction).toHaveBeenCalledWith('modal_out:close');
    });

    test('should allow manual step navigation', async () => {
      const user = userEvent.setup();
      render(<SetupWizard setupState={createAllSystemsReadyState()} sendAction={mockSendAction} />);

      // Should start on Complete step
      expect(screen.getByText('All Systems Ready!')).toBeInTheDocument();

      // Click on Platform Check step
      const platformStep = screen.getByTestId('step-platform-check');
      await user.click(platformStep);

      // Should navigate to Platform Check
      expect(screen.getByText('Platform Compatibility Check')).toBeInTheDocument();
    });
  });

  describe('Step Indicator Count', () => {
    test('should always show 4 step indicators', () => {
      render(<SetupWizard setupState={createMockState()} sendAction={mockSendAction} />);

      expect(screen.getByTestId('step-platform-check')).toBeInTheDocument();
      expect(screen.getByTestId('step-extension-setup')).toBeInTheDocument();
      expect(screen.getByTestId('step-server-setup')).toBeInTheDocument();
      expect(screen.getByTestId('step-complete')).toBeInTheDocument();
    });
  });
});