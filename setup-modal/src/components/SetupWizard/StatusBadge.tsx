import { AlertCircle, CheckCircle, Loader2, Refresh<PERSON><PERSON>, XCircle } from 'lucide-react';

type StatusType = 'success' | 'error' | 'warning' | 'loading' | 'neutral';
interface StatusBadgeProps {
  status: StatusType;
  text?: string;
  onRefresh?: () => void;
  showRefresh?: boolean;
  refreshOnly?: boolean;
}
export function StatusBadge({
  status,
  text,
  onRefresh,
  showRefresh = false,
  refreshOnly = false
}: StatusBadgeProps) {
  const getStatusClasses = () => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'loading':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 mr-1.5" />;
      case 'error':
        return <XCircle className="w-4 h-4 mr-1.5" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 mr-1.5" />;
      case 'loading':
        return <Loader2 className="w-4 h-4 mr-1.5 animate-spin" />;
      default:
        return null;
    }
  };
  if (refreshOnly && onRefresh) {
    return <button onClick={e => {
      e.stopPropagation();
      onRefresh();
    }} className="p-1.5 rounded-full hover:bg-gray-100" title="Refresh detection">
        <RefreshCw className="w-4 h-4 text-gray-600 hover:text-blue-600" />
      </button>;
  }
  return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusClasses()}`}>
      {getStatusIcon()}
      {text}
      {showRefresh && onRefresh && <RefreshCw className="w-3.5 h-3.5 ml-1.5 cursor-pointer hover:text-blue-600" onClick={e => {
      e.stopPropagation();
      onRefresh();
    }} />}
    </span>;
}