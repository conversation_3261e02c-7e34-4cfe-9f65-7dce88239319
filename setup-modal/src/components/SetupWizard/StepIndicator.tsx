import { SetupStateOrNull, SetupStep } from '@/lib/types';
import { CheckCircle, Circle, DownloadCloud, HelpCircle, Laptop, Server, XCircle } from 'lucide-react';
import { Fragment } from 'react';

interface StepIndicatorProps {
  currentStep: SetupStep;
  setupState: SetupStateOrNull;
  onStepClick: (step: SetupStep) => void;
}

export function StepIndicator({
  currentStep,
  setupState,
  onStepClick
}: StepIndicatorProps) {
  // Handle null state - show loading/incomplete indicators
  if (!setupState) {
    const getStepStatus = (step: SetupStep) => {
      if (step === currentStep) return 'current';
      return 'incomplete';
    };
    
    const steps = [
      {
        id: SetupStep.PLATFORM_CHECK,
        name: 'Platform Check',
        icon: <Laptop />,
        status: getStepStatus(SetupStep.PLATFORM_CHECK)
      },
      {
        id: SetupStep.EXTENSION_SETUP,
        name: 'Extension Setup',
        icon: <DownloadCloud />,
        status: getStepStatus(SetupStep.EXTENSION_SETUP)
      },
      {
        id: SetupStep.SERVER_SETUP,
        name: 'Server Setup',
        icon: <Server />,
        status: getStepStatus(SetupStep.SERVER_SETUP)
      },
      {
        id: SetupStep.COMPLETE,
        name: 'Complete',
        icon: <CheckCircle />,
        status: getStepStatus(SetupStep.COMPLETE)
      }
    ];

    return (
      <div className="flex items-center w-full">
        {steps.map((step, index) => {
          const isCurrent = currentStep === step.id;
          const circleClass = isCurrent 
            ? 'w-10 h-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 ring-2 ring-blue-300'
            : 'w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-400 border-2 border-gray-200';

          return (
            <Fragment key={step.id}>
              <div className="flex flex-col items-center">
                <div className={circleClass}>
                  {step.icon}
                </div>
                <span className={`mt-2 text-xs ${isCurrent ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
                  {step.name}
                </span>
              </div>

              {index < steps.length - 1 && (
                <div className="flex-1 h-0.5 mx-2 bg-gray-200" />
              )}
            </Fragment>
          );
        })}
      </div>
    );
  }

  const { extension, server, env, browsers, os } = setupState;
  const extensionState = extension.status;
  const serverState = server.status;
  const isPlatformSupported = browsers.some(b => b.id === env.browser && b.status === 'supported');
  const isOSSupported = os.some(o => o.id === env.os && o.status === 'supported');

  const getStepStatus = (step: SetupStep) => {
    switch (step) {
      case SetupStep.PLATFORM_CHECK:
        if (isPlatformSupported && isOSSupported) return 'complete';
        if (!isPlatformSupported || !isOSSupported) return 'not-supported';
        return 'incomplete';
      case SetupStep.EXTENSION_SETUP:
        if (extensionState === 'ready') return 'complete';
        if (extensionState === 'unreachable' || extensionState === 'unsupported') return 'error';
        return 'incomplete';
      case SetupStep.SERVER_SETUP:
        if (serverState === 'ready') return 'complete';
        if (serverState === 'error') return 'error';
        return 'incomplete';
      case SetupStep.COMPLETE:
        if (extensionState === 'ready' && serverState === 'ready') return 'complete';
        return 'incomplete';
      default:
        return 'incomplete';
    }
  };

  const steps = [
    {
      id: SetupStep.PLATFORM_CHECK,
      name: 'Platform Check',
      icon: <Laptop />,
      status: getStepStatus(SetupStep.PLATFORM_CHECK)
    },
    {
      id: SetupStep.EXTENSION_SETUP,
      name: 'Extension Setup',
      icon: <DownloadCloud />,
      status: getStepStatus(SetupStep.EXTENSION_SETUP)
    },
    {
      id: SetupStep.SERVER_SETUP,
      name: 'Server Setup',
      icon: <Server />,
      status: getStepStatus(SetupStep.SERVER_SETUP)
    },
    {
      id: SetupStep.COMPLETE,
      name: 'Complete',
      icon: <CheckCircle />,
      status: getStepStatus(SetupStep.COMPLETE)
    }
  ];

  const getStepVisuals = (step: typeof steps[0]) => {
    const isCurrent = currentStep === step.id;

    if (step.status === 'complete') {
      return {
        circleClass: 'w-10 h-10 flex items-center justify-center rounded-full bg-green-100 text-green-600',
        icon: <CheckCircle className="w-6 h-6" />,
        textClass: 'text-green-600',
        clickable: true
      };
    }

    if (step.status === 'not-supported') {
      return {
        circleClass: 'w-10 h-10 flex items-center justify-center rounded-full bg-red-100 text-red-600',
        icon: <XCircle className="w-6 h-6" />,
        textClass: 'text-red-600',
        clickable: true
      };
    }

    if (step.status === 'error') {
      return {
        circleClass: 'w-10 h-10 flex items-center justify-center rounded-full bg-red-100 text-red-600',
        icon: <XCircle className="w-6 h-6" />,
        textClass: 'text-red-600',
        clickable: true
      };
    }

    if (isCurrent) {
      return {
        circleClass: 'w-10 h-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 ring-2 ring-blue-300',
        icon: step.icon,
        textClass: 'text-blue-600 font-medium',
        clickable: true
      };
    }

    // Accessible but incomplete
    return {
      circleClass: 'w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-400 border-2 border-gray-200 hover:bg-gray-200 transition-colors',
      icon: step.icon,
      textClass: 'text-gray-500 hover:text-gray-700',
      clickable: true
    };
  };

  return (
    <div className="flex items-center w-full">
      {steps.map((step, index) => {
        const visuals = getStepVisuals(step);

        return (
          <Fragment key={step.id}>
            <div
              className={`flex flex-col items-center ${visuals.clickable ? 'cursor-pointer hover:opacity-80' : 'cursor-default'
                } transition-opacity`}
              data-testid={`step-${step.id}`}
              onClick={() => visuals.clickable && onStepClick(step.id)}
            >
              <div className={visuals.circleClass}>
                {visuals.icon}
              </div>
              <span className={`mt-2 text-xs ${visuals.textClass} transition-colors`}>
                {step.name}
              </span>
            </div>

            {index < steps.length - 1 && (
              <div className={`flex-1 h-0.5 mx-2 transition-colors ${step.status === 'complete' && steps[index + 1].status === 'complete'
                  ? 'bg-green-300'
                  : step.status === 'complete'
                    ? 'bg-gradient-to-r from-green-300 to-gray-200'
                    : step.status === 'not-supported'
                      ? 'bg-gradient-to-r from-red-300 to-gray-200'
                      : 'bg-gray-200'
                }`} />
            )}
          </Fragment>
        );
      })}
    </div>
  );
}