import { PlatformDropdown } from '@/components/common/PlatformDropdown';
import { StatusBadge } from '@/components/SetupWizard/StatusBadge';
import { BrowserType, NotSupportedBrowser, SetupState, SupportedBrowser } from '@/lib/types';
import { ArrowRight, Download, ExternalLink } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ExtensionSetupProps {
  setupState: SetupState;
}

export function ExtensionSetup({
  setupState
}: ExtensionSetupProps) {
  const { extension, env, browsers } = setupState;
  
  // Use env.browser as source of truth, with temporary override for exploration
  const [tempBrowserOverride, setTempBrowserOverride] = useState<BrowserType | null>(null);
  
  // Always use env.browser unless user has temporarily overridden
  const selectedBrowser = tempBrowserOverride || env.browser;
  
  // Reset temp override when env.browser changes (new state received)
  useEffect(() => {
    setTempBrowserOverride(null);
  }, [env.browser]);
  
  const setSelectedBrowser = (browser: BrowserType) => {
    setTempBrowserOverride(browser);
  };
  
  // Use error message and code from setupState
  const getStatusInfo = () => {
    if (extension.status === 'ready') {
      return {
        status: 'success' as const,
        title: 'Extension Ready',
        message: `Extension version ${extension.version} is connected and ready.`
      };
    }
    
    // Use error from setupState
    return {
      status: extension.status === 'unsupported' ? 'warning' as const : 'error' as const,
      title: extension.error.message,
      message: `Error Code: ${extension.error.code}`
    };
  };

  const statusInfo = getStatusInfo();
  const selectedBrowserData = browsers.find(b => b.id === selectedBrowser);
  
  const handleInstallExtension = () => {
    const targetBrowser = browsers.find(b => b.id === selectedBrowser && b.status === 'supported') as SupportedBrowser;
    if (targetBrowser) {
      window.open(targetBrowser.extension_url, '_blank');
    }
  };

  const handleViewGitHubIssue = () => {
    const targetBrowser = browsers.find(b => b.id === selectedBrowser && b.status === 'not-supported') as NotSupportedBrowser;
    if (targetBrowser?.github_issue_url) {
      window.open(targetBrowser.github_issue_url, '_blank');
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center mb-4">
          <div className="flex-1">
            <h4 className="text-base font-medium text-gray-900">
              {statusInfo.title}
            </h4>
            <p className="mt-1 text-sm text-gray-500">{statusInfo.message}</p>
          </div>
          <StatusBadge status={statusInfo.status} />
        </div>

        {/* Browser selection dropdown */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Browser (for extension store link)
          </label>
          <PlatformDropdown
            type="browser"
            value={selectedBrowser}
            supportedOptions={browsers}
            onChange={(browser) => setSelectedBrowser(browser as BrowserType)}
          />
          <p className="mt-1 text-xs text-gray-500">
            Auto-selected: {browsers.find(b => b.id === env.browser)?.name || 'Unknown Browser'}. 
            Change selection to view other browser options.
          </p>
        </div>

        {/* Installation Actions */}
        {selectedBrowserData?.status === 'supported' ? (
          <div className="space-y-4">
            <button
              onClick={handleInstallExtension}
              className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="w-4 h-4 mr-2" />
              Download Extension for {selectedBrowserData.name}
            </button>
            
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <h5 className="text-sm font-medium text-blue-900 mb-2">
                Installation Instructions for {selectedBrowserData.name}
              </h5>
              <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Click the download button above to open the extension store</li>
                <li>Click "Add to {selectedBrowserData.name}" or "Install"</li>
                <li>Accept the permissions when prompted</li>
                <li>Look for the Bodhi icon in your browser toolbar</li>
                <li>Click refresh in this modal to detect the extension</li>
              </ol>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
              <div className="flex items-start">
                <div className="ml-3">
                  <h5 className="text-sm font-medium text-amber-900">
                    {selectedBrowserData?.name || 'Selected Browser'} - Coming Soon
                  </h5>
                  <p className="mt-1 text-sm text-amber-800">
                    Support for {selectedBrowserData?.name || 'this browser'} is in development.
                  </p>
                  {selectedBrowserData?.github_issue_url && (
                    <button
                      onClick={handleViewGitHubIssue}
                      className="mt-2 inline-flex items-center text-sm text-amber-700 hover:text-amber-900"
                    >
                      Track Progress <ExternalLink className="w-3 h-3 ml-1" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Troubleshooting */}
        {extension.status !== 'ready' && (
          <div className="mt-6 p-3 bg-white border border-gray-200 rounded-md">
            <h5 className="text-sm font-medium text-gray-900 mb-2">
              Troubleshooting
            </h5>
            <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
              <li>Make sure you're using a supported browser version</li>
              <li>Check that the extension is enabled in your browser settings</li>
              <li>Try refreshing this page after installing the extension</li>
              <li>Restart your browser if the extension isn't detected</li>
            </ul>
          </div>
        )}

        {/* Success state */}
        {extension.status === 'ready' && (
          <div className="mt-6 flex items-center justify-center p-4 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center">
              <div className="text-green-600 mr-3">
                <ArrowRight className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-green-900">
                  Extension is ready! Proceeding to server setup.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}