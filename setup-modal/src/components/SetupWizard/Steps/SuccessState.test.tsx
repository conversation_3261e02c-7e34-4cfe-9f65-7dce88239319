import { SuccessState } from '@/components/SetupWizard/Steps/SuccessState';
import {
  createAllSystemsReadyState,
  createExtensionNotInstalledState,
  createMockState,
  createReadyExtensionState,
  createReadyServerState,
  createServerNotReadyState,
  createUnreachableExtensionState,
  createUnsupportedPlatformState,
} from '@/test/mock-factories';
import { render, screen, within } from '@/test/test-utils';
import userEvent from '@testing-library/user-event';

const mockSendAction = vi.fn();

describe('SuccessState - Completion Status Display', () => {
  beforeEach(() => {
    mockSendAction.mockClear();
  });

  describe('Overall Status Display', () => {
    const overallStatusCases = [
      {
        name: 'all systems ready shows success state',
        state: createAllSystemsReadyState(),
        expectedTitle: 'All Systems Ready!',
        expectedDescription: 'Your Bodhi Platform setup is complete and ready to use.',
        shouldShowContinueButton: true,
        expectedIcon: 'success', // Green checkmark
      },
      {
        name: 'incomplete setup shows in-progress state',
        state: createExtensionNotInstalledState(),
        expectedTitle: 'Setup In Progress',
        expectedDescription: 'Complete the remaining setup steps to get started.',
        shouldShowContinueButton: false,
        expectedIcon: 'warning', // Amber clock
      },
      {
        name: 'unsupported platform shows in-progress state',
        state: createUnsupportedPlatformState('firefox', 'linux'),
        expectedTitle: 'Setup In Progress',
        expectedDescription: 'Complete the remaining setup steps to get started.',
        shouldShowContinueButton: false,
        expectedIcon: 'warning',
      },
    ];

    test.each(overallStatusCases)(
      'should $name',
      ({ state, expectedTitle, expectedDescription, shouldShowContinueButton }) => {
        render(<SuccessState setupState={state} sendAction={mockSendAction} />);
        
        // Should show correct title and description
        expect(screen.getByTestId('success-state-title')).toHaveTextContent(expectedTitle);
        expect(screen.getByTestId('success-state-description')).toHaveTextContent(expectedDescription);
        
        // Should conditionally show continue button
        if (shouldShowContinueButton) {
          expect(screen.getByTestId('continue-button')).toBeInTheDocument();
        } else {
          expect(screen.queryByTestId('continue-button')).not.toBeInTheDocument();
        }
      }
    );
  });

  describe('Setup Status Summary', () => {
    test('should show platform compatibility status for supported platform', () => {
      const state = createMockState({
        env: { browser: 'chrome', os: 'macos' },
      });
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByTestId('status-summary-title')).toHaveTextContent('Setup Status Summary');
      expect(screen.getByTestId('platform-status-label')).toHaveTextContent('Platform Compatibility');
      expect(screen.getByTestId('platform-status-details')).toHaveTextContent('Chrome on macOS');
      
      // Should show Ready status for all components
      expect(screen.getByTestId('platform-status-text')).toHaveTextContent('Ready');
      expect(screen.getByTestId('extension-status-text')).toHaveTextContent('Ready');
      expect(screen.getByTestId('server-status-text')).toHaveTextContent('Ready');
    });

    test('should show platform compatibility status for unsupported platform', () => {
      const state = createUnsupportedPlatformState('firefox', 'macos');
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByTestId('platform-status-label')).toHaveTextContent('Platform Compatibility');
      expect(screen.getByTestId('platform-status-details')).toHaveTextContent('Firefox on macOS');
      
      // Should show Not Supported status for platform
      expect(screen.getByTestId('platform-status-text')).toHaveTextContent('Not Supported');
    });

    test('should show extension status for ready extension', () => {
      const state = createMockState({
        extension: createReadyExtensionState('2.1.0', 'ext-789'),
      });
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByTestId('extension-status-label')).toHaveTextContent('Browser Extension');
      expect(screen.getByTestId('extension-status-details')).toHaveTextContent('Version 2.1.0');
      
      // Should show Ready status for extension
      expect(screen.getByTestId('extension-status-text')).toHaveTextContent('Ready');
    });

    test('should show extension status for not installed extension', () => {
      const state = createExtensionNotInstalledState();
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByTestId('extension-status-label')).toHaveTextContent('Browser Extension');
      expect(screen.getByTestId('extension-status-details')).toHaveTextContent('Extension is not installed');
      
      // Should show Incomplete status for extension
      expect(screen.getByTestId('extension-status-text')).toHaveTextContent('Incomplete');
    });

    test('should show server status for ready server', () => {
      const state = createMockState({
        server: createReadyServerState('3.2.1'),
      });
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByTestId('server-status-label')).toHaveTextContent('Local Server');
      expect(screen.getByTestId('server-status-details')).toHaveTextContent('Version 3.2.1');
      
      // Should show Ready status for server
      expect(screen.getByTestId('server-status-text')).toHaveTextContent('Ready');
    });

    test('should show server status for unreachable server', () => {
      const state = createServerNotReadyState();
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByTestId('server-status-label')).toHaveTextContent('Local Server');
      expect(screen.getByTestId('server-status-details')).toHaveTextContent('Server connection refused');
      
      // Should show Incomplete status for server
      expect(screen.getByTestId('server-status-text')).toHaveTextContent('Incomplete');
    });
  });

  describe('Continue Button Interaction', () => {
    test('should call sendAction with MODAL_OUT_COMPLETE when continue button clicked', async () => {
      const user = userEvent.setup();
      const state = createAllSystemsReadyState();
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      const continueButton = screen.getByTestId('continue-button');
      await user.click(continueButton);
      
      expect(mockSendAction).toHaveBeenCalledWith('modal_out:complete');
    });

    test('should not show continue button when setup is incomplete', () => {
      const state = createMockState({
        extension: createUnreachableExtensionState(),
      });
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.queryByText('Continue to Webpage')).not.toBeInTheDocument();
    });
  });

  describe('Help Text for Incomplete Setup', () => {
    test('should show navigation help text when setup is incomplete', () => {
      const state = createExtensionNotInstalledState();
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByText('You can navigate to any step using the progress indicator above to complete the remaining setup.')).toBeInTheDocument();
    });

    test('should not show navigation help text when all systems are ready', () => {
      const state = createAllSystemsReadyState();
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.queryByText('You can navigate to any step using the progress indicator above to complete the remaining setup.')).not.toBeInTheDocument();
    });
  });

  describe('Status Icon Display', () => {
    test('should show green checkmark icon when all systems ready', () => {
      const state = createAllSystemsReadyState();
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      // The component should have a green background when all ready
      const iconContainer = screen.getByText('All Systems Ready!').closest('div')?.querySelector('.bg-green-100');
      expect(iconContainer).toBeInTheDocument();
    });

    test('should show amber clock icon when setup incomplete', () => {
      const state = createExtensionNotInstalledState();
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      // The component should have an amber background when incomplete
      const iconContainer = screen.getByText('Setup In Progress').closest('div')?.querySelector('.bg-amber-100');
      expect(iconContainer).toBeInTheDocument();
    });
  });

  describe('Platform Name Display', () => {
    test('should handle unknown browser and OS gracefully', () => {
      const state = createMockState({
        env: { browser: 'unknown', os: 'unknown' },
      });
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByText('Unknown Browser on Unknown OS')).toBeInTheDocument();
    });

    test('should display actual platform names from setupState', () => {
      const state = createMockState({
        env: { browser: 'edge', os: 'windows' },
      });
      
      render(<SuccessState setupState={state} sendAction={mockSendAction} />);
      
      expect(screen.getByText('Edge on Windows')).toBeInTheDocument();
    });
  });
});
