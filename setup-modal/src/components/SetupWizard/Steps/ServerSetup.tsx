import { PlatformDropdown } from '@/components/common/PlatformDropdown';
import { StatusBadge } from '@/components/SetupWizard/StatusBadge';
import { OSType, SetupState } from '@/lib/types';
import { ArrowRight, Download, ExternalLink } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ServerSetupProps {
  setupState: SetupState;
}

export function ServerSetup({
  setupState
}: ServerSetupProps) {
  const { server, env, os: osArray } = setupState;
  const [tempOSOverride, setTempOSOverride] = useState<OSType | null>(null);
  const selectedOS = tempOSOverride || env.os;
  useEffect(() => {
    setTempOSOverride(null);
  }, [env.os]);
  
  const setSelectedOS = (os: OSType) => {
    setTempOSOverride(os);
  };
  
  const getStatusInfo = () => {
    if (server.status === 'ready') {
      return {
        status: 'success' as const,
        title: 'Server Ready',
        message: `Server version ${server.version} is running and ready.`
      };
    }
    return {
      status: server.status === 'setup' || server.status === 'resource-admin' ? 'warning' as const : 'error' as const,
      title: server.error.message,
      message: `Error Code: ${server.error.code}`
    };
  };

  const statusInfo = getStatusInfo();
  const selectedOSData = osArray.find(o => o.id === selectedOS);
  
  const handleDownloadServer = () => {
    const targetOS = osArray.find(o => o.id === selectedOS && o.status === 'supported');
    if (targetOS && targetOS.status === 'supported') {
      window.open(targetOS.download_url, '_blank');
    }
  };

  const handleViewGitHubIssue = () => {
    const targetOS = osArray.find(o => o.id === selectedOS && o.status === 'not-supported');
    if (targetOS && targetOS.status === 'not-supported' && targetOS.github_issue_url) {
      window.open(targetOS.github_issue_url, '_blank');
    }
  };

  return (
    <div className="space-y-6 min-h-0">
      <div className="bg-gray-50 p-4 rounded-lg min-h-0">
        <div className="flex items-center mb-4">
          <div className="flex-1">
            <h4 className="text-base font-medium text-gray-900">
              {statusInfo.title}
            </h4>
            <p className="mt-1 text-sm text-gray-500">{statusInfo.message}</p>
          </div>
          <StatusBadge status={statusInfo.status} />
        </div>

        {/* OS selection dropdown */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Operating System (for server download)
          </label>
          <PlatformDropdown
            type="os"
            value={selectedOS}
            supportedOptions={osArray}
            onChange={(os) => setSelectedOS(os as OSType)}
          />
          <p className="mt-1 text-xs text-gray-500">
            Auto-selected: {osArray.find(o => o.id === env.os)?.name || 'Unknown OS'}. 
            Change selection to view other OS options.
          </p>
        </div>

        {/* Installation Actions */}
        {selectedOSData?.status === 'supported' ? (
          <div className="space-y-4">
            <button
              onClick={handleDownloadServer}
              className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="w-4 h-4 mr-2" />
              Download Bodhi App Server for {selectedOSData.name}
            </button>
            
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <h5 className="text-sm font-medium text-blue-900 mb-2">
                Installation Instructions for {selectedOSData.name}
              </h5>
              <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Click the download button above to get the installer</li>
                <li>Run the downloaded installer</li>
                <li>Follow the installation wizard</li>
                <li>Launch the Bodhi App Server</li>
                <li>Click refresh in this modal to detect the server</li>
              </ol>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
              <div className="flex items-start">
                <div className="ml-3">
                  <h5 className="text-sm font-medium text-amber-900">
                    {selectedOSData?.name || 'Selected OS'} - Coming Soon
                  </h5>
                  <p className="mt-1 text-sm text-amber-800">
                    Support for {selectedOSData?.name || 'this operating system'} is in development.
                  </p>
                  {selectedOSData?.github_issue_url && (
                    <button
                      onClick={handleViewGitHubIssue}
                      className="mt-2 inline-flex items-center text-sm text-amber-700 hover:text-amber-900"
                    >
                      Track Progress <ExternalLink className="w-3 h-3 ml-1" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* State-specific actions */}
        {server.status === 'setup' && (
          <div className="mt-4">
            <p className="text-sm text-gray-600 mb-3">
              Server needs initial configuration. Complete the setup process.
            </p>
          </div>
        )}

        {server.status === 'resource-admin' && (
          <div className="mt-4">
            <p className="text-sm text-gray-600 mb-3">
              Server requires admin approval. Complete the admin setup.
            </p>
          </div>
        )}

        {/* Troubleshooting */}
        {server.status !== 'ready' && (
          <div className="mt-6 p-3 bg-white border border-gray-200 rounded-md">
            <h5 className="text-sm font-medium text-gray-900 mb-2">
              Troubleshooting
            </h5>
            <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
              <li>Make sure the Bodhi App Server is installed and running</li>
              <li>Check that your firewall isn't blocking the server</li>
              <li>Verify the server is running on the expected port</li>
              <li>Try restarting the server application</li>
            </ul>
          </div>
        )}

        {/* Success state */}
        {server.status === 'ready' && (
          <div className="mt-6 flex items-center justify-center p-4 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center">
              <div className="text-green-600 mr-3">
                <ArrowRight className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-green-900">
                  Server is ready! Setup complete.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}