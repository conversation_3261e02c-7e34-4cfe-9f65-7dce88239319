import { MODAL_OUT_COMPLETE, SetupState } from '@/lib/types';
import { ArrowR<PERSON>, CheckCircle, Clock, XCircle } from 'lucide-react';

interface SuccessStateProps {
  setupState: SetupState;
  sendAction: (actionType: typeof MODAL_OUT_COMPLETE, data?: unknown) => void;
}

export function SuccessState({ setupState, sendAction }: SuccessStateProps) {
  const { env, extension, server, browsers, os } = setupState;
  
  // Find detected platform data from setupState
  const detectedBrowser = browsers.find(b => b.id === env.browser);
  const detectedOS = os.find(o => o.id === env.os);
  
  const isPlatformSupported = detectedBrowser?.status === 'supported';
  const isOSSupported = detectedOS?.status === 'supported';
  const isExtensionReady = extension.status === 'ready';
  const isServerReady = server.status === 'ready';
  
  const allReady = isPlatformSupported && isOSSupported && isExtensionReady && isServerReady;

  const getStatusIcon = (isComplete: boolean, isSupported = true) => {
    if (isComplete && isSupported) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    } else if (!isSupported) {
      return <Clock className="w-5 h-5 text-amber-500" />;
    } else {
      return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusText = (isComplete: boolean, isSupported = true) => {
    if (isComplete && isSupported) {
      return { text: 'Ready', className: 'text-green-700' };
    } else if (!isSupported) {
      return { text: 'Not Supported', className: 'text-amber-700' };
    } else {
      return { text: 'Incomplete', className: 'text-red-700' };
    }
  };

  const getBrowserName = () => {
    return detectedBrowser?.name || 'Unknown Browser';
  };

  const getOSName = () => {
    return detectedOS?.name || 'Unknown OS';
  };

  const handleContinue = () => {
    sendAction(MODAL_OUT_COMPLETE);
  };

  return (
    <div className="space-y-6" data-testid="success-state-container">
      <div className="text-center py-6" data-testid="success-state-header">
        <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
          allReady ? 'bg-green-100' : 'bg-amber-100'
        }`} data-testid="success-state-icon">
          {allReady ? (
            <CheckCircle className="w-8 h-8 text-green-600" />
          ) : (
            <Clock className="w-8 h-8 text-amber-600" />
          )}
        </div>
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2" data-testid="success-state-title">
          {allReady ? 'All Systems Ready!' : 'Setup In Progress'}
        </h3>
        
        <p className="text-gray-600 mb-6" data-testid="success-state-description">
          {allReady 
            ? 'Your Bodhi Platform setup is complete and ready to use.'
            : 'Complete the remaining setup steps to get started.'
          }
        </p>

        {allReady && (
          <button
            onClick={handleContinue}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            data-testid="continue-button"
          >
            Continue to Webpage
            <ArrowRight className="ml-2 w-5 h-5" />
          </button>
        )}
      </div>

      {/* Setup Status Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-4" data-testid="status-summary-section">
        <h4 className="text-sm font-medium text-gray-900 mb-4" data-testid="status-summary-title">Setup Status Summary</h4>
        
        <div className="space-y-3">
          {/* Platform Compatibility */}
          <div className="flex items-center justify-between py-2 border-b border-gray-100" data-testid="platform-status-row">
            <div className="flex items-center">
              {getStatusIcon(isPlatformSupported && isOSSupported, isPlatformSupported && isOSSupported)}
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900" data-testid="platform-status-label">Platform Compatibility</p>
                <p className="text-xs text-gray-500" data-testid="platform-status-details">
                  {getBrowserName()} on {getOSName()}
                </p>
              </div>
            </div>
            <span className={`text-xs font-medium ${getStatusText(isPlatformSupported && isOSSupported, isPlatformSupported && isOSSupported).className}`} data-testid="platform-status-text">
              {getStatusText(isPlatformSupported && isOSSupported, isPlatformSupported && isOSSupported).text}
            </span>
          </div>

          {/* Browser Extension */}
          <div className="flex items-center justify-between py-2 border-b border-gray-100" data-testid="extension-status-row">
            <div className="flex items-center">
              {getStatusIcon(isExtensionReady)}
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900" data-testid="extension-status-label">Browser Extension</p>
                <p className="text-xs text-gray-500" data-testid="extension-status-details">
                  {isExtensionReady 
                    ? `Version ${extension.version}` 
                    : extension.error.message
                  }
                </p>
              </div>
            </div>
            <span className={`text-xs font-medium ${getStatusText(isExtensionReady).className}`} data-testid="extension-status-text">
              {getStatusText(isExtensionReady).text}
            </span>
          </div>

          {/* Local Server */}
          <div className="flex items-center justify-between py-2" data-testid="server-status-row">
            <div className="flex items-center">
              {getStatusIcon(isServerReady)}
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900" data-testid="server-status-label">Local Server</p>
                <p className="text-xs text-gray-500" data-testid="server-status-details">
                  {isServerReady 
                    ? `Version ${server.version}` 
                    : server.error.message
                  }
                </p>
              </div>
            </div>
            <span className={`text-xs font-medium ${getStatusText(isServerReady).className}`} data-testid="server-status-text">
              {getStatusText(isServerReady).text}
            </span>
          </div>
        </div>
      </div>

      {/* Help text for incomplete setup */}
      {!allReady && (
        <div className="text-center bg-gray-50 rounded-lg p-4" data-testid="help-text-section">
          <p className="text-sm text-gray-600 mb-2" data-testid="help-text">
            You can navigate to any step using the progress indicator above to complete the remaining setup.
          </p>
        </div>
      )}
    </div>
  );
}