import { ExtensionSetup } from '@/components/SetupWizard/Steps/ExtensionSetup';
import {
  createMockState,
  createNotInstalledExtensionState,
  createReadyExtensionState,
  createUnreachableExtensionState,
  createUnsupportedExtensionState,
} from '@/test/mock-factories';
import { render, screen } from '@/test/test-utils';
import userEvent from '@testing-library/user-event';

describe('ExtensionSetup - Dropdown Pre-selection', () => {
  describe('Initial Dropdown Selection', () => {
    const initialSelectionCases = [
      {
        name: 'pre-selects Chrome when env.browser is chrome',
        state: createMockState({
          env: { browser: 'chrome', os: 'macos' },
          extension: createNotInstalledExtensionState(),
        }),
        expectedSelection: 'Chrome',
        expectedBrowserId: 'chrome',
      },
      {
        name: 'pre-selects Edge when env.browser is edge',
        state: createMockState({
          env: { browser: 'edge', os: 'windows' },
          extension: createNotInstalledExtensionState(),
        }),
        expectedSelection: 'Edge',
        expectedBrowserId: 'edge',
      },
      {
        name: 'pre-selects Firefox when env.browser is firefox (unsupported)',
        state: createMockState({
          env: { browser: 'firefox', os: 'macos' },
          extension: createNotInstalledExtensionState(),
        }),
        expectedSelection: 'Firefox',
        expectedBrowserId: 'firefox',
      },
      {
        name: 'pre-selects Safari when env.browser is safari (unsupported)',
        state: createMockState({
          env: { browser: 'safari', os: 'macos' },
          extension: createNotInstalledExtensionState(),
        }),
        expectedSelection: 'Safari',
        expectedBrowserId: 'safari',
      },
      {
        name: 'pre-selects Unknown Browser when env.browser is unknown',
        state: createMockState({
          env: { browser: 'unknown', os: 'macos' },
          extension: createNotInstalledExtensionState(),
        }),
        expectedSelection: 'Unknown Browser',
        expectedBrowserId: 'unknown',
      },
    ];

    test.each(initialSelectionCases)(
      'should $name',
      ({ state, expectedSelection }) => {
        render(<ExtensionSetup setupState={state} />);
        
        // Should show dropdown with correct selection - look for the dropdown container
        const dropdownContainer = screen.getByText(/Select Browser \(for extension store link\)/);
        expect(dropdownContainer).toBeInTheDocument();
        
        // The dropdown button should show the expected selection
        const dropdownButton = screen.getByText(expectedSelection).closest('button')!;
        expect(dropdownButton).toHaveTextContent(expectedSelection);
        
        // Should show auto-selected message mentioning detected browser
        const autoSelectedText = screen.getByText(/Auto-selected:/);
        expect(autoSelectedText).toHaveTextContent(expectedSelection);
      }
    );
  });

  describe('Extension Status Display', () => {
    const extensionStatusCases = [
      {
        name: 'shows success status for ready extension',
        extension: createReadyExtensionState('2.0.1', 'ext-456'),
        expectedTitle: 'Extension Ready',
        expectedMessage: 'Extension version 2.0.1 is connected and ready.',
      },
      {
        name: 'shows error status for not-installed extension',
        extension: createNotInstalledExtensionState('Extension is not installed'),
        expectedTitle: 'Extension is not installed',
        expectedMessage: 'Error Code: ext-not-installed',
      },
      {
        name: 'shows error status for unreachable extension',
        extension: createUnreachableExtensionState('Could not connect to extension'),
        expectedTitle: 'Could not connect to extension',
        expectedMessage: 'Error Code: ext-connection-failed',
      },
      {
        name: 'shows warning status for unsupported extension',
        extension: createUnsupportedExtensionState('Extension version not supported'),
        expectedTitle: 'Extension version not supported',
        expectedMessage: 'Error Code: ext-unsupported-version',
      },
    ];

    test.each(extensionStatusCases)(
      'should $name',
      ({ extension, expectedTitle, expectedMessage }) => {
        const state = createMockState({ extension });
        render(<ExtensionSetup setupState={state} />);
        
        expect(screen.getByText(expectedTitle)).toBeInTheDocument();
        expect(screen.getByText(expectedMessage)).toBeInTheDocument();
      }
    );
  });

  describe('Dropdown State Reset on env.browser Change', () => {
    test('should reset dropdown selection when env.browser changes', () => {
      const initialState = createMockState({
        env: { browser: 'chrome', os: 'macos' },
        extension: createNotInstalledExtensionState(),
      });
      
      const { rerender } = render(<ExtensionSetup setupState={initialState} />);
      
      // Initial selection should be Chrome
      const dropdownButton = screen.getByText('Chrome').closest('button')!;
      expect(dropdownButton).toHaveTextContent('Chrome');
      
      // Simulate new state with different detected browser
      const newState = createMockState({
        env: { browser: 'firefox', os: 'macos' },
        extension: createNotInstalledExtensionState(),
      });
      
      rerender(<ExtensionSetup setupState={newState} />);
      
      // Should reset to Firefox (ignoring any user's previous override)
      const updatedDropdownButton = screen.getByText('Firefox').closest('button')!;
      expect(updatedDropdownButton).toHaveTextContent('Firefox');
      expect(screen.getByText(/Auto-selected: Firefox/)).toBeInTheDocument();
    });
  });

  describe('Temporary Override Behavior', () => {
    test('should allow temporary browser override while staying on same step', async () => {
      const user = userEvent.setup();
      const state = createMockState({
        env: { browser: 'chrome', os: 'macos' },
        extension: createNotInstalledExtensionState(),
      });
      
      render(<ExtensionSetup setupState={state} />);
      
      // Should start with Chrome selected
      let dropdownButton = screen.getByText('Chrome').closest('button')!;
      expect(dropdownButton).toHaveTextContent('Chrome');
      
      // Open dropdown
      await user.click(dropdownButton);
      
      // Click on Firefox option
      const firefoxOption = screen.getByText('Firefox');
      await user.click(firefoxOption);
      
      // Should now show Firefox as selected
      dropdownButton = screen.getByText('Firefox').closest('button')!;
      expect(dropdownButton).toHaveTextContent('Firefox');
      
      // Auto-selected message should still show original Chrome
      expect(screen.getByText(/Auto-selected: Chrome/)).toBeInTheDocument();
    });

    test('should maintain override selection within same state', async () => {
      const user = userEvent.setup();
      const state = createMockState({
        env: { browser: 'chrome', os: 'macos' },
        extension: createNotInstalledExtensionState(),
      });
      
      const { rerender } = render(<ExtensionSetup setupState={state} />);
      
      // Change selection to Firefox
      let dropdownButton = screen.getByText('Chrome').closest('button')!;
      await user.click(dropdownButton);
      await user.click(screen.getByText('Firefox'));
      
      // Re-render with same state (no env.browser change)
      rerender(<ExtensionSetup setupState={state} />);
      
      // Should maintain Firefox selection
      dropdownButton = screen.getByText('Firefox').closest('button')!;
      expect(dropdownButton).toHaveTextContent('Firefox');
    });
  });

  describe('Extension Store Links', () => {
    test('should show correct extension store link for supported browsers', async () => {
      const user = userEvent.setup();
      const state = createMockState({
        env: { browser: 'chrome', os: 'macos' },
        extension: createNotInstalledExtensionState(),
      });
      
      render(<ExtensionSetup setupState={state} />);
      
      // Should show Chrome extension store link
      const installButton = screen.getByText('Download Extension for Chrome');
      expect(installButton).toBeInTheDocument();
      
      // Change to Edge
      const dropdownButton = screen.getByText('Chrome').closest('button')!;
      await user.click(dropdownButton);
      await user.click(screen.getByText('Edge'));
      
      // Should show Edge extension store link
      expect(screen.getByText('Download Extension for Edge')).toBeInTheDocument();
    });

    test('should show GitHub issue link for unsupported browsers', () => {
      const state = createMockState({
        env: { browser: 'firefox', os: 'macos' },
        extension: createNotInstalledExtensionState(),
      });
      
      render(<ExtensionSetup setupState={state} />);
      
      // Should show GitHub issue link for Firefox
      expect(screen.getByText('Track Progress')).toBeInTheDocument();
    });
  });

  describe('Integration with Status Badge', () => {
    test('should show refresh capability when extension is not ready', () => {
      const state = createMockState({
        extension: createUnreachableExtensionState(),
      });
      
      render(<ExtensionSetup setupState={state} />);
      
      // Status badge should be present (exact implementation depends on StatusBadge component)
      expect(screen.getByText('Could not connect to extension')).toBeInTheDocument();
    });
  });
});