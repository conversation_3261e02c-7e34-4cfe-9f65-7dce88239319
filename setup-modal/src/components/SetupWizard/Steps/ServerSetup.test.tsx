import { ServerSetup } from '@/components/SetupWizard/Steps/ServerSetup';
import {
  createMockState,
  createPendingServerState,
  createReadyServerState,
  createResourceAdminServerState,
  createSetupServerState,
  createUnreachableServerState
} from '@/test/mock-factories';
import { render, screen } from '@/test/test-utils';
import userEvent from '@testing-library/user-event';
import { describe, expect, test } from 'vitest';

describe('ServerSetup - Dropdown Pre-selection', () => {
  describe('Initial OS Dropdown Selection', () => {
    const initialOSSelectionCases = [
      {
        name: 'pre-selects macOS when env.os is macos',
        state: createMockState({
          env: { browser: 'chrome', os: 'macos' },
          server: createUnreachableServerState(),
        }),
        expectedSelection: 'macOS',
        expectedOSId: 'macos',
      },
      {
        name: 'pre-selects Windows when env.os is windows',
        state: createMockState({
          env: { browser: 'chrome', os: 'windows' },
          server: createUnreachableServerState(),
        }),
        expectedSelection: 'Windows',
        expectedOSId: 'windows',
      },
      {
        name: 'pre-selects Linux when env.os is linux (unsupported)',
        state: createMockState({
          env: { browser: 'chrome', os: 'linux' },
          server: createUnreachableServerState(),
        }),
        expectedSelection: 'Linux',
        expectedOSId: 'linux',
      },
      {
        name: 'pre-selects Unknown OS when env.os is unknown',
        state: createMockState({
          env: { browser: 'chrome', os: 'unknown' },
          server: createUnreachableServerState(),
        }),
        expectedSelection: 'Unknown OS',
        expectedOSId: 'unknown',
      },
    ];

    test.each(initialOSSelectionCases)(
      'should $name',
      ({ state, expectedSelection }) => {
        render(<ServerSetup setupState={state} />);
        
        // Should show dropdown with correct selection - look for dropdown in OS selection area
        const dropdownContainer = screen.getByText(/Select Operating System \(for server download\)/);
        expect(dropdownContainer).toBeInTheDocument();
        
        // The dropdown button should show the expected selection
        const dropdownButton = screen.getByText(expectedSelection).closest('button')!;
        expect(dropdownButton).toHaveTextContent(expectedSelection);
        
        // Should show auto-selected message mentioning detected OS
        const autoSelectedText = screen.getByText(/Auto-selected:/);
        expect(autoSelectedText).toHaveTextContent(expectedSelection);
      }
    );
  });

  describe('Server Status Display', () => {
    const serverStatusCases = [
      {
        name: 'shows success status for ready server',
        server: createReadyServerState('3.1.0'),
        expectedTitle: 'Server Ready',
        expectedMessage: 'Server version 3.1.0 is running and ready.',
      },
      {
        name: 'shows error status for unreachable server',
        server: createUnreachableServerState('Server connection refused'),
        expectedTitle: 'Server connection refused',
        expectedMessage: 'Error Code: server-conn-refused',
      },
      {
        name: 'shows warning status for server in setup mode',
        server: createSetupServerState('Server requires initial setup', '3.0.0'),
        expectedTitle: 'Server requires initial setup',
        expectedMessage: 'Error Code: server-in-setup-status',
      },
      {
        name: 'shows warning status for server in resource-admin mode',
        server: createResourceAdminServerState('Server in resource admin mode', '3.0.0'),
        expectedTitle: 'Server in resource admin mode',
        expectedMessage: 'Error Code: server-in-admin-status',
      },
      {
        name: 'shows error status for pending server',
        server: createPendingServerState('Server pending extension ready'),
        expectedTitle: 'Server pending extension ready',
        expectedMessage: 'Error Code: server-pending-ext-ready',
      },
    ];

    test.each(serverStatusCases)(
      'should $name',
      ({ server, expectedTitle, expectedMessage }) => {
        const state = createMockState({ server });
        render(<ServerSetup setupState={state} />);
        
        expect(screen.getByText(expectedTitle)).toBeInTheDocument();
        expect(screen.getByText(expectedMessage)).toBeInTheDocument();
      }
    );
  });

  describe('Dropdown State Reset on env.os Change', () => {
    test('should reset OS dropdown selection when env.os changes', () => {
      const initialState = createMockState({
        env: { browser: 'chrome', os: 'macos' },
        server: createUnreachableServerState(),
      });
      
      const { rerender } = render(<ServerSetup setupState={initialState} />);
      
      // Initial selection should be macOS
      let dropdownButton = screen.getByText('macOS').closest('button')!;
      expect(dropdownButton).toHaveTextContent('macOS');
      
      // Simulate new state with different detected OS
      const newState = createMockState({
        env: { browser: 'chrome', os: 'windows' },
        server: createUnreachableServerState(),
      });
      
      rerender(<ServerSetup setupState={newState} />);
      
      // Should reset to Windows (ignoring any user's previous override)
      dropdownButton = screen.getByText('Windows').closest('button')!;
      expect(dropdownButton).toHaveTextContent('Windows');
      expect(screen.getByText(/Auto-selected: Windows/)).toBeInTheDocument();
    });
  });

  describe('Temporary OS Override Behavior', () => {
    test('should allow temporary OS override while staying on same step', async () => {
      const user = userEvent.setup();
      const state = createMockState({
        env: { browser: 'chrome', os: 'macos' },
        server: createUnreachableServerState(),
      });
      
      render(<ServerSetup setupState={state} />);
      
      // Should start with macOS selected
      let dropdownButton = screen.getByText('macOS').closest('button')!;
      expect(dropdownButton).toHaveTextContent('macOS');
      
      // Open dropdown
      await user.click(dropdownButton);
      
      // Click on Windows option
      const windowsOption = screen.getByText('Windows');
      await user.click(windowsOption);
      
      // Should now show Windows as selected
      dropdownButton = screen.getByText('Windows').closest('button')!;
      expect(dropdownButton).toHaveTextContent('Windows');
      
      // Auto-selected message should still show original macOS
      expect(screen.getByText(/Auto-selected: macOS/)).toBeInTheDocument();
    });

    test('should maintain override selection within same state', async () => {
      const user = userEvent.setup();
      const state = createMockState({
        env: { browser: 'chrome', os: 'macos' },
        server: createUnreachableServerState(),
      });
      
      const { rerender } = render(<ServerSetup setupState={state} />);
      
      // Change selection to Linux
      let dropdownButton = screen.getByText('macOS').closest('button')!;
      await user.click(dropdownButton);
      await user.click(screen.getByText('Linux'));
      
      // Re-render with same state (no env.os change)
      rerender(<ServerSetup setupState={state} />);
      
      // Should maintain Linux selection
      dropdownButton = screen.getByText('Linux').closest('button')!;
      expect(dropdownButton).toHaveTextContent('Linux');
    });
  });

  describe('Server Download Links', () => {
    test('should show correct download link for supported OS', async () => {
      const user = userEvent.setup();
      const state = createMockState({
        env: { browser: 'chrome', os: 'macos' },
        server: createUnreachableServerState(),
      });
      
      render(<ServerSetup setupState={state} />);
      
      // Should show macOS download link
      const downloadButton = screen.getByText('Download Bodhi App Server for macOS');
      expect(downloadButton).toBeInTheDocument();
      
      // Change to Windows
      const dropdownButton = screen.getByText('macOS').closest('button')!;
      await user.click(dropdownButton);
      await user.click(screen.getByText('Windows'));
      
      // Should show Windows download link
      expect(screen.getByText('Download Bodhi App Server for Windows')).toBeInTheDocument();
    });

    test('should show GitHub issue link for unsupported OS', () => {
      const state = createMockState({
        env: { browser: 'chrome', os: 'linux' },
        server: createUnreachableServerState(),
      });
      
      render(<ServerSetup setupState={state} />);
      
      // Should show GitHub issue link for Linux
      expect(screen.getByText('Track Progress')).toBeInTheDocument();
    });
  });

  describe('Server State Specific UI', () => {
    test('should show different UI for setup vs resource-admin server states', () => {
      // Test setup state
      const setupState = createMockState({
        server: createSetupServerState(),
      });
      
      const { rerender } = render(<ServerSetup setupState={setupState} />);
      expect(screen.getByText('Server requires initial setup')).toBeInTheDocument();
      
      // Test resource-admin state  
      const adminState = createMockState({
        server: createResourceAdminServerState(),
      });
      
      rerender(<ServerSetup setupState={adminState} />);
      expect(screen.getByText('Server in resource admin mode')).toBeInTheDocument();
    });
  });

  describe('Integration with Status Badge', () => {
    test('should show appropriate status for different server states', () => {
      const state = createMockState({
        server: createSetupServerState(),
      });
      
      render(<ServerSetup setupState={state} />);
      
      // Status badge should be present with warning/error state
      expect(screen.getByText('Server requires initial setup')).toBeInTheDocument();
    });
  });
});