import { useSetupModal } from '@/hooks/useSetupModal';
import { createMockState } from '@/test/mock-factories';
import { act, renderHook } from '@testing-library/react';

// Mock postMessage
const mockPostMessage = vi.fn();
Object.defineProperty(window, 'parent', {
  value: { postMessage: mockPostMessage },
  writable: true,
});

describe('useSetupModal', () => {
  beforeEach(() => {
    mockPostMessage.mockClear();
  });

  test('should initialize with null setupState', () => {
    const { result } = renderHook(() => useSetupModal());
    
    expect(result.current.setupState).toBe(null);
    expect(typeof result.current.sendAction).toBe('function');
  });

  test('should send ready message on mount', () => {
    renderHook(() => useSetupModal());
    
    expect(mockPostMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'modal_out:ready',
        requestId: expect.any(String),
      }),
      '*'
    );
  });

  test('should update setupState when receiving modal:state message', () => {
    const { result } = renderHook(() => useSetupModal());
    const mockState = createMockState();

    act(() => {
      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'modal:state',
          data: mockState,
        },
      });
      window.dispatchEvent(messageEvent);
    });

    expect(result.current.setupState).toEqual(mockState);
  });

  test('should ignore non-modal:state messages', () => {
    const { result } = renderHook(() => useSetupModal());

    act(() => {
      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'other:message',
          data: createMockState(),
        },
      });
      window.dispatchEvent(messageEvent);
    });

    expect(result.current.setupState).toBe(null);
  });

  test('should send action messages via sendAction', () => {
    const { result } = renderHook(() => useSetupModal());

    act(() => {
      result.current.sendAction('modal_out:refresh', { test: 'data' });
    });

    expect(mockPostMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'modal_out:refresh',
        data: { test: 'data' },
        requestId: expect.any(String),
      }),
      '*'
    );
  });
});