import { useEffect, useState } from 'react';
import { MODAL_MESSAGE_STATE, ModalOutMessageType, SetupStateOrNull } from '@/lib/types';

export function useSetupModal() {
  const [setupState, setSetupState] = useState<SetupStateOrNull>(null);

  // Handle messages from parent window
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === MODAL_MESSAGE_STATE) {
        if (import.meta.env.DEV) {
          console.log('[useSetupModal] Received state:', event.data.data);
        }
        setSetupState(event.data.data);
      }
    };

    window.addEventListener('message', handleMessage);
    
    // Send ready message to parent
    window.parent.postMessage({
      type: 'modal_out:ready',
      requestId: Date.now().toString()
    }, '*');

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // Function to send actions to parent
  const sendAction = (actionType: ModalOutMessageType, data?: unknown) => {
    window.parent.postMessage({
      type: actionType,
      data,
      requestId: Date.now().toString()
    }, '*');
  };

  return { setupState, sendAction };
}