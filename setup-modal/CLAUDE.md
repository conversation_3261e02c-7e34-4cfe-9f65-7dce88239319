# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm install` - Install dependencies
- `npm run dev` - Start development server with Vite
- `npm run build` - Build for production
- `npm run build:fast` - Fast build using custom build script
- `npm run lint` - Run ESLint for code quality
- `npm run preview` - Preview production build locally

### Testing Commands (test-iframe-srcdoc directory)

- `cd test-iframe-srcdoc && npm run test` - Run Vitest tests
- `cd test-iframe-srcdoc && npm run test:watch` - Run tests in watch mode
- `cd test-iframe-srcdoc && npm run test:ui` - Run tests with Vitest UI
- `cd test-iframe-srcdoc && npm run dev` - Start test app development server

## Architecture Overview

This is a React-based setup wizard modal for the Bodhi Platform, built with Vite, TypeScript, and Tailwind CSS. The application is designed to be embedded as an iframe component.

### Core Architecture

**State Management**: The application uses a centralized state pattern with `BodhiPlatformState` interface defined in `src/App.tsx`. State includes:
- `extension`: Extension installation and connection status
- `server`: Server setup and connection status  
- `env`: Environment detection (OS/browser)

**Communication**: Uses `postMessage` API for parent-iframe communication:
- Sends `ready` message on mount
- Listens for `state` messages to update platform state
- Sends `action` messages to parent with user interactions

**Component Structure**:
- `App.tsx` - Root component managing state and iframe communication
- `SetupWizard/` - Main wizard component with step navigation
- `Steps/` - Individual setup step components (Platform Check, Extension Setup, Server Setup, Success)
- `SimulationPanel.tsx` - Development-only state simulation panel

### Step Flow Logic

The wizard automatically advances through steps based on state:
1. **Platform Check** - Validates browser (Chrome/Edge) and OS (macOS) support
2. **Extension Setup** - Handles browser extension installation and connection
3. **Server Setup** - Manages server connection and authentication
4. **Success State** - Completion confirmation

Step navigation is controlled in `SetupWizard.tsx:40-65` with conditional access based on current state.

### Key Design Patterns

- **Type Safety**: Comprehensive TypeScript interfaces for all state objects
- **Conditional UI**: Components adapt based on platform capabilities and current state
- **Error Handling**: Structured error objects with retryable flags
- **Responsive Design**: Tailwind CSS with custom theme extensions

### Styling System

Uses Tailwind CSS with custom configuration in `tailwind.config.js` including:
- Custom color palette with CSS variable integration
- Extended animations and keyframes
- Container and responsive breakpoint customization

### Development Utilities

- `src/lib/utils.ts` - Contains `cn()` utility for conditional class merging using `clsx` and `tailwind-merge`
- Vite configuration with path aliases (`@` maps to `src/`)
- ESLint setup for React and TypeScript development
- `vite-plugin-singlefile` - Inlines all assets for single-file distribution

### Testing Architecture

**Framework**: Uses Vitest for unit/integration tests and Playwright for browser automation testing.

**Test Structure**: 
- Tests located in `test-iframe-srcdoc/tests/` directory
- `setup.ts` - Global test configuration and setup
- Browser testing with real browser instances and extension loading
- Focuses on iframe communication and integration scenarios

**Test Standards**:
- Deterministic tests with no conditional logic in test bodies
- Single flow per test case without reuse or alternative paths
- Setup in beforeAll/afterAll for costly operations like servers
- Setup in beforeEach for lightweight mocks
- Blackbox testing approach - test user-accessible features only

### Build Configuration

- **Single File Output**: Uses `vite-plugin-singlefile` to create self-contained HTML files
- **Asset Inlining**: All assets are inlined with limit set to 100MB
- **Target**: ESNext for modern browser compatibility
- **Fast Build**: Custom `build-fast.mjs` script for optimized development builds
- do not update @ai-docs/context/setup-modal.md, @ai-docs/context/setup-modal-test.md as a changelog document, but check the as is state, and update the document to have the as is state, and no changelog type update