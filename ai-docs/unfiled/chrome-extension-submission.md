# Chrome Web Store Submission: Questions and Answers

This document contains the questions from the Chrome Web Store submission form and their corresponding answers based on the Bodhi Browser Extension documentation.

## Single Purpose

**Question**: Single purpose description

**Answer**:

We have following components in the Bodhi Platform:
- Bodhi App Server: OAuth 2.1 secured resource servers capable of running Open Source LLMs 
- Bodhi Browser Extension: Companion extension to communicate with Bodhi App Server

The Bodhi Browser Extension, is a companion extension specifically designed for communicating with Bodhi App servers that are running on users device. Bodhi App servers are OAuth 2.1 resource servers that are capable of running AI/LLM inference using Open Source LLMs. Thus by allowing access to user deployed AI servers, Bodhi Browser Chrome extension facilitates access to local AI capabilities to web pages while ensuring complete security and user control.

Bodhi App Servers use OAuth 2.1 for all important API endpoints (endpoints like /ping are public for health/availability check). Any web page that requires access to Bodhi App APIs via the extension should also be a client on the Auth Server and receive access via consent screen to user configured Bodhi App server. This access token is then forwarded with every request to Bodhi App server which authenticates the request, and only allows access based on the consented privileges.

Thus Bodhi App, along with its companion extension, provides Web AI capabilities to web pages, enabling users to utilize their own computational resources and AI models for enhanced web experiences while maintaining complete security.


## Permission Justification

**Question**: Permission justification

**Answer**:
The extension requires minimal permissions for its core functionality:

- **storage**: Required to store backend server configuration (default: localhost:1135) and user settings. This allows users to configure which local AI server the extension should connect to.

- **host_permissions** (`<all_urls>`): Required to inject content scripts into web pages that want to use local AI capabilities. The extension needs to inject the `window.bodhiext` API into web pages so they can communicate with the extension. This is essential for the bridge functionality between web pages and local AI servers.

The extension does NOT access or store any user data, personal information, or web page content. It only acts as a secure proxy for API requests between web pages and local AI servers.

**Question**: Host permission justification

**Answer**:
The `<all_urls>` host permission is required because:

1. **Content Script Injection**: The extension must inject content scripts into any web page that wants to use local AI capabilities. Web developers can integrate the bodhijs library into any website, so the extension needs to be able to inject the `window.bodhiext` API into any domain.

2. **Secure Bridge Functionality**: The extension acts as a secure bridge between web pages and local AI servers. It cannot predict which websites will integrate AI features, so it must be able to inject its communication interface into any website that includes the bodhijs library.

3. **No Data Collection**: Despite having broad host permissions, the extension does NOT collect, store, or transmit any user data, browsing history, or web page content. It only forwards API requests from web pages to local AI servers and returns responses.

4. **Local Communication Only**: All API requests are forwarded to local servers (typically localhost:1135), not to remote servers, ensuring user privacy.

## Remote Code Usage

**Question**: Are you using remote code?

**Answer**: No, I am not using remote code

**Justification**: The extension does not include any remote code, external scripts, or dynamically loaded JavaScript. All code is bundled within the extension package. The extension only communicates with local AI servers running on the user's device (typically localhost:1135) and does not fetch or execute any remote JavaScript code.

## Data Usage

**Question**: What user data do you plan to collect from users now or in the future?

**Answers**:
- ☐ Personally identifiable information - **NOT COLLECTED**
- ☐ Health information - **NOT COLLECTED**  
- ☐ Financial and payment information - **NOT COLLECTED**
- ☐ Authentication information - **NOT COLLECTED**
- ☐ Personal communications - **NOT COLLECTED**
- ☐ Location - **NOT COLLECTED**
- ☐ Web history - **NOT COLLECTED**
- ☐ User activity - **NOT COLLECTED**
- ☐ Website content - **NOT COLLECTED**

**Data Collection Statement**: The Bodhi Browser Extension does not collect, store, or transmit any user data. It functions purely as a local proxy between web pages and local AI servers running on the user's device. No personal information, browsing data, or web page content is accessed, stored, or transmitted by the extension.

## Data Usage Certifications

**Question**: I certify that the following disclosures are true:

**Answers**:
- ☑ I do not sell or transfer user data to third parties, apart from the approved use cases
- ☑ I do not use or transfer user data for purposes that are unrelated to my item's single purpose  
- ☑ I do not use or transfer user data to determine creditworthiness or for lending purposes

**Justification**: Since the extension does not collect any user data, these certifications are automatically satisfied. The extension only forwards API requests between web pages and local AI servers without accessing, storing, or transmitting any user data.

## Privacy Policy

**Question**: Privacy policy URL

**Answer**: [To be provided - URL to privacy policy]

**Privacy Policy Content Summary**: The privacy policy should state that:
- The extension does not collect any user data
- All communication is between web pages and local AI servers on the user's device
- No data is transmitted to remote servers
- The extension only stores local configuration settings (backend server URL)
- No tracking, analytics, or data collection occurs

## Technical Implementation Details

### Architecture Overview
The extension consists of three main components:
1. **Background Script** (`background.js`): Handles API requests to local AI servers
2. **Content Script** (`content.js`): Injected into web pages to relay messages
3. **Inject Script** (`inject.js`): Creates the `window.bodhiext` API for web pages

### Communication Flow
```
Web Page → window.bodhiext → window.postMessage → content.js → 
chrome.runtime.sendMessage → background.js → fetch(localhost:1135) → 
Local AI Server
```

### Security Features
- Origin verification for all messages
- Content Security Policy enforcement
- Minimal permissions (only storage and host permissions)
- No remote code execution
- Secure ID generation using crypto.randomUUID()
- Request timeouts (30 seconds for API, 60 seconds for streaming)

### Use Cases
1. **Enhanced Content Understanding**: Web pages can send content to local AI for summarization or analysis
2. **Privacy-Preserving AI**: Users benefit from AI without sending data to remote servers
3. **Cost-Free AI Features**: Websites can offer AI features without cloud API costs
4. **Personalized AI Experiences**: Users interact with their preferred local AI models

## Compliance and Security

### Browser Security Compliance
- Follows Chrome Extension Manifest V3 specifications
- Implements proper Content Security Policy
- Uses secure message passing between components
- Validates all message origins and formats

### Privacy Compliance
- No data collection or storage of user information
- All processing happens locally on user's device
- No tracking or analytics implemented
- Transparent about minimal permissions required

### Authentication Support
The extension supports secure authentication methods:
- API token authentication (stored in chrome.storage)
- OAuth2 PKCE flow for web applications
- OAuth2 confidential client flow for backend applications
- Extension-to-extension communication for other Chrome extensions

## Testing and Quality Assurance

The extension includes comprehensive testing:
- Unit tests for all components
- Integration tests with real AI servers
- Browser automation tests using Playwright
- Error handling and edge case testing
- Cross-browser compatibility testing (Chrome, Edge, Brave)

## Open Source and Community

- Project is open source and available on GitHub
- Comprehensive documentation for developers
- Active development and maintenance
- Community-driven feature requests and bug reports
- Follows web standards and best practices

## Conclusion

The Bodhi Browser Extension provides a secure, privacy-focused solution for integrating local AI capabilities into web applications. It requires minimal permissions, collects no user data, and operates entirely locally on the user's device. The extension enables a new paradigm of privacy-preserving AI experiences while maintaining the highest security standards.