# Research: Embedded UI Onboarding Patterns for Missing Dependencies

## Executive Summary

This research analyzes successful patterns from integration libraries that provide embedded UI components for handling missing dependencies and onboarding flows. The goal is to inform the design of bodhi-js library's gradual onboarding features to provide smooth user experiences when required components (browser extension or Bodhi app) are not available.

## Key Findings

### 1. Framework-Agnostic Embedded UI is the Gold Standard

The most successful libraries provide self-contained HTML+CSS+JavaScript components that don't interfere with host applications:

**Stripe Elements Pattern:**

- Self-contained iframe-based components
- CSS-level customization via Appearance API
- Framework-agnostic but with React/Vue/Angular wrappers
- No external dependencies beyond the core library

**Key Insight:** Framework-agnostic components with optional framework wrappers provide maximum flexibility while maintaining security and isolation.

### 2. Configuration-Driven Onboarding States

**OnboardJS Pattern:**

```typescript
const config = {
  steps: [...],
  componentRegistry: {...},
  onFlowComplete: (context) => {...},
  localStoragePersistence: { key: "onboardingState" }
}
```

**Stripe Connect Pattern:**

```typescript
const accountOnboarding = stripeConnectInstance.create('account-onboarding');
accountOnboarding.setCollectionOptions({
  fields: 'eventually_due',
  futureRequirements: 'include',
});
```

**Key Insight:** Configuration objects allow developers to customize onboarding flows without changing core library code.

### 3. Idempotent Detection Over State Storage

**Web3 Wallet Libraries (RainbowKit, ConnectKit):**

- Real-time wallet detection on every interaction
- No persistent state storage for connection status
- Graceful degradation when wallets unavailable

**Key Insight:** Rather than storing setup progress, successful libraries perform real-time detection to determine current state, making the system naturally resilient to environment changes.

### 4. Embedded Components with Overlay Options

**Successful Pattern Combinations:**

1. **Inline Embedded:** Components render directly in developer's layout
2. **Modal Overlay:** Components appear as overlays/modals over existing UI
3. **Developer Choice:** Configuration option to choose presentation mode

**Stripe Elements Example:**

```typescript
// Inline embedding
const paymentElement = elements.create('payment');
paymentElement.mount('#payment-element');

// Modal approach (via Checkout)
stripe.redirectToCheckout({ sessionId: 'session_id' });
```

### 5. Progressive Enhancement Architecture

**Common Patterns:**

1. **Core Detection:** Check for required dependencies
2. **Graceful Degradation:** Provide limited functionality when components missing
3. **Guided Setup:** Embedded UI to resolve missing components
4. **Full Functionality:** Normal operation when all components available

## Analyzed Libraries and Patterns

### 1. Stripe Elements

**Approach:** Iframe-based embedded components with extensive customization

- **Missing Dependencies:** Handles missing payment methods gracefully
- **Onboarding:** Stripe Connect embedded onboarding for complex setup flows
- **UI Strategy:** Self-contained iframes with CSS-level customization
- **Framework Support:** Framework-agnostic with React/Vue/Angular wrappers

**Key Learnings:**

- Iframe isolation prevents conflicts with host application
- Appearance API provides powerful customization without security risks
- Step-by-step onboarding with real-time validation
- Automatic requirement detection and progressive disclosure

### 2. OnboardJS

**Approach:** Headless state management with custom UI components

- **Missing Dependencies:** N/A (pure onboarding library)
- **Onboarding:** Multi-step flows with persistent state
- **UI Strategy:** Developer provides React components, library manages state
- **Framework Support:** React-focused with hooks

**Key Learnings:**

- Separation of state management and UI rendering
- Component registry pattern for mapping steps to UI
- Local storage persistence for progress tracking
- Lifecycle hooks for custom logic

### 3. Web3 Wallet Libraries (RainbowKit, ConnectKit)

**Approach:** React components with wallet detection and connection flows

- **Missing Dependencies:** Wallet installation guidance and app store links
- **Onboarding:** Modal-based wallet selection and installation guidance
- **UI Strategy:** React components with theme customization
- **Framework Support:** React-focused

**Key Learnings:**

- Real-time wallet detection without persistent state
- Browser-specific installation instructions
- App store deep linking for mobile wallets
- Graceful fallbacks for unsupported environments

### 4. Auth0 Universal Login

**Approach:** Hosted vs embedded authentication flows

- **Missing Dependencies:** Handles missing auth providers gracefully
- **Onboarding:** Embedded login widgets with customization
- **UI Strategy:** Hosted pages or embedded widgets
- **Framework Support:** Framework-agnostic SDKs

**Key Learnings:**

- Hosted vs embedded trade-offs (security vs customization)
- Configuration-driven customization
- Accessibility compliance (WCAG 2.2 AA)
- Progressive enhancement from basic to advanced auth

### 5. Modern Treasury User Onboarding

**Approach:** React hooks and components for compliance onboarding

- **Missing Dependencies:** Handles incomplete user verification
- **Onboarding:** Multi-step compliance flows
- **UI Strategy:** React components with customizable styling
- **Framework Support:** React-focused

**Key Learnings:**

- Hook-based API for state management
- Button components with built-in loading states
- Custom styling through props and CSS classes
- Success/error callback patterns

## Recommended Patterns for Bodhi-JS

### 1. Modal-First Architecture

**Modal Mode (Primary):**

```typescript
const client = await loadBodhiClient({
  onboarding: {
    mode: 'modal',
    size: 'medium', // 'small', 'medium', 'large'
    showOnMissingDeps: true,
    dismissible: true,
    theme: 'auto',
    customization: {
      colors: { primary: '#007bff' },
      borderRadius: '8px',
    },
  },
});
```

**Modal Benefits:**

- Independent of host application layout
- Mobile-responsive layouts controlled internally
- No DOM container conflicts
- Consistent UX across different integrations

### 2. Configuration-Driven Setup States

```typescript
interface BodhiClientConfig {
  timeout?: number;
  onboarding?: {
    mode: 'embedded' | 'modal' | 'custom' | 'disabled';
    container?: string;
    theme?: 'light' | 'dark' | 'auto';
    customization?: OnboardingCustomization;
    steps?: OnboardingStep[];
    callbacks?: {
      onStepChange?: (step: SetupStep) => void;
      onComplete?: () => void;
      onDismiss?: () => void;
    };
  };
}
```

### 3. Setup State Detection

```typescript
interface SetupState {
  extension: {
    status: 'installed' | 'missing' | 'disabled' | 'outdated';
    version?: string;
    downloadUrl?: string;
  };
  server: {
    status: 'reachable' | 'unreachable' | 'unauthorized';
    url?: string;
    version?: string;
  };
  overall: 'ready' | 'partial' | 'missing' | 'error';
}

// Real-time detection
const setupState = await client.getSetupState();
```

### 4. Self-Contained Modal Components

**Core Implementation (No Backend Required):**

```typescript
class BodhiOnboardingModal {
  constructor(config: OnboardingConfig) {
    this.config = config;
    this.modal = this.createModal();
  }

  private createModal(): HTMLElement {
    // Create modal using data URLs or srcdoc for complete isolation
    const modal = document.createElement('div');
    modal.className = 'bodhi-modal-overlay';

    // Self-contained iframe with srcdoc (no backend needed)
    const iframe = document.createElement('iframe');
    iframe.setAttribute('srcdoc', this.generateOnboardingHTML());
    iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin');
    iframe.style.cssText = this.getModalStyles();

    modal.appendChild(iframe);
    return modal;
  }

  private generateOnboardingHTML(): string {
    // Generate complete HTML document with embedded CSS and JS
    return `<!DOCTYPE html>
    <html>
    <head>
      <style>${this.getEmbeddedCSS()}</style>
    </head>
    <body>
      ${this.getOnboardingContent()}
      <script>${this.getEmbeddedJS()}</script>
    </body>
    </html>`;
  }

  show() {
    document.body.appendChild(this.modal);
    // Handle modal lifecycle
  }
}
```

**Benefits:**

- No backend dependencies (uses srcdoc/data URLs)
- Complete style isolation via iframe sandbox
- Mobile-responsive design controlled internally
- Framework-agnostic implementation

### 5. Progressive Enhancement Flow

```typescript
async function loadBodhiClient(
  config: BodhiClientConfig
): Promise<BodhiClient | BodhiOnboardingClient> {
  const setupState = await detectSetupState();

  switch (setupState.overall) {
    case 'ready':
      return new BodhiClient(extensionId);

    case 'partial':
    case 'missing':
      if (config.onboarding?.mode !== 'disabled') {
        return new BodhiOnboardingClient(setupState, config.onboarding);
      }
      throw new SetupIncompleteError(setupState);

    case 'error':
      throw new SetupError(setupState);
  }
}
```

### 6. Onboarding Client with Limited Functionality

```typescript
class BodhiOnboardingClient {
  constructor(private setupState: SetupState, private onboardingConfig?: OnboardingConfig) {}

  // Limited API when setup incomplete
  async ping(): Promise<{ message: string; setupRequired: SetupState }> {
    return {
      message: 'Setup incomplete',
      setupRequired: this.setupState,
    };
  }

  // Onboarding UI methods
  showOnboarding(container?: HTMLElement): BodhiOnboardingWidget {
    const target = container || this.createModalContainer();
    return new BodhiOnboardingWidget(target, {
      ...this.onboardingConfig,
      initialState: this.setupState,
    });
  }

  // Upgrade to full client when setup complete
  async checkAndUpgrade(): Promise<BodhiClient | null> {
    const newState = await detectSetupState();
    if (newState.overall === 'ready') {
      return new BodhiClient(newState.extension.id);
    }
    return null;
  }
}
```

## Implementation Recommendations

### Phase 1: Core Infrastructure

1. **Setup State Detection:** Implement idempotent detection for extension and server status
2. **Configuration System:** Design flexible config object for onboarding customization
3. **Basic Embedded UI:** Create framework-agnostic onboarding widget with HTML+CSS+JS

### Phase 2: Enhanced UI Components

1. **Theme System:** Implement light/dark themes with customization options
2. **Step-by-Step Flows:** Create guided setup flows for extension and app installation
3. **Browser Detection:** Add browser-specific installation instructions and deep links

### Phase 3: Framework Integration

1. **React Components:** Create React wrapper components and hooks
2. **Vue/Angular Support:** Add framework-specific wrappers as needed
3. **Advanced Customization:** Implement appearance API for deep customization

### Phase 4: Production Features

1. **Accessibility:** Ensure WCAG 2.2 AA compliance
2. **Internationalization:** Add multi-language support
3. **Analytics Integration:** Add optional telemetry for onboarding funnel analysis

## Concrete Interface Proposals

### Option A: Configuration-Heavy Approach (Recommended)

```typescript
// Comprehensive config-driven approach
const client = await loadBodhiClient({
  timeout: 10000,
  onboarding: {
    mode: 'embedded',
    container: '#setup-area',
    theme: 'auto',
    steps: {
      extensionDetection: { enabled: true, autoRetry: true },
      extensionInstallation: {
        enabled: true,
        customInstructions: 'Please install our browser extension',
      },
      serverConnection: { enabled: true, showAdvanced: false },
      completion: {
        enabled: true,
        redirectUrl: '/dashboard',
      },
    },
    customization: {
      colors: { primary: '#007bff', secondary: '#6c757d' },
      borderRadius: '8px',
      fontFamily: 'Inter, sans-serif',
    },
    callbacks: {
      onStepChange: step => analytics.track('onboarding_step', { step }),
      onComplete: () => analytics.track('onboarding_complete'),
      onDismiss: () => analytics.track('onboarding_dismissed'),
    },
  },
});
```

### Option B: Method-Chaining Approach

```typescript
// Fluent API for configuration
const client = await loadBodhiClient()
  .withOnboarding()
  .embedded('#setup-container')
  .theme('dark')
  .customColors({ primary: '#007bff' })
  .onComplete(() => console.log('Setup complete'))
  .build();
```

### Option C: Hook-Based Approach (React-specific)

```typescript
// React-first approach
function MyApp() {
  const { client, setupState, showOnboarding, isLoading } = useBodhiClient({
    onboarding: {
      mode: 'embedded',
      theme: 'light',
    },
  });

  if (setupState?.overall !== 'ready') {
    return <BodhiOnboarding onComplete={() => window.location.reload()} />;
  }

  return <MyAppContent client={client} />;
}
```

## Scenarios to Handle

### 1. Extension Missing

- **Detection:** `window.bodhiext` undefined after timeout
- **UI Response:** Show extension installation instructions with browser-specific download links
- **Fallback:** Provide manual installation guide and troubleshooting steps

### 2. Extension Outdated

- **Detection:** Extension version check against minimum required version
- **UI Response:** Show update instructions with link to latest version
- **Fallback:** Allow limited functionality with warning about missing features

### 3. Server Unreachable

- **Detection:** Network requests to Bodhi app fail
- **UI Response:** Show server setup instructions and connection troubleshooting
- **Fallback:** Provide offline mode or cloud-based alternatives if available

### 4. Partial Setup (Extension OK, Server Down)

- **Detection:** Extension available but server connectivity fails
- **UI Response:** Focus on server setup while showing extension status as complete
- **Fallback:** Allow extension-only features while guiding server setup

### 5. Network Issues

- **Detection:** Distinguish between server down vs network connectivity issues
- **UI Response:** Show appropriate messaging for network vs server problems
- **Fallback:** Implement retry mechanisms with exponential backoff

### 6. Browser Compatibility

- **Detection:** Check browser support for required features
- **UI Response:** Show browser compatibility information and upgrade suggestions
- **Fallback:** Graceful degradation for unsupported browsers

## Success Metrics

1. **Setup Completion Rate:** Percentage of users who complete full setup
2. **Time to Setup:** Average time from first interaction to full functionality
3. **Drop-off Points:** Where users abandon the onboarding process
4. **Support Ticket Reduction:** Decrease in setup-related support requests
5. **Developer Satisfaction:** Ease of integration and customization

## Conclusion

The research shows that successful embedded UI onboarding patterns share several key characteristics:

1. **Framework-agnostic core** with optional framework wrappers
2. **Configuration-driven customization** rather than code changes
3. **Real-time detection** instead of persistent state storage
4. **Progressive enhancement** from limited to full functionality
5. **Multiple presentation modes** (embedded, modal, custom)

For bodhi-js, implementing a configuration-heavy approach (Option A) provides the best balance of flexibility, developer experience, and maintainability while following proven patterns from successful integration libraries.
