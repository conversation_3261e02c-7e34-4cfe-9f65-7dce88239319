# Bodhi-JS Onboarding Interface Proposal (Revised)

## Overview

Based on the research into successful embedded UI onboarding patterns and clarifying feedback, this document proposes a focused interface design for implementing gradual onboarding features in the bodhi-js library. The design prioritizes modal-based onboarding, self-contained UI without backend dependencies, and intuitive developer experience.

## Core Design Principles

1. **Modal-First:** Overlay approach for complete independence from host application
2. **Self-Contained:** No backend dependencies using iframe srcdoc or data URLs
3. **Real-Time Detection:** Idempotent state detection without persistent storage
4. **Simple Configuration:** Essential customization options only
5. **Intuitive API:** Clear developer interaction patterns without instanceof checks

## Proposed API Structure

### 1. Redesigned Library Initialization

**Problem with loadBodhiClient:** Blocking behavior and instanceof checks are unintuitive.

**Solution:** Separate initialization from client usage with clear state management.

```typescript
interface BodhiConfig {
  timeout?: number;
  onboarding?: {
    mode: 'modal'; // Only modal mode supported
    theme?: 'light' | 'dark' | 'auto';
    dismissible?: boolean;
    callbacks?: {
      onComplete?: (state: BodhiPlatformState, client: BodhiExtClient) => void;
      onDismiss?: (state: BodhiPlatformState) => void;
    };
  };
}

// New initialization pattern
class BodhiPlatform {
  constructor(config: BodhiConfig = {}) {}

  // Non-blocking initialization
  async initialize(): Promise<BodhiPlatformState>;

  // Get current state
  getState(): BodhiPlatformState;

  // Get client if ready
  getClient(): BodhiExtClient | null;

  // Show onboarding modal
  showOnboarding(): void;
}

interface BodhiPlatformState {
  status: 'ready' | 'setup' | 'error';
  extension: ExtensionState;
  server: ServerState;
  client?: BodhiExtClient;
}

// State meanings:
// - ready: Both extension and server are ready for use
// - setup: One or both components need setup/configuration
// - error: Unrecoverable error occurred during detection
```

### 2. Enhanced Setup State Detection

```typescript
interface ExtensionState {
  status: 'ready' | 'installed' | 'unreachable' | 'not-installed' | 'unsupported' | 'disabled';
  version?: string;
}

// Extension state progression: not-installed/disabled -> unsupported -> installed -> ready
// - not-installed: Extension is not installed
// - disabled: Extension is installed but disabled by user
// - installed: Extension is installed but not configured
// - unreachable: Extension is installed but not responding
// - unsupported: Extension is of older version and is not supported by bodhi-js library
// - ready: Extension is installed and properly configured

interface ServerState {
  status: 'ready' | 'setup' | 'resource-admin' | 'unreachable' | 'error';
  url?: string;
  version?: string;
  error?: {
    type: 'network' | 'server' | 'auth' | 'config';
    code?: string; // Unique error code for debugging
    message: string;
    retryable: boolean;
  };
}

// Server state progression: unreachable -> setup/error -> resource-admin -> ready
// - unreachable: Cannot connect to server
// - setup: Server needs initial setup/installation
// - resource-admin: Server needs resource/admin configuration
// - ready: Server is running and accessible
// - error: Server returned an error
```

### 3. Self-Contained Modal Implementation

```typescript
class BodhiOnboardingModal {
  private modal: HTMLElement | null = null;
  private iframe: HTMLIFrameElement | null = null;

  constructor(private state: BodhiPlatformState, private config: BodhiConfig) {}

  show(): void {
    if (this.modal) return; // Already showing

    this.modal = this.createModalOverlay();
    this.iframe = this.createOnboardingIframe();

    this.modal.appendChild(this.iframe);
    document.body.appendChild(this.modal);

    // Handle escape key, backdrop clicks, etc.
    this.setupEventListeners();
  }

  hide(): void {
    if (this.modal) {
      document.body.removeChild(this.modal);
      this.modal = null;
      this.iframe = null;
    }
  }

  private createOnboardingIframe(): HTMLIFrameElement {
    const iframe = document.createElement('iframe');

    // Use srcdoc for self-contained content (no backend needed)
    iframe.setAttribute('srcdoc', this.generateOnboardingHTML());
    iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin');
    iframe.style.cssText = this.getIframeStyles();

    return iframe;
  }

  private generateOnboardingHTML(): string {
    // Complete HTML document with embedded CSS/JS
    return `<!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>Bodhi Setup</title>
      <style>${this.getEmbeddedCSS()}</style>
    </head>
    <body>
      ${this.getOnboardingContent()}
      <script>${this.getEmbeddedJS()}</script>
    </body>
    </html>`;
  }

  private getOnboardingContent(): string {
    // Generate content based on current state
    if (this.state.extension.status === 'not-installed') {
      return this.getExtensionInstallContent();
    } else if (this.state.server.status !== 'ready') {
      return this.getServerSetupContent();
    }
    return this.getGenericSetupContent();
  }
}
```

## Usage Examples

### Example 1: Basic Usage with Automatic Onboarding

```typescript
// Initialize Bodhi app
const app = new BodhiPlatform({
  onboarding: {
    mode: 'modal',
    theme: 'auto',
    callbacks: {
      onComplete: (state, client) => {
        console.log('Setup complete!', client.getExtensionId());
        // Continue with app functionality
        startApp(client);
      },
    },
  },
});

// Initialize and check state
const state = await app.initialize();

if (state.status === 'ready') {
  // Ready to use immediately
  const client = app.getClient();
  startApp(client);
} else if (state.status === 'setup') {
  // Show onboarding modal automatically
  app.showOnboarding();
}

function startApp(client: BodhiExtClient) {
  // Use client for API calls
  console.log('App ready with client:', client.getExtensionId());
}
```

### Example 2: React Integration

```typescript
// React hook (separate package: @BodhiPlatform/bodhijs-react)
function useBodhiPlatform(config?: BodhiConfig) {
  const [app] = useState(() => new BodhiPlatform(config));
  const [state, setState] = useState<BodhiPlatformState | null>(null);
  const [client, setClient] = useState<BodhiExtClient | null>(null);

  useEffect(() => {
    // Initialize app and check state
    app.initialize().then(initialState => {
      setState(initialState);

      if (initialState.status === 'ready') {
        setClient(app.getClient());
      }
    });
  }, [app]);

  const showOnboarding = useCallback(async () => {
    app.showOnboarding();

    // Wait for setup completion
    const checkCompletion = async () => {
      const newState = await app.initialize();
      setState(newState);

      if (newState.status === 'ready') {
        setClient(app.getClient());
      } else {
        // Check again after delay
        setTimeout(checkCompletion, 1000);
      }
    };

    checkCompletion();
  }, [app]);

  return {
    app,
    state,
    client,
    showOnboarding,
    isReady: state?.status === 'ready',
    needsSetup: state?.status === 'setup',
  };
}

// React component usage
function MyApp() {
  const { client, isReady, needsSetup, showOnboarding } = useBodhiPlatform({
    onboarding: {
      mode: 'modal',
      theme: 'auto',
    },
  });

  if (needsSetup) {
    return (
      <div>
        <h1>Setup Required</h1>
        <p>Bodhi extension or server setup is needed to continue.</p>
        <button onClick={showOnboarding}>Complete Bodhi Setup</button>
      </div>
    );
  }

  if (!isReady) {
    return <div>Loading...</div>;
  }

  return <MainApp client={client} />;
}
```

## Technical Implementation Details

### Self-Contained Modal Architecture

The onboarding modal uses iframe `srcdoc` attribute to create a completely self-contained UI:

```typescript
class BodhiOnboardingModal {
  private createModalIframe(): HTMLIFrameElement {
    const iframe = document.createElement('iframe');
    iframe.srcdoc = this.generateCompleteHTML();
    iframe.style.cssText = this.getModalStyles();
    return iframe;
  }

  private generateCompleteHTML(): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <style>${this.getEmbeddedCSS()}</style>
        </head>
        <body>
          ${this.getOnboardingContent()}
          <script>${this.getEmbeddedJS()}</script>
        </body>
      </html>
    `;
  }
}
```

### State Detection Strategy

```typescript
interface DetectionResult {
  extension: ExtensionDetectionResult;
  server: ServerDetectionResult;
  timestamp: number;
}

// Real-time detection without persistence
async function detectCurrentState(): Promise<DetectionResult> {
  const [extension, server] = await Promise.all([detectExtension(), detectServer()]);

  return {
    extension,
    server,
    timestamp: Date.now(),
  };
}
```

### Modal Communication Patterns

Since the modal runs in an iframe, communication happens through `postMessage`:

```typescript
// Parent to iframe
iframe.contentWindow?.postMessage(
  {
    type: 'config-update',
    theme: 'dark',
    customization: { colors: { primary: '#007bff' } },
  },
  '*'
);

// Iframe to parent
window.parent.postMessage(
  {
    type: 'setup-complete',
    client: clientReference,
  },
  '*'
);
```

### Modal Sizing Strategy

```typescript
// MVP: Fixed size with responsive behavior
const getModalSize = () => {
  const isMobile = window.innerWidth < 768;
  if (isMobile) {
    return { width: '95vw', height: '90vh' };
  }
  // Default desktop size for MVP
  return { width: '600px', height: '700px' };
};

// Future: Allow custom dimensions
// size?: { width: string; height: string } | 'auto'
```

## MVP Configuration

For the initial release, configuration is kept minimal:

```typescript
// Only essential configuration options for MVP
interface BodhiConfig {
  timeout?: number;
  onboarding?: {
    mode: 'modal';
    theme?: 'light' | 'dark' | 'auto';
    dismissible?: boolean;
    callbacks?: {
      onComplete?: (state: BodhiPlatformState, client: BodhiExtClient) => void;
      onDismiss?: (state: BodhiPlatformState) => void;
    };
  };
}

// Future enhancements can include:
// - Custom sizing options
// - Branding customization
// - Step flow configuration
// - Advanced error handling
// - Accessibility options
```

## Security Considerations

### Content Security Policy (CSP) Compatibility

The self-contained iframe approach works with strict CSP policies:

```typescript
// No external resources loaded
// All CSS/JS embedded in srcdoc
// No eval() or unsafe-inline needed
// Communication only through postMessage
```

### Data Isolation

```typescript
// Modal runs in separate context
// No access to parent page data
// Communication only through defined message interface
// No persistent storage in modal context
```

## Key Benefits of This Approach

### For Developers

1. **Intuitive API:** Clear state management without instanceof checks or blocking calls
2. **Non-Blocking:** Initialize app and handle setup asynchronously
3. **Framework Agnostic:** Works with any framework or vanilla JS
4. **No Backend Required:** Self-contained modal using iframe srcdoc
5. **CSP Compatible:** Works with strict Content Security Policies

### For Users

1. **Consistent Experience:** Modal approach provides uniform UX across all integrations
2. **Mobile Responsive:** Internal responsive design handles all screen sizes
3. **Complete Guidance:** Step-by-step setup for both extension and server
4. **Professional UI:** Branded modal with "Powered by Bodhi" attribution
5. **Accessible:** Full keyboard navigation and screen reader support

### Technical Advantages

1. **No Style Conflicts:** iframe isolation prevents CSS conflicts with host apps
2. **Self-Contained:** All HTML, CSS, and JS embedded in srcdoc (no backend)
3. **Real-Time Detection:** Idempotent state checking without persistent storage
4. **Minimal Configuration:** Simple options focused on essential customization
5. **Security Isolation:** Modal runs in separate context from host application

This interface proposal provides a focused, practical foundation for implementing gradual onboarding in bodhi-js while addressing all the clarified requirements and constraints.
