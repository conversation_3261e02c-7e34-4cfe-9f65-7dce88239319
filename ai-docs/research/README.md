# Research Documentation

This folder contains research documents that inform the design and implementation of new features in the Bodhi Browser project. Research documents analyze existing patterns, successful implementations from other libraries, and provide data-driven recommendations for our architectural decisions.

## Documents

### Embedded UI Onboarding Patterns

**File:** `embedded-ui-onboarding-patterns.md`

Comprehensive research into successful patterns from integration libraries that provide embedded UI components for handling missing dependencies and onboarding flows. Analyzes libraries like Stripe Elements, OnboardJS, RainbowKit, Auth0, and Modern Treasury to extract proven patterns for:

- Framework-agnostic embedded UI components
- Configuration-driven onboarding states
- Idempotent detection over state storage
- Progressive enhancement architectures
- Multiple presentation modes (embedded, modal, custom)

**Key Findings:**

- Framework-agnostic core with optional framework wrappers is the gold standard
- Configuration objects provide maximum flexibility without code changes
- Real-time detection is more reliable than persistent state storage
- Successful libraries offer multiple integration patterns for different use cases

### Bodhi-JS Onboarding Interface Proposal

**File:** `bodhi-js-onboarding-interface-proposal.md`

Concrete interface proposal for implementing gradual onboarding features in the bodhi-js library, based on the research findings. Provides detailed TypeScript interfaces, usage examples, and implementation strategy for:

- Enhanced `loadBodhiClient` function with onboarding configuration
- Setup state detection and management
- `BodhiOnboardingClient` for incomplete setup scenarios
- Framework-agnostic `OnboardingWidget` class
- React integration patterns and components

**Key Proposals:**

- Configuration-driven approach with extensive customization options
- Dual-mode architecture (embedded and modal presentation)
- Progressive enhancement from limited to full functionality
- Real-time setup state detection
- Comprehensive theming and customization system

## Research Process

Research documents in this folder follow a structured approach:

1. **Problem Analysis:** Clear definition of the challenge being addressed
2. **Pattern Research:** Analysis of successful implementations in similar libraries
3. **Key Findings:** Distilled insights and proven patterns
4. **Recommendations:** Specific architectural and implementation guidance
5. **Concrete Proposals:** Detailed interfaces and usage examples when applicable

## Usage Guidelines

- Reference these documents when implementing new features
- Update research documents when new patterns or libraries are discovered
- Use findings to inform architectural decisions and code reviews
- Cite research documents in feature specifications and design documents

## Contributing

When adding new research documents:

1. Follow the established structure and format
2. Include concrete examples and code samples
3. Cite sources and provide links to referenced libraries
4. Update this README.md with document descriptions
5. Reference research findings in related feature specifications
