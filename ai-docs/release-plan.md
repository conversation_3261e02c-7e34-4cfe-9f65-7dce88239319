# Bodhi Browser Extension: Practical Release Plan

This revised release plan presents a more practical, prioritized roadmap for the Bodhi Browser Extension project, focusing on the most essential features first while postponing nice-to-have elements. Each stage builds on the previous, reflecting a focused approach to delivering core functionality.

---

## Phase 1: Foundation (Completed)
**Files:** domain.md, architecture.md, mock-llm-server.md, bodhijs.md, bodhi-browser-ext.md

**What you can do after this phase:**
- Use a unified JavaScript library to interact with the extension from any web page
- Install the extension and proxy API calls between web pages and the local LLM server
- Configure the backend URL via the extension popup

---

## Phase 2: Core Functionality (Completed)
**Files:** feature-configurable-backend-server.md, feature-streaming-support.md

**What you can do after this phase:**
- Users can set/change the LLM backend URL through the extension UI
- Web apps receive LLM completions in real time (token streaming)
- Streaming is supported throughout the stack (bodhijs, extension, backend)

---

## Phase 3: Authentication with API Tokens (Priority)
**Files:** feature-auth-api-token-extension.md, feature-auth-api-token-web.md

**What you can do after this phase:**
- Web pages can prompt users for API tokens and store them securely
- Chrome extensions can prompt users for API tokens and store them securely
- Both web pages and extensions can make authenticated requests to the LLM resource server
- Users can manage (view, update, remove) their API tokens

**Implementation Focus:**
1. Add secure API token storage in extensions (chrome.storage) and web pages (localStorage)
2. Implement user interface for token entry, validation, and management
3. Add token inclusion in Authorization headers for all API requests
4. Ensure proper error handling for authentication failures

---

## Phase 4: OAuth2 Resource Server Integration (Priority)
**File:** feature-auth-oauth2-resource-server.md

**What you can do after this phase:**
- The LLM backend validates OAuth2 access tokens presented by clients
- Support token exchange if incoming token is not directly valid for resource server
- Return appropriate OAuth2 errors for invalid tokens
- Secure communication between all components using standard OAuth2 practices

**Implementation Focus:**
1. Implement token validation in the LLM backend
2. Add support for token exchange if needed
3. Ensure proper error propagation throughout the stack
4. Document the authentication flow for developers

---

## Phase 5: Multi-Server Management
**Files:** feature-setup-dialog-resource-discovery.md, feature-setup-unauthenticated-server-warnings.md, feature-setup-api-resource-server-parameter.md

**What you can do after this phase:**
- Configure multiple LLM resource servers (manual entry or scan)
- Set a default server and manage the server list (add, edit, remove)
- All APIs accept an optional `resourceServerId` parameter to select a server
- Users are warned about unauthenticated servers

---

## Phase 6: Advanced Authentication (Lower Priority)
**Files:** feature-auth-extension-oauth2.md, feature-auth-extension-token-forwarding.md, feature-auth-oauth2-confidential-client.md, feature-auth-oauth2-pkce-public-client.md

**What you can do after this phase:**
- Extensions can use OAuth2 tokens to access the LLM resource server
- The extension can forward OAuth2 tokens from web pages to the LLM resource server
- Support for both confidential client and PKCE public client OAuth2 flows

---

## Phase 7: Extension-to-Extension Communication (Nice to Have)
**Files:** feature-extension-to-extension.md, feature-bodhi-js-ext2ext.md

**What you can do after this phase:**
- Other Chrome extensions can securely access LLM capabilities via a standardized API
- Standardized error handling and security for extension-to-extension calls

---

## Phase 8: Usage Tracking (Nice to Have)
**Files:** feature-track-extension-client.md, feature-track-web-client.md

**What you can do after this phase:**
- The extension logs which Chrome extensions are invoking its APIs
- The extension logs which web pages are invoking its APIs
- Enables allow/block lists and analytics for both extension and web page usage

---

## Test Plan Integration
**File:** feature-setup-test-plan.md

Testing will be integrated throughout all phases, ensuring each feature is properly validated before release.

---

## Prioritization Rationale

This release plan is organized based on the following priorities:

1. **Core Functionality First**: The foundation and basic streaming capabilities are essential and have been completed.

2. **Authentication is Critical**: API token authentication for both extensions and web pages is the next priority, as it enables secure access to the LLM server. This is a practical approach that delivers immediate value.

3. **OAuth2 Resource Server**: Making the LLM server act as an OAuth2 resource server is also high priority, as it enables authenticated communication with client web pages and extensions.

4. **Multi-Server Management**: After authentication is in place, adding support for multiple servers provides flexibility for users.

5. **Advanced Features Later**: Extension-to-extension communication, advanced OAuth2 flows, and usage tracking are lower priority and can be implemented later as they're not critical for the core user flow.

This approach focuses on delivering a functional, authenticated system first, then extending with more sophisticated features as resources allow.

---

**Development Strategy:**
1. Complete the API token authentication features for both web pages and extensions (Phase 3)
2. Implement the OAuth2 resource server integration (Phase 4)
3. Add multi-server management capabilities (Phase 5)
4. Evaluate priorities for the remaining features based on user feedback and resources

**Note:**
- Each phase delivers a functional product increment with clear user value
- Testing is integrated throughout each phase
- Later phases may be reprioritized based on user feedback and evolving requirements
