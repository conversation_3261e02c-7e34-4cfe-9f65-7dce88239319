# Project Files and Directories

This document provides an overview of the files and directories in the Bodhi Browser project, explaining the purpose and functionality of each component.

## Project Structure

The Bodhi Browser project is organized into three main components:

1. **bodhi-browser-ext**: Chrome extension that acts as a bridge between web pages and the local Bodhi App server
2. **bodhijs**: JavaScript/TypeScript library that bridges communication between web pages and the extension
3. **integration-tests**: Tests that verify the extension and library work correctly with test web pages and applications

## bodhi-browser-ext (Chrome Extension)

The following is the updated file structure of the bodhi-browser-ext project:

```
bodhi-browser-ext/
├── .gitignore                # Git ignore file
├── .env.test.example         # Example environment variable file
├── Makefile                  # Build and development commands
├── README.md                 # Project overview
├── WEBPACK_BUILD.md          # Webpack build details
├── eslint.config.mjs         # ESLint configuration
├── fix-paths.mjs             # Script to fix asset paths
├── next-env.d.ts             # Next.js type declarations
├── next.config.ts            # Next.js configuration
├── package-lock.json         # NPM lockfile
├── package.json              # NPM package configuration
├── postcss.config.mjs        # PostCSS config
├── tsconfig.json             # TypeScript config
├── vitest.config.js          # Vitest test config
├── dist/                     # Bundled extension scripts and assets
│   ├── background.js         # Bundled background script
│   ├── content.js            # Bundled content script
│   ├── inject.js             # Bundled inject script
│   ├── icons/                # Bundled icons
│   ├── manifest.json         # Bundled manifest
│   └── ...                   # Other build outputs
├── node_modules/             # Installed dependencies
├── public/                   # Static assets and manifest
│   ├── favicon.ico           # Extension favicon
│   ├── icons/                # Icon images
│   └── manifest.json         # Extension manifest
├── src/                      # Next.js UI (popup/settings)
│   ├── components/           # React components
│   │   └── Settings/         # Settings UI
│   ├── pages/                # Next.js pages (_app.tsx, _document.tsx, index.tsx)
│   └── styles/               # CSS modules and global styles
├── src-ext/                  # Extension scripts (TypeScript)
│   ├── background.ts         # Background service worker
│   ├── content.ts            # Content script
│   ├── inject.ts             # Injected script for window.bodhiext
│   └── shared/               # Shared types/utilities/constants
├── tests/                    # Test files and helpers
│   ├── README.md             # Test documentation
│   ├── ext2ext.test.ts       # Extension-to-extension tests
│   ├── settings.test.ts      # Settings tests
│   ├── setup.ts              # Test setup
│   ├── test-extension/       # Extension test helpers/assets
│   ├── test-helpers.ts       # Test utilities
│   ├── test-page/            # Test web pages/assets
│   └── web2ext.test.ts       # Web-to-extension tests
```

### Directory and File Descriptions

- **src/**: Contains the Next.js React UI for the extension popup/settings.
- **src-ext/**: Contains all extension logic scripts (background, content, inject, shared TS code).
- **public/**: Static assets and manifest for the extension (icons, manifest.json, etc).
- **dist/**: Webpack/Next.js build output for extension scripts and assets.
- **tests/**: Integration/unit tests and test helpers/assets.

### File Descriptions

| File                  | Description                                                                                       |
| --------------------- | ------------------------------------------------------------------------------------------------- |
| **eslint.config.mjs** | Configuration file for ESLint that defines code style and quality rules for the extension.       |
| **fix-paths.mjs**     | Script that fixes asset paths in HTML files after Next.js build.                                  |
| **next.config.ts**    | Next.js configuration file that specifies options for the build.                                 |
| **postcss.config.mjs**| Configuration for PostCSS to support Tailwind CSS processing.                                    |
| **src-ext/background.ts** | Background service worker script that handles communication with the Bodhi App server.     |
| **src-ext/content.ts** | Content script injected into web pages to facilitate communication between the page and extension.|
| **src-ext/inject.ts**  | Script injected directly into web pages that creates and exposes the window.bodhiext global object.|
| **public/manifest.json** | Chrome extension manifest file that defines metadata, permissions, scripts, and other extension configuration.|
| **eslint.config.mjs** | Configuration file for ESLint that defines code style and quality rules for the extension, updated to ES module format with Next.js support. |
| **fix-paths.mjs**     | Script that fixes paths in HTML files after Next.js build, replacing `/_next/` with `./next/` for Chrome extension compatibility. |
| **next.config.ts**    | Next.js configuration file that specifies options for the build, including output format, distribution directory, and React strict mode. |
| **postcss.config.mjs**| Configuration for PostCSS to support Tailwind CSS processing. |
| **public/background.js** | Background service worker script that handles communication with the Bodhi App server. It processes messages from content scripts, makes HTTP requests to the local server, and manages extension lifecycle events. |
| **public/content.js** | Content script injected into web pages to facilitate communication between the page and extension. It injects the inject.js script into pages, listens for messages via window.postMessage, and forwards them to the background script. |
| **public/inject.js**  | Script injected directly into web pages that creates and exposes the window.bodhiext global object. It implements methods for web pages to communicate with the extension and manages message passing. |
| **public/manifest.json** | Chrome extension manifest file that defines metadata, permissions, scripts, and other extension configuration. It specifies the extension's capabilities and security restrictions. |
| **src/components/Index/index.tsx** | React component that serves as the main landing page for the extension popup, showing a title, description, and a navigation button. |
| **src/components/New/index.tsx** | React component that serves as a secondary page, demonstrating navigation between pages in the extension UI. |
| **src/components/Settings/index.tsx** | React component that provides a settings UI for configuring extension options, such as API endpoints or user preferences. |
| **src/pages/_app.tsx** | Next.js app component that sets up global styles and provides the outer wrapper for all pages. |
| **src/pages/_document.tsx** | Next.js document component that defines the HTML structure, including language attribute, head section, and scripts. |
| **src/pages/index.tsx** | Main page component that manages navigation state between different UI components. |
| **src/pages/Pages.module.css** | CSS Module that provides styles for page components, including layout, typography, and visual effects. |
| **src/styles/globals.css** | Global CSS file that imports Tailwind CSS and sets up theme variables for light and dark modes. |
| **tsconfig.json**     | TypeScript configuration file that specifies compiler options, including target versions, module resolution, and included files. |
| **tests/background.test.js** | Tests for the background script logic. |
| **tests/ext2ext.test.ts** | Tests communication between extensions. |
| **tests/settings.test.ts** | Tests the Settings component UI and logic. |
| **tests/setup.ts** | General test setup for the extension tests. |
| **tests/test-extension/** | Directory containing extension-specific test helpers and mocks. |
| **tests/test-helpers.ts** | Utility functions for use across multiple tests. |
| **tests/test-page/** | Test pages for integration and UI testing. |
| **tests/web2ext.test.ts** | Tests communication from web pages to the extension. |
| **vitest.config.js**  | Configuration file for the Vitest testing framework used to run the extension's tests. |

## bodhijs (JavaScript Library)

The following is the file structure of the bodhijs project:

```
bodhi-js/
├── README.md                # Project documentation
├── eslint.config.js         # ESLint configuration
├── package.json             # NPM package configuration
├── rollup.config.js         # Rollup bundler configuration
├── src/                     # Source code
│   ├── api.ts               # API implementation
│   ├── core.ts              # Core utilities
│   ├── index.ts             # Main entry point
│   └── types.ts             # TypeScript type definitions
├── tests/                   # Integration and E2E tests
│   ├── README.md            # Test documentation
│   ├── bodhijs-as-npm-library.test.ts         # E2E test for npm package usage
│   ├── bodhijs-as-script-tag-library.test.ts  # E2E test for script tag usage
│   ├── npm-test-app/        # Test app using npm package
│   │   ├── package.json     # NPM configuration for test app
│   │   ├── public/          # Public files for test app
│   │   ├── src/             # Source code for test app
│   │   ├── tsconfig.json    # TypeScript configuration for test app
│   │   └── webpack.config.js# Webpack configuration for test app
│   ├── npm-test-static-server.ts   # Static server for npm tests
│   ├── setup.ts             # Common test setup
│   ├── start-mock-llm.js    # Script to start the mock LLM server
│   ├── test-page/           # Test pages for script tag scenarios
│   │   ├── index.html       # Main test HTML page
│   │   ├── script-tag-chat-completion.html      # Chat completion test page
│   │   ├── script-tag-installation-check.html   # Installation check page
│   │   ├── script-tag-ping-test.html            # Ping test page
│   │   └── script-tag-streaming-chat.html       # Streaming chat test page
│   └── test-utils.ts        # Test utilities
└── tsconfig.json            # TypeScript compiler configuration
```

### File Descriptions

| File                                         | Description                                                                                       |
|----------------------------------------------|---------------------------------------------------------------------------------------------------|
| **README.md**                               | Documentation file for the bodhijs library.                                                      |
| **eslint.config.js**                        | ESLint configuration for the bodhijs library.                                                    |
| **package.json**                            | NPM package configuration for the library.                                                       |
| **rollup.config.js**                        | Rollup bundler configuration for the library.                                                    |
| **src/api.ts**                              | API implementation for the library.                                                              |
| **src/core.ts**                             | Core utilities for the library.                                                                  |
| **src/index.ts**                            | Main entry point for the library.                                                                |
| **src/types.ts**                            | TypeScript type definitions for the library.                                                     |
| **tsconfig.json**                           | TypeScript configuration for the library.                                                        |
| **tests/README.md**                         | Documentation for the integration and E2E tests.                                                 |
| **tests/bodhijs-as-npm-library.test.ts**     | End-to-end test verifying bodhijs works as an npm package.                                       |
| **tests/bodhijs-as-script-tag-library.test.ts** | End-to-end test verifying bodhijs works when included via script tag.                         |
| **tests/npm-test-app/**                     | Test application using bodhijs as an npm package.                                                |
| **tests/npm-test-static-server.ts**          | Static server for serving the npm test application during tests.                                 |
| **tests/setup.ts**                          | Common setup file for the integration and E2E tests.                                             |
| **tests/start-mock-llm.js**                 | Script to start the mock LLM server for integration tests.                                       |
| **tests/test-page/**                        | Test pages for script tag scenarios and UI integration.                                          |
| **tests/test-utils.ts**                     | Utility functions for use in tests.                                                              |

#### tests/npm-test-app/
| File                       | Description                                                     |
|----------------------------|-----------------------------------------------------------------|
| **package.json**           | NPM configuration for the test app.                              |
| **public/**                | Public files for the test app (HTML, CSS, etc).                  |
| **src/**                   | Source code for the test app.                                    |
| **tsconfig.json**          | TypeScript configuration for the test app.                       |
| **webpack.config.js**      | Webpack configuration for the test app.                          |

#### tests/test-page/
| File                                    | Description                        |
|-----------------------------------------|------------------------------------|
| **index.html**                          | Main test HTML page.               |
| **script-tag-chat-completion.html**     | Chat completion test page.         |
| **script-tag-installation-check.html**  | Installation check test page.      |
| **script-tag-ping-test.html**           | Ping test page.                    |
| **script-tag-streaming-chat.html**      | Streaming chat test page.          |

## mock-llm-server

The following is the file structure of the mock-llm-server project:

```
mock-llm-server/
├── Makefile                # Build and test automation
├── bin/                    # Executable scripts
├── dist/                   # Compiled output
├── eslint.config.js        # ESLint configuration
├── package.json            # NPM package configuration
├── src/                    # Source code
│   ├── index.ts            # Entry point for the mock server
│   ├── mock-llm-server.ts  # Main mock LLM server implementation
│   ├── static-server.ts    # Static file server for serving test assets
│   ├── stub-bodhi-server.test.ts # Tests for the stub Bodhi server
│   └── stub-bodhi-server.ts      # Stub implementation for Bodhi server
├── tsconfig.json           # TypeScript configuration
└── vitest.config.ts        # Vitest configuration
```

### File Descriptions

| File                          | Description                                                                                       |
| ----------------------------- | ------------------------------------------------------------------------------------------------- |
| **Makefile**                  | Build and test automation commands for the mock-llm-server project. |
| **bin/**                      | Directory containing executable scripts for development and testing. |
| **dist/**                     | Compiled output of the TypeScript source code. |
| **eslint.config.js**          | Configuration file for ESLint that defines code style and quality rules for the mock server. |
| **package.json**              | Node.js package configuration for dependencies and scripts. |
| **src/index.ts**              | Entry point that starts the mock LLM server. |
| **src/mock-llm-server.ts**    | Main implementation of the mock LLM server, simulating responses for testing. |
| **src/static-server.ts**      | Static file server for serving test assets. |
| **src/stub-bodhi-server.test.ts** | Test file for the stub Bodhi server implementation. |
| **src/stub-bodhi-server.ts**  | Stub implementation of the Bodhi server for integration testing. |
| **tsconfig.json**             | TypeScript compiler configuration. |
| **vitest.config.ts**          | Configuration file for the Vitest testing framework. |

## Key Relationships

- **public/content.js** injects **public/inject.js** into web pages
- **public/inject.js** creates the global window.bodhiext object
- **bodhijs** interacts with the window.bodhiext object
- **public/background.js** communicates with the local Bodhi App server
- **src/pages/index.tsx** serves as the entry point for the extension popup UI
- Web pages include **bodhijs** as a script tag or npm package (@bodhiapp/bodhijs) to use the system
- **bodhi-js/tests** (formerly integration-tests) verifies that all components work together correctly


[Log] [BodhiOnboardingModal] Detected browser: – {name: "Safari", type: "safari"} (index-BK3wDTQp.js, line 66)
[Log] [BodhiOnboardingModal] Available browsers: – [{id: "chrome", name: "Google Chrome"}, {id: "edge", name: "Microsoft Edge"}, {id: "firefox", name: "Firefox"}, …] (5) (index-BK3wDTQp.js, line 66)
[{id: "chrome", name: "Google Chrome"}, {id: "edge", name: "Microsoft Edge"}, {id: "firefox", name: "Firefox"}, {id: "safari", name: "Safari"}, {id: "unknown", name: "Unknown Browser"}]Array (5)
[Log] [BodhiOnboardingModal] Final setup state env: – {browser: "safari", os: "macos"} (index-BK3wDTQp.js, line 66)
[Log] [BodhiOnboardingModal] Final browsers array: – [{id: "chrome", name: "Google Chrome"}, {id: "edge", name: "Microsoft Edge"}, {id: "firefox", name: "Firefox"}, …] (5) (index-BK3wDTQp.js, line 66)
[{id: "chrome", name: "Google Chrome"}, {id: "edge", name: "Microsoft Edge"}, {id: "firefox", name: "Firefox"}, {id: "safari", name: "Safari"}, {id: "unknown", name: "Unknown Browser"}]Array (5)
[Log] [BodhiOnboardingModal] Sent state to modal: – {extension: {status: "not-installed", error: {message: "Bodhi extension is not installed or not detected", code: "ext-not-installed"}}, server: {status: "pending-extension-ready", error: {message: "Server is waiting for extension to be ready", code: "server-pending-ext-ready"}}, env: {browser: "safari", os: "macos"}, …} (index-BK3wDTQp.js, line 109)
{extension: {status: "not-installed", error: {message: "Bodhi extension is not installed or not detected", code: "ext-not-installed"}}, server: {status: "pending-extension-ready", error: {message: "Server is waiting for extension to be ready", code: "server-pending-ext-ready"}}, env: {browser: "safari", os: "macos"}, browsers: Array, os: Array}Object
[Log] [BodhiOnboardingModal] Detected browser: – {name: "Safari", type: "safari"} (index-BK3wDTQp.js, line 66)
[Log] [BodhiOnboardingModal] Available browsers: – [{id: "chrome", name: "Google Chrome"}, {id: "edge", name: "Microsoft Edge"}, {id: "firefox", name: "Firefox"}, …] (5) (index-BK3wDTQp.js, line 66)
[{id: "chrome", name: "Google Chrome"}, {id: "edge", name: "Microsoft Edge"}, {id: "firefox", name: "Firefox"}, {id: "safari", name: "Safari"}, {id: "unknown", name: "Unknown Browser"}]Array (5)
[Log] [BodhiOnboardingModal] Final setup state env: – {browser: "safari", os: "macos"} (index-BK3wDTQp.js, line 66)
[Log] [BodhiOnboardingModal] Final browsers array: – [{id: "chrome", name: "Google Chrome"}, {id: "edge", name: "Microsoft Edge"}, {id: "firefox", name: "Firefox"}, …] (5) (index-BK3wDTQp.js, line 66)
[{id: "chrome", name: "Google Chrome"}, {id: "edge", name: "Microsoft Edge"}, {id: "firefox", name: "Firefox"}, {id: "safari", name: "Safari"}, {id: "unknown", name: "Unknown Browser"}]Array (5)
[Log] [BodhiOnboardingModal] Sent state to modal: (index-BK3wDTQp.js, line 109)
Object

browsers: [Object, Object, Object, Object, Object] (5)

env: {browser: "safari", os: "macos"}

extension: {status: "not-installed", error: {message: "Bodhi extension is not installed or not detected", code: "ext-not-installed"}}

os: [Object, Object, Object, Object] (4)

server: {status: "pending-extension-ready", error: {message: "Server is waiting for extension to be ready", code: "server-pending-ext-ready"}}

Object Prototype