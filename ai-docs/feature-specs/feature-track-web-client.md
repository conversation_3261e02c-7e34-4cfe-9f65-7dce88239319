# Feature Specification: Tracking Web Page Clients Invoking Extension APIs

## Primary Task
Enable the Bodhi Browser Extension to reliably track and log which web page (origin and context) is invoking its APIs via bodhi-js, for auditing, security, and analytics.

## Overview
When a web page uses bodhi-js to communicate with the Bodhi Browser Extension (for LLM queries, authentication, etc.), it is important for the extension to capture and record the identity of the invoking web page. This enables:
- Auditing which sites are using the extension
- Security enforcement (e.g., allow/block lists)
- Analytics on extension usage
- Debugging and support

This feature details how the extension can reliably capture the source of each API call made from a web page context.

## Technical Details

### 1. Communication Flow
- Web pages communicate with the extension via injected scripts and content scripts (using `window.postMessage` and `chrome.runtime.sendMessage`).
- The message travels: Web Page → inject.js → content.js → background.js (service worker).

### 2. Capturing the Source
- In `content.js`, when relaying messages from the web page, capture the following:
  - `window.location.origin` and `window.location.href` (the origin and full URL of the page)
  - Optionally, the top-level frame URL if running in an iframe
  - Any identifying information from the message payload (e.g., client app name/version if provided)
- Attach this information to the message sent to `background.js`.
- In `background.js`, log or process the origin/URL with each request.

### 3. Security and Reliability
- Do not trust data provided directly by the web page (e.g., a self-reported origin in the payload). Always capture the origin in `content.js`.
- For iframes, distinguish between the iframe's origin and the top-level window's origin (if accessible).
- Consider edge cases:
  - Multiple tabs/windows
  - Incognito mode
  - Same-origin iframes vs. cross-origin iframes
- Ensure that no sensitive user data (e.g., page content) is logged—only origin and URL.

### 4. Logging and Usage
- Store origin/URL with each API invocation in extension logs (memory, Chrome storage, or external analytics if user consents).
- Optionally, implement allow/block lists for origins.
- Provide a UI in the extension for users to review which sites have accessed the extension.

## Flow Diagram

```
[Web Page] --(postMessage)--> [inject.js] --(postMessage)--> [content.js]
     | (captures window.location.origin/href)
[content.js] --(chrome.runtime.sendMessage w/ origin)--> [background.js]
     | (logs origin/URL)
[background.js] --(proxies API request)--> [LLM Resource Server]
```

## Implementation Checklist
- [ ] content.js: Capture window.location.origin and href for each message
- [ ] content.js: Attach origin/URL to messages sent to background.js
- [ ] background.js: Log/store origin/URL with each API invocation
- [ ] Handle iframe and multi-tab scenarios
- [ ] UI: Optionally display origin usage to user
- [ ] Documentation: Describe tracking and privacy implications

## Test Plan
- Unit test: Correct origin/URL is captured for various web page contexts
- Integration test: Origin is logged for API calls from multiple tabs/windows
- Test: Cross-origin iframe and incognito scenarios
- Security test: Ensure only origin/URL (not sensitive data) is logged
- UI test: Display of tracked origins (if implemented)

## Out of Scope
- Tracking extension-to-extension invocations (covered in a separate feature)
- Tracking user identity (only site origin/URL is tracked)
