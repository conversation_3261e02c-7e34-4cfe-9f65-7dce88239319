# Feature Specification: Extension Authenticated Resource Access

## Primary Task
Enable Chrome extensions (including Bodhi Browser Extension or third-party extensions) to authenticate and access the LLM resource server using OAuth2 tokens, supporting both direct and proxied flows.

## Overview
While the primary use case covers web pages authenticating via bodhi-js, extensions themselves may need to access the LLM resource server as authenticated clients. This feature specifies the flows for extensions to acquire, forward, and use OAuth2 tokens (including token exchange if needed) when accessing the resource server, and how the Bodhi Browser Extension mediates these requests.

## Technical Details

### 1. Extension as OAuth2 Client
- Extension registers as an OAuth2 client (public or confidential, per use case).
- Initiates OAuth2 flow (PKCE for public, client credentials for confidential).
- Stores and manages its own access token securely (Chrome storage).

### 2. Making Authenticated Requests
- Extension includes `Authorization: Bearer <token>` in requests to the resource server, either directly (if allowed by CORS) or via the Bodhi Browser Extension as a proxy.
- The Bodhi Browser Extension performs the same token format checks as for web pages, then forwards the request.

### 3. Token Exchange (Optional)
- If the extension's token is not valid for the resource server, it may use OAuth2 Token Exchange (RFC 8693) to obtain a suitable token.
- The extension or the resource server initiates the exchange, depending on architecture.

### 4. Security Considerations
- Tokens are stored only in extension storage, never exposed to web pages.
- All requests and token exchanges occur over HTTPS.
- Extensions must request appropriate permissions for OAuth2 endpoints and resource server URLs.

## Flow Diagram
```
[Extension] --(OAuth2 flow)--> [Authorization Server]
     | (receives access token)
[Extension] --(API request w/ token)--> [bodhi-browser-ext] --(proxy)--> [LLM Resource Server]
     | (validates or exchanges token)
[LLM Resource Server] --(response)--> [bodhi-browser-ext] --(response)--> [Extension]
```

## Implementation Checklist
- [ ] Extension: Register and configure as OAuth2 client
- [ ] Extension: Implement token acquisition and storage
- [ ] Extension: Add Authorization header to API requests
- [ ] bodhi-browser-ext: Support proxied, authenticated extension requests
- [ ] Integration: Test direct and proxied flows
- [ ] Documentation: Update extension integration guide

## Test Plan
- Unit test: Extension token acquisition and storage
- Integration test: Authenticated request via bodhi-browser-ext
- Integration test: Token exchange flow
- Security test: Token isolation between extensions and web pages
- Test: Error handling for invalid/missing tokens
- Test: Permission enforcement for protected endpoints

## Out of Scope
- Web page authentication (covered in other features)
- Resource server implementation details (see resource server integration feature)
