# Comprehensive Test Plan: Multi-Server LLM Extension

Test cases are grouped by their relevant story/spec file and written in Given-When-Then format.

---

## 1. feature-setup-dialog-resource-discovery.md

### Server Discovery and Setup Dialog
- **Given** the user opens the setup dialog for the first time  
  **When** the extension scans for local/network servers  
  **Then** all discoverable LLM servers are listed with their type and endpoint

- **Given** the user enters a new server endpoint manually  
  **When** the endpoint is valid and unique  
  **Then** the server is added to the configured list

- **Given** the user tries to add a duplicate or invalid endpoint  
  **When** the form is submitted  
  **Then** an error message is shown and the server is not added

### Server Prioritization and Default
- **Given** a list of configured servers  
  **When** the user drags a server to the top  
  **Then** it is marked as the default and receives the “Default” badge

- **Given** the default server is deleted  
  **When** the action is confirmed  
  **Then** the next server in the list becomes default automatically

### Editing and Removing Servers
- **Given** a configured server  
  **When** the user edits its name or endpoint  
  **Then** the changes are saved if valid and unique

- **Given** a configured server  
  **When** the user clicks the trash icon  
  **Then** the server is removed from the list

---

## 2. feature-api-resource-server-parameter.md

### API Parameter Handling
- **Given** multiple servers are configured  
  **When** a client calls an API with a valid `resourceServerId`  
  **Then** the request is routed to the correct backend and response is returned

- **Given** no `resourceServerId` is specified  
  **When** a client calls an API  
  **Then** the request is routed to the default server

- **Given** an invalid or unavailable `resourceServerId` is provided  
  **When** a client calls an API  
  **Then** a clear error (e.g., `RESOURCE_SERVER_NOT_FOUND`) is returned

### Exposing Server List to Clients
- **Given** the extension has multiple servers configured  
  **When** a client calls `getAvailableResourceServers()`  
  **Then** the full, up-to-date list with all relevant fields is returned

- **Given** a server’s status or configuration changes  
  **When** a client calls `getAvailableResourceServers()`  
  **Then** the response reflects the latest configuration

### Backward Compatibility
- **Given** a legacy client (no server selection support)  
  **When** it calls an API  
  **Then** the request is routed to the default server and behaves as before

---

## 3. feature-unauthenticated-server-warnings.md

### Warning and Consent
- **Given** a user attempts to add an unauthenticated server (discovery or manual)  
  **When** the add action is initiated  
  **Then** a warning icon and text are displayed, and explicit consent is required

- **Given** a user does not provide consent  
  **When** attempting to add an unauthenticated server  
  **Then** the server is not added

- **Given** an unauthenticated server is in the configured list  
  **When** the server list is displayed  
  **Then** it is visually marked (icon + tooltip) as unauthenticated

---

## 4. Client Integration and UX (merged into feature-api-resource-server-parameter.md)

### Server Selection in Client UI
- **Given** a client app displays available servers  
  **When** the user selects a server from a dropdown or list  
  **Then** subsequent API calls use the selected server’s ID

- **Given** a server is unauthenticated  
  **When** it is selected in the client UI  
  **Then** a warning or info banner is displayed to the user

- **Given** a server becomes unavailable  
  **When** the user attempts to use it  
  **Then** an actionable error is shown and the user can select another server
