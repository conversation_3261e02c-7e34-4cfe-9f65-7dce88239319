# Feature Specification: Refactor Extension-to-Extension Communication to Generic makeRequest Action

## Primary Task
Make the web page to extension and extension-to-extension request/response formats consistent, using a unified structure for both types of communication. This change unifies the API surface for both one-off and streaming messages, making the extension-to-extension protocol more consistent and extensible.

## Motivation
- **Consistency:** Align extension-to-extension communication with the established web extension messaging format.
- **Extensibility:** Enable future addition of new API actions without protocol changes.
- **Simplicity:** Reduce code duplication and simplify message validation and routing.

## Background
Historically, extension-to-extension communication and web page to extension communication used different message formats and conventions. The unified format, as described in [ai-docs/extension-message-format.md], specifies that all API and streaming requests and responses—regardless of origin—should use a consistent structure with `type`, `requestId`, and a `request` or `response` object. This proposal ensures that extension-to-extension communication adopts the same format as web page to extension communication, eliminating legacy patterns and enabling a single, consistent protocol across all extension messaging.

## Technical Details

### Message Protocol

#### Request Format (One-off & Streaming)
All extension-to-extension requests must use the following format:

```json
{
  "type": "BODHI_API_REQUEST" | "BODHI_STREAM_REQUEST",
  "requestId": "<unique-id>",
  "request": {
    "method": "GET" | "POST" | ...,
    "endpoint": "/v1/chat/completions",
    "body": { ... },
    "headers": { ... }
  }
}
```
- `type`: Indicates if this is a regular API request or a streaming request.
- `requestId`: Unique identifier for correlating requests and responses.
- `request`: Contains all details for the HTTP call (method, endpoint, body, headers).

**Note:**
- There are no special keys like `action` or `payload`. The format is identical to the web page to extension format described in `ai-docs/extension-message-format.md`.
- All extension-to-extension consumers must generate and propagate a unique `requestId`.
- `requestId` is only for consistency with web-page request format, it is not used in routing the response.

#### Response Format (One-off & Streaming)
Identical to web page to extension:

```json
{
  "type": "BODHI_API_RESPONSE" | "BODHI_STREAM_CHUNK" | "BODHI_STREAM_ERROR",
  "requestId": "<same-id>",
  "response": { ... }
}
```

### Example

#### Request
```json
{
  "type": "BODHI_API_REQUEST",
  "requestId": "abc123",
  "request": {
    "method": "POST",
    "endpoint": "/v1/chat/completions",
    "body": { "messages": [ ... ] },
    "headers": { "Authorization": "Bearer ..." }
  }
}
```

#### Response
```json
{
  "type": "BODHI_API_RESPONSE",
  "requestId": "abc123",
  "response": { "result": ... }
}
```

#### Streaming Chunk
```json
{
  "type": "BODHI_STREAM_CHUNK",
  "requestId": "abc123",
  "response": { "chunk": ... }
}
```

### Error Handling
All errors must be returned in the same response structure, with `type` set to `BODHI_ERROR` (one-off) or `BODHI_STREAM_ERROR` (streaming), and the error details in the `response` field.

### Migration Steps
1. **Update background.ts** and all extension-to-extension consumers to use the unified message format (no `action`, no `payload`).
2. **Deprecate and remove all legacy action names and wrappers.**
3. **Update documentation and TypeScript types** to reflect the new message format.
4. **Add tests** for both one-off and streaming extension-to-extension requests using the new protocol.

## Projects Impacted
- **bodhi-browser-ext**: background script, message validation, error handling, streaming logic.
- **integration-tests**: test extension-to-extension scenarios using new format.
- **Documentation**: Update extension messaging docs and code samples.

## Acceptance Criteria
- [ ] All extension-to-extension requests and responses use the unified message format as described above.
- [ ] No legacy action names or payload wrappers are accepted.
- [ ] Responses and errors use the unified response structure.
- [ ] Streaming works with the new format.
- [ ] Tests and documentation are updated.

## Out of Scope
- Changes to web page → extension communication (already uses the correct format).
- Non-API extension messaging (unrelated to API proxying).

---

**Author:**
**Date:** 2025-04-22
