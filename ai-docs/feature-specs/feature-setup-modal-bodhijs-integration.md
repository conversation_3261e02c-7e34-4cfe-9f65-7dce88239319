# Setup Modal Integration with Bodhi-JS - REVISED PLAN

## Overview

This document outlines the integration plan for incorporating the standalone `setup-modal` React application into the `bodhi-js` library's onboarding system. Based on feedback, we will **completely replace** the existing onboarding implementation with a new one modeled after the `test-iframe-srcdoc` example, with no backwards compatibility concerns.

## Current State Analysis

### Setup Modal (Source)
- **Location**: `setup-modal/` directory
- **Technology**: React 18 + TypeScript + Tailwind CSS + Vite
- **Build Output**: Single self-contained HTML file (`dist/index.html`)
- **Architecture**: Complete iframe-embeddable application with postMessage communication
- **State Management**: Sophisticated `SetupState` with discriminated unions for extension/server states
- **UI Features**: Step-by-step wizard, platform detection, error handling, GitHub issue integration

### Bodhi-JS Onboarding (Target)
- **Location**: `bodhi-js/src/onboarding/` directory
- **Current Implementation**: Basic HTML template with `OnboardingContentGenerator`
- **Architecture**: iframe srcdoc approach with `BodhiOnboardingModal` class
- **State Management**: Simple `BodhiPlatformState` interface
- **Integration Point**: `content.ts` imports `modal.html?raw` (currently failing)

## Integration Challenges Identified

### 1. Type System Incompatibility

**Setup Modal Types (Complex)**:
```typescript
interface SetupState {
  source: 'modal' | 'parent';
  extension: ExtensionState; // Discriminated union with detailed error codes
  server: ServerState;       // Discriminated union with detailed error codes
  env: EnvState;            // Browser/OS detection
  browsers: Browser[];      // Platform configuration arrays
  os: OS[];                 // Platform configuration arrays
}
```
> source: 'modal' | 'parent';
in SetupState is only for internal, as the source is always set by receiver in modal, so we do not have to pass source, and we should have our SetupState without source attribute



**Bodhi-JS Types (Simple)**:
```typescript
interface BodhiPlatformState {
  status: 'ready' | 'setup' | 'error';
  extension: ExtensionState; // Simple status + optional error
  server: ServerState;       // Simple status + optional error
}
```
> we need to scrap the existing, and create a new one based on the test-iframe-srcdoc example. there is no backwards compatability. so feel free to completely delete and design showing of onboarding modal from scratch

### 2. State Mapping Complexity

The setup modal expects rich configuration data (browsers array, OS array, GitHub URLs) that bodhi-js doesn't currently provide.

> we have constant.ts in bodhi-js, where you should put the placeholder variables you need, and will update with final values

### 3. Build Process Integration

Current approach uses simple file copy which fails if source doesn't exist. Need robust build dependency management.

> this works, so we do not have to do more sophisticated approach

### 4. Message Protocol Differences

Setup modal uses specific message types (`modal_out:ready`, `modal_out:refresh`, etc.) that need integration with bodhi-js callback system.

> scrapt the current onboarding screen show with the new approach

## Integration Plan

### Phase 1: Build System Integration

#### 1.1 Enhance Copy Script with Error Handling

**File**: `bodhi-js/package.json`

Replace simple copy with robust script:
```json
{
  "scripts": {
    "copy-modal": "node scripts/copy-modal.js",
    "prebuild": "npm run copy-modal"
  }
}
```
> having copy-modal in prebuild is good. removed npm run clean as that will increase our build time.

**New File**: `bodhi-js/scripts/copy-modal.js`
```javascript
const fs = require('fs');
const path = require('path');

const sourceFile = '../setup-modal/dist/index.html';
const targetFile = 'src/onboarding/modal.html';

// Check if source exists
if (!fs.existsSync(sourceFile)) {
  console.error(`❌ Source file not found: ${sourceFile}`);
  console.error('Please build setup-modal first: cd ../setup-modal && npm run build');
  process.exit(1);
}

// Ensure target directory exists
const targetDir = path.dirname(targetFile);
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Copy file
fs.copyFileSync(sourceFile, targetFile);
console.log(`✅ Copied ${sourceFile} → ${targetFile}`);
```
> this is good as it is going to be multiplatform

#### 1.2 Update Makefile Dependencies

**File**: `bodhi-js/Makefile`

Add setup-modal build dependency:
```makefile
# Build setup-modal as prerequisite
build-modal: ## Build setup-modal dependency
	@echo "Building setup-modal dependency..."
	cd ../setup-modal && npm run build:fast
	@echo "setup-modal built successfully"

# Update main build to depend on modal
build: build-modal ## Build bodhi-js library
	npm run build
	@echo "bodhi-js built successfully"
```

we have build:fast for local development to save time, have the ci config to do the build without cache

### Phase 2: Type System Harmonization

#### 2.1 Create Type Mapping Layer

**New File**: `bodhi-js/src/onboarding/state-mapper.ts`

```typescript
import type { BodhiPlatformState, ExtensionState as BodhiExtensionState, ServerState as BodhiServerState } from '../types';
import { detectBrowser, detectOS, getSystemInfo } from './detection';
import { generateGitHubIssueUrl, getBrowserDownloadUrl, getServerDownloadUrl } from './constants';

// Import setup-modal types (we'll need to make these available)
import type { 
  SetupState, 
  ExtensionState as ModalExtensionState, 
  ServerState as ModalServerState,
  Browser,
  OS,
  EnvState
} from './setup-modal-types';

export class BodhiSetupStateMapper {
  /**
   * Maps BodhiPlatformState to SetupState expected by setup-modal
   */
  static mapToSetupState(bodhiState: BodhiPlatformState): SetupState {
    const browser = detectBrowser();
    const os = detectOS();
    const systemInfo = getSystemInfo();

    return {
      source: 'parent', // remove source, not passed by embedder
      extension: this.mapExtensionState(bodhiState.extension),
      server: this.mapServerState(bodhiState.server),
      env: {
        browser: browser.type as any,
        os: os.type as any
      },
      browsers: this.generateBrowsersConfig(),
      os: this.generateOSConfig()
    };
  }

  private static mapExtensionState(bodhiExt: BodhiExtensionState): ModalExtensionState {
    switch (bodhiExt.status) {
      case 'ready':
        return {
          status: 'ready',
          version: bodhiExt.version || 'unknown', // should not be the case, should have the version
          id: bodhiExt.id || 'unknown' // should not be the case, should have id
        };
      case 'not-installed':
        return {
          status: 'not-installed',
          error: {
            message: bodhiExt.error?.message || 'Extension not installed',
            code: 'ext-not-installed'
          }
        };
      case 'unreachable':
        return {
          status: 'unreachable',
          error: {
            message: bodhiExt.error?.message || 'Extension unreachable',
            code: 'ext-connection-failed'
          }
        };
    }
  }

  private static mapServerState(bodhiServer: BodhiServerState): ModalServerState {
    switch (bodhiServer.status) {
      case 'ready':
        return {
          status: 'ready',
          version: bodhiServer.version || 'unknown', // should not be the case
          url: bodhiServer.url || 'unknown' // should not be the case
        };
      case 'pending-extension-ready':
        return {
          status: 'pending-extension-ready',
          error: {
            message: 'Server waiting for extension',
            code: 'server-pending-ext-ready'
          }
        };
      case 'unreachable':
        return {
          status: 'unreachable',
          error: {
            message: bodhiServer.error?.message || 'Server unreachable',
            code: 'server-conn-refused'
          }
        };
      // ... handle other states
    }
  }

  private static generateBrowsersConfig(): Browser[] {
    return [
      {
        id: 'chrome',
        status: 'supported',
        name: 'Google Chrome',
        extension_url: getBrowserDownloadUrl('chrome') || 'https://chrome.google.com/webstore' // should not be the case, do not have fallback using ||
      },
      {
        id: 'edge',
        status: 'supported', 
        name: 'Microsoft Edge',
        extension_url: getBrowserDownloadUrl('edge') || 'https://microsoftedge.microsoft.com/addons' // should not be the case, do not have fallback using ||
      },
      {
        id: 'firefox',
        status: 'not-supported',
        name: 'Firefox',
        github_issue_url: generateGitHubIssueUrl('unsupportedBrowser', { browserName: 'Firefox' })
      },
      {
        id: 'safari',
        status: 'not-supported',
        name: 'Safari',
        github_issue_url: generateGitHubIssueUrl('unsupportedBrowser', { browserName: 'Safari' })
      }
    ];
  }

  private static generateOSConfig(): OS[] {
    return [
      {
        id: 'macos',
        status: 'supported',
        name: 'macOS',
        download_url: getServerDownloadUrl('macos') || 'https://github.com/BodhiSearch/bodhi-server/releases' // should not be the case, do not have fallback using ||
      },
      {
        id: 'windows', 
        status: 'supported',
        name: 'Windows',
        download_url: getServerDownloadUrl('windows') || 'https://github.com/BodhiSearch/bodhi-server/releases' // should not be the case, do not have fallback using ||
      },
      {
        id: 'linux',
        status: 'not-supported',
        name: 'Linux',
        github_issue_url: generateGitHubIssueUrl('unsupportedOS', { osName: 'Linux' })
      }
    ];
  }
}
```

> have my comments inlined for above
> should have those values, have the lookup method throw error otherwise
> we control the bodhi-js and setup-modal, so this fallback using `||` is not desired, we would rather catch it in development in stead of having `||`

#### 2.2 Extract Setup Modal Types

**New File**: `bodhi-js/src/onboarding/setup-modal-types.ts`

Extract relevant types from setup-modal to avoid circular dependencies:
```typescript
// Re-export key types from setup-modal for use in bodhi-js
// These should be kept in sync with setup-modal/src/lib/types.ts

export type BrowserType = 'chrome' | 'edge' | 'firefox' | 'safari' | 'unknown';
export type OSType = 'macos' | 'windows' | 'linux' | 'unknown';

export interface SetupState {
  source: 'modal' | 'parent'; // should not have source, remove this attribute
  extension: ExtensionState;
  server: ServerState;
  env: EnvState;
  browsers: Browser[];
  os: OS[];
}

// ... (include other necessary types)
```

### Phase 3: Modal Integration Update

#### 3.1 Update Content Generator

**File**: `bodhi-js/src/onboarding/content.ts`

```typescript
import type { BodhiPlatformState } from '../types';
import { BodhiSetupStateMapper } from './state-mapper';
import htmlContent from './modal.html?raw';

export class OnboardingContentGenerator {
  /**
   * Returns the static HTML content from setup-modal
   */
  static generateContent(): string {
    return htmlContent;
  }

  /**
   * Generates setup state for the modal based on bodhi platform state
   */
  static generateSetupState(platformState: BodhiPlatformState) {
    return BodhiSetupStateMapper.mapToSetupState(platformState);
  }
}
```

#### 3.2 Update Modal Class

**File**: `bodhi-js/src/onboarding/modal.ts`

```typescript
import type { BodhiPlatformState } from '../types';
import type { OnboardingModalConfig } from './types';
import { OnboardingContentGenerator } from './content';

export class BodhiOnboardingModal {
  // ... existing code ...

  private updateModalState(): void {
    if (!this.iframeElement?.contentWindow) return;

    // Convert bodhi state to setup-modal format
    const setupState = OnboardingContentGenerator.generateSetupState(this.platformState);
    
    this.iframeElement.contentWindow.postMessage({
      type: 'modal:state',
      data: setupState
    }, '*');
  }

  private handleMessage = (event: MessageEvent): void => {
    if (event.source !== this.iframeElement?.contentWindow) return;

    const { type, data } = event.data;

    switch (type) {
      case 'modal_out:ready':
        this.updateModalState();
        break;
      case 'modal_out:refresh':
        this.callbacks.onRedetection?.();
        break;
      case 'modal_out:complete':
        this.callbacks.onComplete?.(this.platformState);
        this.hide();
        break;
      case 'modal_out:close':
        this.callbacks.onDismiss?.(this.platformState);
        this.hide();
        break;
    }
  };

  // ... rest of existing code ...
}
```

### Phase 4: Testing Integration

#### 4.1 Update Test Applications

**File**: `bodhi-js/tests/onboarding-modal.test.ts`

Update tests to work with new setup-modal:
```typescript
import { test, expect } from 'vitest';
import { BrowserManager } from './test-utils/browser-manager';

test('setup modal displays and responds to state changes', async () => {
  const browserManager = BrowserManager.default(null);
  const context = await browserManager.start();
  const page = await context.newPage();

  // Navigate to test page that uses bodhi-js
  await page.goto('http://localhost:12345');
  
  // Show onboarding modal
  await page.evaluate(() => {
    const platform = new window.BodhiPlatform();
    platform.showOnboarding();
  });

  // Verify modal appears with setup-modal content
  await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).toBeVisible();
  await expect(page.locator('text=Bodhi Platform Setup')).toBeVisible();
  
  // Test step navigation
  await expect(page.locator('[data-testid="step-platform-check"]')).toBeVisible();
  
  await browserManager.stop();
});
```
  // Show onboarding modal
  await page.evaluate(() => {
    const platform = new window.BodhiPlatform();
    platform.showOnboarding();
  });
we do not test using such hacks, instead setup the page and test app to have the modal shown

#### 4.2 Integration Tests

Add tests to verify state mapping and message passing between bodhi-js and setup-modal.

### Phase 5: Documentation Updates

#### 5.1 Update AI Documentation

**File**: `ai-docs/context/setup-modal.md`

Update to reflect the integration with bodhi-js and the state mapping layer.

> setup-modal.md is only derived from setup-modal project, if you are making changes to setup-modal source code, then update the context file

#### 5.2 Update Implementation Guides

Update relevant documentation to show the new integration patterns.

## Implementation Timeline

### Week 1: Build System (Phase 1)
- [ ] Implement robust copy script with error handling
- [ ] Update Makefile with setup-modal dependencies
- [ ] Test build process integration

### Week 2: Type Mapping (Phase 2)
- [ ] Create state mapper with comprehensive type conversion
- [ ] Extract and sync setup-modal types
- [ ] Implement browser/OS configuration generation

### Week 3: Modal Integration (Phase 3)
- [ ] Update content generator to use setup-modal
- [ ] Modify modal class for new message protocol
- [ ] Test iframe communication

### Week 4: Testing & Polish (Phase 4-5)
- [ ] Update existing tests
- [ ] Add integration tests
- [ ] Update documentation
- [ ] Performance testing and optimization

## Risk Assessment

### High Risk
- **Type System Complexity**: Complex mapping between different state models
- **Message Protocol**: Ensuring reliable communication between iframe and parent

> the setup-modal project is quite stable and well designed
> we do not have a release out for which we need fallback or backwards compatability
> feel free to change things in bodhi-js project to remove complexity and have homogenity between setup-modal and bodhi-js 

### Medium Risk
- **Build Dependencies**: Managing build order between setup-modal and bodhi-js
- **Performance**: Large HTML payload in iframe srcdoc

### Low Risk
- **UI Consistency**: Setup-modal is already well-tested
- **Browser Compatibility**: Both systems use standard web APIs

## Success Criteria

1. **Functional**: Bodhi-js successfully embeds and communicates with setup-modal
2. **Type Safety**: All state conversions are type-safe and comprehensive
3. **Build Process**: Reliable build with proper error handling for missing dependencies
4. **Testing**: All existing tests pass plus new integration tests
5. **Performance**: Modal load time under 2 seconds
6. **Documentation**: Complete documentation of integration patterns

## Open Questions

1. **State Synchronization**: Should we implement bidirectional state sync or keep it unidirectional?
> setup-modal does update its state, it receives state from parent and displays idempotent ui based on state received, so state is uni-directional which keeps it simpler
1. **Error Boundaries**: How should we handle setup-modal crashes or load failures?
> given iframe srcdoc, i am not sure how we will be able to do that, but do propose how you plan to implement it
2. **Theme Integration**: Should we pass theme preferences from bodhi-js to setup-modal?
> not right now
3. **Caching**: Should we cache the setup-modal HTML to avoid repeated copying?
> no need, very cheap operation

## Next Steps

1. Review this plan with stakeholders
2. Confirm type mapping approach
3. Begin Phase 1 implementation
4. Set up integration testing environment
