# Bodhi.js Test Infrastructure Refactor

## Overview

Refactor the bodhi-js test infrastructure to align with the improvements made in bodhi-browser-ext, creating a polished React-based test application with OAuth integration, streamlined test utilities, and comprehensive integration testing using real Bodhi server instances via app-bindings.

## Domain Context

### Business Rules
- Tests must validate complete integration flow: React App → bodhi-js library → Chrome extension → real Bodhi server
- OAuth flow must include the new request-access step before OAuth authorization
- Test infrastructure should use real Bodhi servers via `@bodhiapp/app-bindings` NAPI bindings
- React test app should demonstrate production-ready usage patterns of bodhi-js library
- All tests should follow black-box testing principles with user-centric workflows

### Domain Entities
- **BodhiJS Library**: NPM package providing JavaScript API for extension communication
- **React Test App**: Production-ready demonstration app using bodhi-js
- **OAuth Flow**: App-to-BodhiApp authentication with request-access + OAuth2 + PKCE
- **Extension Integration**: Communication layer between web apps and Chrome extension
- **Bodhi Server**: Real server instances for authentic integration testing

### Workflow Patterns
1. **Test Setup**: Start Bodhi server → Build extension → Launch React app → Configure extension
2. **OAuth Flow**: Request access → Get resource scope → OAuth authorization → Token exchange → API access
3. **API Testing**: Authenticated requests through bodhi-js → extension → Bodhi server
4. **Integration Validation**: End-to-end user workflows with real browser interactions

## Functional Requirements

### User Stories

**As a developer integrating bodhi-js**, I want a comprehensive React test application so that I can see production-ready usage patterns and OAuth integration examples.

**As a test maintainer**, I want streamlined test utilities that use real Bodhi servers so that tests validate actual integration behavior without complex mocking.

**As a CI/CD pipeline**, I want reliable integration tests that follow the new OAuth flow so that I can validate library functionality across different environments.

### Acceptance Criteria

#### React Test App Enhancement
- [ ] **Modern React Architecture**: Convert test app to use modern React patterns with TypeScript
- [ ] **OAuth Integration**: Implement complete OAuth flow with request-access step
- [ ] **Extension Detection**: Robust extension detection with loading states and error handling
- [ ] **API Testing Interface**: Comprehensive form for testing all bodhi-js methods
- [ ] **User Authentication**: Login/logout functionality with user profile display
- [ ] **Streaming Support**: Interface for testing streaming API requests
- [ ] **Error Handling**: Comprehensive error display and user feedback
- [ ] **Production Styling**: Professional UI using TailwindCSS or similar

#### Test Infrastructure Refactor
- [ ] **App-Bindings Integration**: Replace mock servers with real Bodhi server instances
- [ ] **Simplified Test Utils**: Streamlined utilities focused on Playwright and server management
- [ ] **OAuth Test Patterns**: Test utilities supporting the new OAuth flow
- [ ] **Server Lifecycle**: Proper startup/teardown of Bodhi servers and React app
- [ ] **Extension Management**: Reliable extension loading and configuration
- [ ] **Port Management**: Dynamic port allocation to avoid conflicts
- [ ] **Error Recovery**: Robust error handling and cleanup procedures

#### Test Suite Consolidation
- [ ] **Integration Tests**: Comprehensive tests covering complete user workflows
- [ ] **OAuth Flow Tests**: Validation of request-access → OAuth → API access flow
- [ ] **API Method Tests**: Testing all bodhi-js library methods with real server
- [ ] **Error Scenario Tests**: Testing error conditions and edge cases
- [ ] **Extension Communication**: Tests validating extension message passing
- [ ] **Streaming Tests**: Validation of streaming API functionality
- [ ] **Authentication Tests**: Login/logout and token management scenarios

## Project Integration

### Architecture References
- [Extension Implementation](../../context/bodhi-browser-ext.md) - Chrome extension architecture
- [bodhi-js library](../../context/bodhi-js.md) - Bodhi-js library patterns
- [Extension Message Format](../../context/extension-message-format.md) - Communication protocols

### Existing Patterns
- Follow OAuth patterns from: `bodhi-browser-ext/tests/test-app-oauth/src/oauth.ts`
- Reference server config: `bodhi-browser-ext/tests/bodhi-server-config.ts`
- Use test helpers patterns: `bodhi-browser-ext/tests/test-helpers.ts`
- Follow React patterns from existing: `bodhi-js/tests/test-app/src/`

### Dependencies
- **@bodhiapp/app-bindings**: Real Bodhi server instances for testing
- **React + TypeScript**: Modern React application framework
- **Playwright**: Browser automation for integration testing
- **Vitest**: Test framework and runner
- **TailwindCSS**: Modern styling framework
- **Chrome Extension**: Built extension for integration testing

## Implementation Plan

### Phase 1: React Test App Enhancement
**Objective**: Transform the basic React test app into a production-ready OAuth-enabled application

**Components to Create/Enhance**:
1. **App.tsx**: Main application with routing and authentication state
2. **AuthContext.tsx**: React context for authentication state management
3. **LoginPage.tsx**: OAuth login interface with extension detection
4. **TestPage.tsx**: Comprehensive API testing interface
5. **UserProfile.tsx**: User information display after authentication
6. **ExtensionStatus.tsx**: Extension detection and status display
7. **ApiTestForm.tsx**: Enhanced form for testing all bodhi-js methods
8. **StreamingTest.tsx**: Interface for testing streaming requests
9. **ErrorBoundary.tsx**: Error handling and display component

**Key Features**:
- Extension detection with loading states
- OAuth flow with request-access step
- User authentication state management
- Comprehensive API testing interface
- Streaming request support
- Professional UI with proper error handling

### Phase 2: Test Infrastructure Refactor
**Objective**: Streamline test utilities and integrate app-bindings for real server testing

**Files to Refactor**:
1. **setup.ts**: Global test setup with Bodhi server and React app management
2. **test-utils.ts**: Simplified utilities for Playwright and authentication
3. **bodhi-server-config.ts**: Already aligned, minor updates if needed
4. **test-helpers.ts**: New file with OAuth and extension testing utilities

**Key Changes**:
- Remove mock-llm-server dependencies
- Integrate app-bindings for real Bodhi servers
- Simplify server lifecycle management
- Add OAuth testing utilities
- Improve error handling and cleanup

### Phase 3: Test Suite Enhancement
**Objective**: Create comprehensive integration tests covering all user workflows

**Test Files to Create/Update**:
1. **bodhijs-oauth-integration.test.ts**: Complete OAuth flow testing
2. **bodhijs-api-methods.test.ts**: All library methods with authenticated requests
3. **bodhijs-streaming.test.ts**: Streaming API functionality
4. **bodhijs-error-handling.test.ts**: Error scenarios and edge cases
5. **bodhijs-extension-communication.test.ts**: Extension message passing validation

**Test Scenarios**:
- Complete OAuth flow from login to API access
- All bodhi-js methods with real server responses
- Streaming request handling and data flow
- Error conditions and recovery
- Extension detection and communication
- User authentication state management

## Implementation Progress

### Completion Status
- [ ] React test app architecture design
- [ ] OAuth integration implementation
- [ ] Test infrastructure refactoring
- [ ] Test suite enhancement
- [ ] Integration testing validation
- [ ] Documentation updates

### Current Phase
**Phase**: Planning
**Last Updated**: 2024-01-20
**Next Milestone**: React test app enhancement

### Implementation Notes
This refactor aligns bodhi-js testing with the mature patterns established in bodhi-browser-ext, providing a production-ready React application for demonstrating library usage and comprehensive integration testing with real Bodhi servers.

## AI Development Changelog

### 2024-01-20 - Initial Specification
- **Completed**: Comprehensive analysis of both test infrastructures
- **Approach**: Align bodhi-js with bodhi-browser-ext improvements
- **Next Steps**: Begin React test app enhancement
- **Context**: Focus on production-ready patterns and real server integration

## Technical Architecture

### React Test App Structure
```
tests/test-app/
├── src/
│   ├── components/
│   │   ├── AuthContext.tsx          # Authentication state management
│   │   ├── ExtensionStatus.tsx      # Extension detection and status
│   │   ├── ApiTestForm.tsx          # Comprehensive API testing
│   │   ├── StreamingTest.tsx        # Streaming request interface
│   │   ├── UserProfile.tsx          # User information display
│   │   └── ErrorBoundary.tsx        # Error handling component
│   ├── pages/
│   │   ├── LoginPage.tsx            # OAuth login interface
│   │   ├── TestPage.tsx             # Main testing interface
│   │   └── CallbackPage.tsx         # OAuth callback handling
│   ├── utils/
│   │   ├── oauth.ts                 # OAuth flow utilities
│   │   ├── extension.ts             # Extension interaction utilities
│   │   └── constants.ts             # Configuration constants
│   ├── App.tsx                      # Main application component
│   └── main.tsx                     # Application entry point
├── package.json                     # Dependencies and scripts
└── vite.config.ts                   # Vite configuration
```

### Test Infrastructure Files
```
tests/
├── setup.ts                        # Global test setup and teardown
├── test-utils.ts                    # Playwright and authentication utilities
├── test-helpers.ts                  # OAuth and extension testing helpers
├── bodhi-server-config.ts           # Bodhi server configuration (existing)
└── *.test.ts                        # Integration test files
```

### Key Integration Points

#### OAuth Flow Implementation
The React app will implement the complete OAuth flow:
1. **Extension Detection**: Wait for `window.bodhiext` availability
2. **Request Access**: Call `POST /bodhi/v1/auth/request-access` to get resource scope
3. **OAuth Authorization**: Redirect to OAuth server with proper scopes including resource scope
4. **Token Exchange**: Handle callback and exchange authorization code for tokens
5. **API Access**: Use tokens for authenticated requests through bodhi-js

#### Bodhi-js Integration
The test app will demonstrate all bodhi-js library features:
- Extension detection and initialization
- Standard API requests with authentication
- Streaming API requests
- Error handling and recovery
- Extension communication validation

#### Testing Patterns
Tests will follow black-box principles:
- User-centric workflows (login → test API → logout)
- Real browser interactions via Playwright
- Authentic server responses via app-bindings
- No internal state manipulation or mocking
- Comprehensive error scenario coverage 