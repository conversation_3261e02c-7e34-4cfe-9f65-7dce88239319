# Setup Modal Integration with Bodhi-JS - FINAL PLAN

## Overview

This document outlines the **complete replacement** of the existing bodhi-js onboarding system with a new implementation that integrates the `setup-modal` React application. Based on feedback, we will scrap the existing implementation entirely and create a new one modeled after the successful `test-iframe-srcdoc` pattern.

## Key Decisions from Feedback

1. ✅ **Complete Replacement**: Delete existing onboarding implementation entirely
2. ✅ **No Backwards Compatibility**: Free to change bodhi-js types and interfaces
3. ✅ **Model After test-iframe-srcdoc**: Use proven working pattern as template
4. ✅ **Remove Source Attribute**: Don't pass `source` field to setup-modal
5. ✅ **Use Constants**: Add platform config to `bodhi-js/src/onboarding/constants.ts`
6. ✅ **No Fallbacks**: Remove `||` operators, let functions throw errors
7. ✅ **Simple Build**: Keep existing copy-modal approach
8. ✅ **Proper Testing**: Use test applications, not page.evaluate() hacks

## Implementation Plan

### Phase 1: Build System (Simple & Robust)

#### 1.1 Update Package.json Script

**File**: `bodhi-js/package.json`
```json
{
  "scripts": {
    "copy-modal": "node scripts/copy-modal.js",
    "prebuild": "npm run copy-modal"
  }
}
```

#### 1.2 Create Copy Script

**File**: `bodhi-js/scripts/copy-modal.js`
```javascript
const fs = require('fs');
const path = require('path');

const sourceFile = '../setup-modal/dist/index.html';
const targetFile = 'src/onboarding/modal.html';

// Check if source exists
if (!fs.existsSync(sourceFile)) {
  console.error(`❌ Source file not found: ${sourceFile}`);
  console.error('Please build setup-modal first: cd ../setup-modal && npm run build');
  process.exit(1);
}

// Ensure target directory exists
const targetDir = path.dirname(targetFile);
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Copy file
fs.copyFileSync(sourceFile, targetFile);
console.log(`✅ Copied ${sourceFile} → ${targetFile}`);
```

#### 1.3 Update Makefile

**File**: `bodhi-js/Makefile`
```makefile
# Build setup-modal as prerequisite
build-modal: ## Build setup-modal dependency
	@echo "Building setup-modal dependency..."
	cd ../setup-modal && npm run build:fast
	@echo "setup-modal built successfully"

# Update main build to depend on modal
build: build-modal ## Build bodhi-js library
	npm run build
	@echo "bodhi-js built successfully"
```

### Phase 2: Platform Configuration Constants

#### 2.1 Extend Constants File

**File**: `bodhi-js/src/onboarding/constants.ts`

Add platform configuration (no fallbacks):
```typescript
/**
 * Platform configuration for setup modal
 */
export const PLATFORM_CONFIG = {
  browsers: [
    {
      id: 'chrome',
      status: 'supported',
      name: 'Google Chrome',
      extension_url: 'https://chrome.google.com/webstore/detail/bodhi-extension/actual-extension-id'
    },
    {
      id: 'edge',
      status: 'supported', 
      name: 'Microsoft Edge',
      extension_url: 'https://microsoftedge.microsoft.com/addons/detail/bodhi-extension/actual-extension-id'
    },
    {
      id: 'firefox',
      status: 'not-supported',
      name: 'Firefox',
      github_issue_url: 'https://github.com/BodhiSearch/bodhi-browser/issues/firefox-support'
    },
    {
      id: 'safari',
      status: 'not-supported',
      name: 'Safari', 
      github_issue_url: 'https://github.com/BodhiSearch/bodhi-browser/issues/safari-support'
    }
  ],
  os: [
    {
      id: 'macos',
      status: 'supported',
      name: 'macOS',
      download_url: 'https://github.com/BodhiSearch/bodhi-server/releases/download/latest/bodhi-server-macos.dmg'
    },
    {
      id: 'windows',
      status: 'supported',
      name: 'Windows',
      download_url: 'https://github.com/BodhiSearch/bodhi-server/releases/download/latest/bodhi-server-windows.exe'
    },
    {
      id: 'linux',
      status: 'not-supported',
      name: 'Linux',
      github_issue_url: 'https://github.com/BodhiSearch/bodhi-browser/issues/linux-support'
    }
  ]
} as const;

/**
 * Get browser configuration by ID (throws if not found)
 */
export function getBrowserConfig(browserId: string) {
  const browser = PLATFORM_CONFIG.browsers.find(b => b.id === browserId);
  if (!browser) {
    throw new Error(`Unsupported browser: ${browserId}`);
  }
  return browser;
}

/**
 * Get OS configuration by ID (throws if not found)
 */
export function getOSConfig(osId: string) {
  const os = PLATFORM_CONFIG.os.find(o => o.id === osId);
  if (!os) {
    throw new Error(`Unsupported OS: ${osId}`);
  }
  return os;
}

/**
 * Get browser extension URL (throws if not supported)
 */
export function getBrowserDownloadUrl(browserType: string): string {
  const config = getBrowserConfig(browserType);
  if (config.status !== 'supported') {
    throw new Error(`Browser ${browserType} is not supported`);
  }
  return (config as any).extension_url;
}

/**
 * Get server download URL (throws if not supported)
 */
export function getServerDownloadUrl(osType: string): string {
  const config = getOSConfig(osType);
  if (config.status !== 'supported') {
    throw new Error(`OS ${osType} is not supported`);
  }
  return (config as any).download_url;
}
```

### Phase 3: New Type System (Aligned with Setup Modal)

#### 3.1 Create Setup State Types

**File**: `bodhi-js/src/onboarding/setup-state-types.ts`

```typescript
// Types aligned with setup-modal (without source field)
export type BrowserType = 'chrome' | 'edge' | 'firefox' | 'safari' | 'unknown';
export type OSType = 'macos' | 'windows' | 'linux' | 'unknown';

export interface EnvState {
  os: OSType;
  browser: BrowserType;
}

export interface SupportedBrowser {
  id: BrowserType;
  status: 'supported';
  name: string;
  extension_url: string;
}

export interface NotSupportedBrowser {
  id: BrowserType;
  status: 'not-supported';
  name: string;
  github_issue_url?: string;
}

export type Browser = SupportedBrowser | NotSupportedBrowser;

export interface SupportedOS {
  id: OSType;
  status: 'supported';
  name: string;
  download_url: string;
}

export interface NotSupportedOS {
  id: OSType;
  status: 'not-supported';
  name: string;
  github_issue_url?: string;
}

export type OS = SupportedOS | NotSupportedOS;

// Extension state (discriminated union)
export interface ExtensionStateReady {
  status: 'ready';
  version: string;
  id: string;
}

export interface ExtensionStateNotReady {
  status: 'unreachable' | 'not-installed' | 'unsupported';
  error: {
    message: string;
    code: string;
  };
}

export type ExtensionState = ExtensionStateReady | ExtensionStateNotReady;

// Server state (discriminated union)
export interface ServerStateReady {
  status: 'ready';
  version: string;
  url: string;
}

export interface ServerStateReachable {
  status: 'setup' | 'resource-admin';
  version: string;
  url: string;
  error: {
    message: string;
    code: string;
  };
}

export interface ServerStatePending {
  status: 'pending-extension-ready';
  error: {
    message: string;
    code: string;
  };
}

export interface ServerStateUnreachable {
  status: 'unreachable';
  error: {
    message: string;
    code: string;
  };
}

export interface ServerStateError {
  status: 'error';
  error: {
    message: string;
    code: string;
  };
}

export type ServerState = ServerStateReady | ServerStateReachable | ServerStatePending | ServerStateUnreachable | ServerStateError;

// Main setup state (no source field)
export interface SetupState {
  extension: ExtensionState;
  server: ServerState;
  env: EnvState;
  browsers: Browser[];
  os: OS[];
}
```

#### 3.2 Replace BodhiPlatformState

**File**: `bodhi-js/src/types.ts`

Replace the existing BodhiPlatformState with setup-modal compatible version:
```typescript
// Import from setup state types
import type { ExtensionState, ServerState } from './onboarding/setup-state-types';

/**
 * Platform state compatible with setup-modal
 */
export interface BodhiPlatformState {
  extension: ExtensionState;
  server: ServerState;
}

// Re-export types
export type { ExtensionState, ServerState } from './onboarding/setup-state-types';
```

### Phase 4: State Mapping and Modal Implementation

#### 4.1 Create State Generator

**File**: `bodhi-js/src/onboarding/state-generator.ts`

```typescript
import type { BodhiPlatformState } from '../types';
import type { SetupState } from './setup-state-types';
import { detectBrowser, detectOS } from './detection';
import { PLATFORM_CONFIG } from './constants';

export class SetupStateGenerator {
  /**
   * Generate SetupState for the modal from BodhiPlatformState
   */
  static generateSetupState(bodhiState: BodhiPlatformState): SetupState {
    const browser = detectBrowser();
    const os = detectOS();

    return {
      extension: bodhiState.extension,
      server: bodhiState.server,
      env: {
        browser: browser.type as any,
        os: os.type as any
      },
      browsers: PLATFORM_CONFIG.browsers as any[],
      os: PLATFORM_CONFIG.os as any[]
    };
  }
}
```

#### 4.2 Create New Modal Class (Based on test-iframe-srcdoc)

**File**: `bodhi-js/src/onboarding/modal.ts`

```typescript
import type { BodhiPlatformState } from '../types';
import { SetupStateGenerator } from './state-generator';
import modalHtml from './modal.html?raw';

export interface OnboardingModalCallbacks {
  onComplete?: (state: BodhiPlatformState) => void;
  onDismiss?: (state: BodhiPlatformState) => void;
  onRedetection?: () => void;
}

export interface OnboardingModalConfig {
  dismissible?: boolean;
  callbacks?: OnboardingModalCallbacks;
}

/**
 * Onboarding modal implementation based on test-iframe-srcdoc pattern
 */
export class BodhiOnboardingModal {
  private modalElement: HTMLElement | null = null;
  private iframeElement: HTMLIFrameElement | null = null;
  private platformState: BodhiPlatformState;
  private config: OnboardingModalConfig;
  private callbacks: OnboardingModalCallbacks;
  private isVisible = false;

  constructor(platformState: BodhiPlatformState, config: OnboardingModalConfig = {}) {
    this.platformState = platformState;
    this.config = config;
    this.callbacks = config.callbacks || {};
  }

  show(): void {
    if (this.isVisible) return;

    this.createModal();
    this.setupEventListeners();
    this.isVisible = true;
  }

  hide(): void {
    if (!this.isVisible) return;

    this.cleanup();
    this.isVisible = false;
  }

  updateState(platformState: BodhiPlatformState): void {
    this.platformState = platformState;
    this.sendStateToModal();
  }

  private createModal(): void {
    // Create modal overlay
    this.modalElement = document.createElement('div');
    this.modalElement.setAttribute('data-testid', 'onboarding-modal-overlay');
    this.modalElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      box-sizing: border-box;
    `;

    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.style.cssText = `
      background: white;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
      width: 100%;
      max-width: 800px;
      max-height: 90vh;
      overflow: hidden;
      position: relative;
    `;

    // Create iframe
    this.iframeElement = document.createElement('iframe');
    this.iframeElement.style.cssText = `
      width: 100%;
      height: 600px;
      border: none;
      display: block;
    `;

    // Set sandbox attributes for security
    this.iframeElement.sandbox.add('allow-scripts', 'allow-same-origin');
    this.iframeElement.srcdoc = modalHtml;

    // Handle iframe load
    this.iframeElement.addEventListener('load', () => {
      setTimeout(() => {
        this.sendStateToModal();
      }, 100);
    });

    // Assemble modal
    modalContainer.appendChild(this.iframeElement);
    this.modalElement.appendChild(modalContainer);
    document.body.appendChild(this.modalElement);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  private setupEventListeners(): void {
    if (!this.modalElement) return;

    // Handle messages from iframe
    const handleMessage = (event: MessageEvent) => {
      if (!event.data?.type?.startsWith('modal_out:')) return;
      if (event.source !== this.iframeElement?.contentWindow) return;

      const { type, data } = event.data;

      switch (type) {
        case 'modal_out:ready':
          this.sendStateToModal();
          break;
        case 'modal_out:refresh':
          this.callbacks.onRedetection?.();
          break;
        case 'modal_out:complete':
          this.callbacks.onComplete?.(this.platformState);
          this.hide();
          break;
        case 'modal_out:close':
          this.callbacks.onDismiss?.(this.platformState);
          this.hide();
          break;
      }
    };

    window.addEventListener('message', handleMessage);

    // Handle backdrop click
    if (this.config.dismissible !== false) {
      this.modalElement.addEventListener('click', (e) => {
        if (e.target === this.modalElement) {
          this.callbacks.onDismiss?.(this.platformState);
          this.hide();
        }
      });
    }

    // Handle escape key
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && this.config.dismissible !== false) {
        this.callbacks.onDismiss?.(this.platformState);
        this.hide();
      }
    };
    document.addEventListener('keydown', handleKeyDown);

    // Store cleanup function
    this.modalElement.dataset.cleanup = 'true';
  }

  private sendStateToModal(): void {
    if (!this.iframeElement?.contentWindow) return;

    const setupState = SetupStateGenerator.generateSetupState(this.platformState);

    this.iframeElement.contentWindow.postMessage({
      type: 'modal:state',
      data: setupState
    }, '*');
  }

  private cleanup(): void {
    if (this.modalElement) {
      document.body.removeChild(this.modalElement);
      this.modalElement = null;
    }

    // Restore body scroll
    document.body.style.overflow = '';

    // Remove event listeners
    window.removeEventListener('message', this.setupEventListeners);
    document.removeEventListener('keydown', this.setupEventListeners);

    this.iframeElement = null;
  }
}
```

### Phase 5: Update Core Integration

#### 5.1 Update BodhiPlatform Class

**File**: `bodhi-js/src/core.ts`

Update the showOnboarding method:
```typescript
import { BodhiOnboardingModal } from './onboarding/modal';

export class BodhiPlatform {
  // ... existing code ...

  /**
   * Shows the onboarding modal
   */
  showOnboarding(config: OnboardingModalConfig = {}): void {
    if (!this.currentState) {
      throw new Error('Platform must be initialized before showing onboarding');
    }

    // Create modal with callbacks
    this.onboardingModal = new BodhiOnboardingModal(this.currentState, {
      ...config,
      callbacks: {
        onComplete: (state) => {
          config.callbacks?.onComplete?.(state);
        },
        onDismiss: (state) => {
          config.callbacks?.onDismiss?.(state);
        },
        onRedetection: () => {
          // Re-initialize platform
          this.initialize().then(newState => {
            this.onboardingModal?.updateState(newState);
          });
          config.callbacks?.onRedetection?.();
        }
      }
    });

    this.onboardingModal.show();
  }
}
```

### Phase 6: Testing Implementation

#### 6.1 Add Onboarding Modal Testing to bodhijs-test-app-react

Instead of creating a new test application, we'll integrate onboarding modal testing into the existing `bodhijs-test-app-react` application with simplified controls similar to `test-iframe-srcdoc`.

#### 6.2 Create Onboarding Modal Test Page

**File**: `bodhi-js/tests/bodhijs-test-app-react/src/pages/OnboardingTestPage.tsx`

```typescript
import React, { useState } from 'react';
import { BodhiPlatform } from '@bodhiapp/bodhijs';
import type { ExtensionState, ServerState } from '@bodhiapp/bodhijs';

type BrowserType = 'chrome' | 'edge' | 'firefox' | 'safari' | 'unknown';
type OSType = 'macos' | 'windows' | 'linux' | 'unknown';

interface TestState {
  browser: BrowserType;
  os: OSType;
  extensionStatus: 'ready' | 'not-installed' | 'unreachable' | 'unsupported';
  serverStatus: 'ready' | 'setup' | 'resource-admin' | 'unreachable' | 'error' | 'pending-extension-ready';
}

function OnboardingTestPage() {
  const [testState, setTestState] = useState<TestState>({
    browser: 'chrome',
    os: 'macos',
    extensionStatus: 'not-installed',
    serverStatus: 'pending-extension-ready'
  });

  const [showModal, setShowModal] = useState(false);
  const [messages, setMessages] = useState<string[]>([]);
  const [platform, setPlatform] = useState<BodhiPlatform | null>(null);

  const logMessage = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setMessages(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const generateMockPlatformState = () => {
    // Generate extension state
    const extension: ExtensionState = testState.extensionStatus === 'ready' 
      ? {
          status: 'ready',
          version: '1.0.0',
          id: 'mock-extension-id'
        }
      : {
          status: testState.extensionStatus,
          error: {
            message: getExtensionErrorMessage(testState.extensionStatus),
            code: getExtensionErrorCode(testState.extensionStatus)
          }
        };

    // Generate server state
    const server: ServerState = testState.serverStatus === 'ready'
      ? {
          status: 'ready',
          version: '1.0.0',
          url: 'http://localhost:1135'
        }
      : ['setup', 'resource-admin'].includes(testState.serverStatus)
      ? {
          status: testState.serverStatus as any,
          version: '1.0.0',
          url: 'http://localhost:1135',
          error: {
            message: getServerErrorMessage(testState.serverStatus),
            code: getServerErrorCode(testState.serverStatus)
          }
        }
      : testState.serverStatus === 'pending-extension-ready'
      ? {
          status: 'pending-extension-ready',
          error: {
            message: 'Server waiting for extension',
            code: 'server-pending-ext-ready'
          }
        }
      : {
          status: testState.serverStatus as any,
          error: {
            message: getServerErrorMessage(testState.serverStatus),
            code: getServerErrorCode(testState.serverStatus)
          }
        };

    return { extension, server };
  };

  const getExtensionErrorMessage = (status: string) => {
    switch (status) {
      case 'not-installed': return 'Extension is not installed/or not detected';
      case 'unreachable': return 'Could not connect to extension';
      case 'unsupported': return 'Extension version is unsupported';
      default: return 'Unknown extension error';
    }
  };

  const getExtensionErrorCode = (status: string) => {
    switch (status) {
      case 'not-installed': return 'ext-not-installed';
      case 'unreachable': return 'ext-connection-failed';
      case 'unsupported': return 'ext-unsupported-version';
      default: return 'ext-unknown-error';
    }
  };

  const getServerErrorMessage = (status: string) => {
    switch (status) {
      case 'setup': return 'Server requires initial setup';
      case 'resource-admin': return 'Server requires resource configuration';
      case 'unreachable': return 'Server connection refused';
      case 'error': return 'Server encountered an error';
      default: return 'Unknown server error';
    }
  };

  const getServerErrorCode = (status: string) => {
    switch (status) {
      case 'setup': return 'server-in-setup-status';
      case 'resource-admin': return 'server-in-admin-status';
      case 'unreachable': return 'server-conn-refused';
      case 'error': return 'server-unexpected-error';
      default: return 'server-unknown-error';
    }
  };

  const handleSendState = () => {
    // This simulates sending state to modal (modal will be updated automatically)
    logMessage('State sent to modal');
  };

  const handleShowModal = () => {
    if (!platform) {
      // Create mock platform with current test state
      const mockPlatform = new BodhiPlatform();
      
      // Override the platform state with our mock state
      const mockState = generateMockPlatformState();
      (mockPlatform as any).currentState = mockState;
      
      setPlatform(mockPlatform);
    }

    const mockState = generateMockPlatformState();
    (platform as any).currentState = mockState;

    platform?.showOnboarding({
      dismissible: true,
      callbacks: {
        onComplete: (state) => {
          logMessage('✅ Setup completed successfully!');
          setShowModal(false);
        },
        onDismiss: (state) => {
          logMessage('❌ Modal closed by user');
          setShowModal(false);
        },
        onRedetection: () => {
          logMessage('🔄 Modal requested refresh - sending current simulation state');
          // Update the platform state with current test state
          const newMockState = generateMockPlatformState();
          (platform as any).currentState = newMockState;
        }
      }
    });

    setShowModal(true);
    logMessage('🔄 Launching modal with current state');
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>Bodhi-JS Onboarding Modal Test</h1>
      
      {/* Simulation Controls */}
      <div style={{ marginBottom: '2rem', padding: '1rem', border: '1px solid #ccc', borderRadius: '8px' }}>
        <h2>Simulation Controls</h2>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
          <div>
            <label>
              Browser:
              <select 
                data-testid="browser-select"
                value={testState.browser}
                onChange={(e) => setTestState(prev => ({ ...prev, browser: e.target.value as BrowserType }))}
                style={{ marginLeft: '0.5rem', padding: '0.25rem' }}
              >
                <option value="chrome">Chrome</option>
                <option value="edge">Edge</option>
                <option value="firefox">Firefox</option>
                <option value="safari">Safari</option>
                <option value="unknown">Unknown</option>
              </select>
            </label>
          </div>
          
          <div>
            <label>
              OS:
              <select
                data-testid="os-select"
                value={testState.os}
                onChange={(e) => setTestState(prev => ({ ...prev, os: e.target.value as OSType }))}
                style={{ marginLeft: '0.5rem', padding: '0.25rem' }}
              >
                <option value="macos">macOS</option>
                <option value="windows">Windows</option>
                <option value="linux">Linux</option>
                <option value="unknown">Unknown</option>
              </select>
            </label>
          </div>
          
          <div>
            <label>
              Extension Status:
              <select
                data-testid="extension-status"
                value={testState.extensionStatus}
                onChange={(e) => setTestState(prev => ({ ...prev, extensionStatus: e.target.value as any }))}
                style={{ marginLeft: '0.5rem', padding: '0.25rem' }}
              >
                <option value="ready">Ready</option>
                <option value="not-installed">Not Installed</option>
                <option value="unreachable">Unreachable</option>
                <option value="unsupported">Unsupported</option>
              </select>
            </label>
          </div>
          
          <div>
            <label>
              Server Status:
              <select
                data-testid="server-status"
                value={testState.serverStatus}
                onChange={(e) => setTestState(prev => ({ ...prev, serverStatus: e.target.value as any }))}
                style={{ marginLeft: '0.5rem', padding: '0.25rem' }}
              >
                <option value="ready">Ready</option>
                <option value="setup">Setup Required</option>
                <option value="resource-admin">Resource Admin</option>
                <option value="unreachable">Unreachable</option>
                <option value="error">Error</option>
                <option value="pending-extension-ready">Pending Extension</option>
              </select>
            </label>
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: '1rem' }}>
          <button onClick={handleSendState} style={{ padding: '0.5rem 1rem' }}>
            Send State
          </button>
          <button onClick={handleShowModal} style={{ padding: '0.5rem 1rem' }}>
            Launch Modal
          </button>
        </div>
      </div>

      {/* Current State Display */}
      <div style={{ marginBottom: '2rem', padding: '1rem', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>Current Test State</h3>
        <pre style={{ fontSize: '0.9rem' }}>
          {JSON.stringify(testState, null, 2)}
        </pre>
      </div>

      {/* Message Log */}
      <div style={{ padding: '1rem', backgroundColor: '#000', color: '#0f0', borderRadius: '8px', fontFamily: 'monospace' }}>
        <h3 style={{ color: '#0f0', margin: '0 0 1rem 0' }}>Message Log</h3>
        <div style={{ height: '200px', overflowY: 'auto' }}>
          {messages.map((msg, index) => (
            <div key={index}>{msg}</div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default OnboardingTestPage;
```

#### 6.3 Update App Router

**File**: `bodhi-js/tests/bodhijs-test-app-react/src/App.tsx`

```typescript
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './pages/LandingPage';
import CallbackPage from './pages/CallbackPage';
import ApiTestPage from './pages/ApiTestPage';
import OnboardingTestPage from './pages/OnboardingTestPage';

function App() {
  return (
    <Router>
      <div className="App" style={{ padding: '2rem', textAlign: 'center' }}>
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/callback" element={<CallbackPage />} />
          <Route path="/api-test" element={<ApiTestPage />} />
          <Route path="/onboarding-test" element={<OnboardingTestPage />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
```

#### 6.4 Update Navigation in Landing Page

**File**: `bodhi-js/tests/bodhijs-test-app-react/src/pages/LandingPage.tsx`

Add navigation button to the onboarding test page:

```typescript
// Add this button in the appropriate section
<button 
  onClick={() => window.location.href = '/onboarding-test'}
  style={{ 
    padding: '0.75rem 1.5rem', 
    fontSize: '1rem',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    margin: '0.5rem'
  }}
>
  Test Onboarding Modal
</button>
```

#### 6.5 Create Comprehensive Test Cases

**File**: `bodhi-js/tests/onboarding-modal-integration.test.ts`

```typescript
import { Browser, chromium, Page } from 'playwright';
import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, test } from 'vitest';

describe('Onboarding Modal Integration Tests', () => {
  let browser: Browser;
  let testUrl: string;
  let page: Page;

  beforeAll(async () => {
    testUrl = globalThis.TEST_URL;
    browser = await chromium.launch({ headless: process.env.CI ? true : false });
  });

  afterAll(async () => {
    await browser?.close();
  });

  beforeEach(async () => {
    page = await browser.newPage();
  });

  afterEach(async () => {
    await page?.close();
  });

  // Test scenarios based on state combinations (adapted from test-iframe-srcdoc)
  const testScenarios = [
    {
      name: 'supported platform, extension not-installed, server pending -> Extension Setup',
      os: 'macos',
      browser: 'chrome',
      extensionStatus: 'not-installed',
      serverStatus: 'pending-extension-ready',
      expectedStep: 'step-extension-setup',
      expectedText: 'Extension is not installed'
    },
    {
      name: 'unsupported OS, supported browser -> Platform Check',
      os: 'linux',
      browser: 'chrome',
      extensionStatus: 'ready',
      serverStatus: 'ready',
      expectedStep: 'step-platform-check',
      expectedText: 'Currently Supported Platforms'
    },
    {
      name: 'supported OS, unsupported browser -> Platform Check',
      os: 'macos',
      browser: 'firefox',
      extensionStatus: 'ready',
      serverStatus: 'ready',
      expectedStep: 'step-platform-check',
      expectedText: 'Currently Supported Platforms'
    },
    {
      name: 'supported platform, extension ready, server not-ready -> Server Setup',
      os: 'macos',
      browser: 'chrome',
      extensionStatus: 'ready',
      serverStatus: 'unreachable',
      expectedStep: 'step-server-setup',
      expectedText: 'Server connection refused'
    },
    {
      name: 'supported platform, extension ready, server ready -> Complete',
      os: 'macos',
      browser: 'chrome',
      extensionStatus: 'ready',
      serverStatus: 'ready',
      expectedStep: 'step-complete',
      expectedText: 'All Systems Ready!'
    }
  ];

  test.each(testScenarios)('should navigate to correct step: $name', async (scenario) => {
    await page.goto(`${testUrl}/onboarding-test`);
    await page.waitForSelector('h1');

    // Set the state using simulation controls
    await page.locator('select[data-testid="os-select"]').selectOption(scenario.os);
    await page.locator('select[data-testid="browser-select"]').selectOption(scenario.browser);
    await page.locator('select[data-testid="extension-status"]').selectOption(scenario.extensionStatus);
    await page.locator('select[data-testid="server-status"]').selectOption(scenario.serverStatus);

    // Launch modal
    await page.locator('button').filter({ hasText: 'Launch Modal' }).click();
    await page.waitForSelector('[data-testid="onboarding-modal-overlay"]');

    // Get iframe and wait for loading indicator to disappear
    const iframe = page.frameLocator('iframe');
    await iframe.locator('[data-testid="loading-indicator"]').waitFor({ state: 'hidden' });

    // Wait for step indicators to be present
    await iframe.locator('[data-testid^="step-"]').first().waitFor();

    // Give a moment for step navigation to complete
    await page.waitForTimeout(100);

    // Verify the expected step content is displayed
    const stepContentArea = iframe.locator('.mt-6.overflow-y-auto.h-\\[400px\\]');
    const actualStepText = await stepContentArea.textContent();
    expect(actualStepText).toContain(scenario.expectedText);

    // Verify correct step indicator is active
    const currentStepElement = await iframe.locator('[data-testid^="step-"]').evaluateAll(elements => {
      for (const element of elements) {
        const circleDiv = element.querySelector('div[class*="ring-2"][class*="ring-blue-300"]');
        if (circleDiv) {
          return element.getAttribute('data-testid');
        }
      }
      return null;
    });

    expect(currentStepElement).toBe(scenario.expectedStep);
  });

  test('should handle modal close correctly', async () => {
    await page.goto(`${testUrl}/onboarding-test`);
    await page.waitForSelector('h1');

    // Launch modal
    await page.locator('button').filter({ hasText: 'Launch Modal' }).click();
    await page.waitForSelector('[data-testid="onboarding-modal-overlay"]');

    // Get iframe and wait for loading indicator to disappear
    const iframe = page.frameLocator('iframe');
    await iframe.locator('[data-testid="loading-indicator"]').waitFor({ state: 'hidden' });

    // Close modal using the close button inside the iframe
    await iframe.locator('[data-testid="close-button"]').click();

    // Wait for modal to disappear
    await page.waitForSelector('[data-testid="onboarding-modal-overlay"]', { state: 'hidden' });

    // Check message log for close message
    const messageLog = await page.locator('div[style*="background: #000"]').textContent();
    expect(messageLog).toContain('Modal closed by user');
  });

  test('should close modal when clicking Continue to Webpage button', async () => {
    await page.goto(`${testUrl}/onboarding-test`);
    await page.waitForSelector('h1');

    // Set up complete state (extension ready, server ready)
    await page.locator('select[data-testid="extension-status"]').selectOption('ready');
    await page.locator('select[data-testid="server-status"]').selectOption('ready');

    // Launch modal
    await page.locator('button').filter({ hasText: 'Launch Modal' }).click();
    await page.waitForSelector('[data-testid="onboarding-modal-overlay"]');

    // Get iframe and wait for loading indicator to disappear
    const iframe = page.frameLocator('iframe');
    await iframe.locator('[data-testid="loading-indicator"]').waitFor({ state: 'hidden' });

    // Verify modal is in complete state
    await iframe.getByText('All Systems Ready!').waitFor();

    // Click Continue to Webpage button
    await iframe.getByText('Continue to Webpage').click();

    // Wait for modal to disappear
    await page.waitForSelector('[data-testid="onboarding-modal-overlay"]', { state: 'hidden' });

    // Check message log for completion message
    const messageLog = await page.locator('div[style*="background: #000"]').textContent();
    expect(messageLog).toContain('Setup completed successfully');
  });

  test('should handle refresh correctly', async () => {
    await page.goto(`${testUrl}/onboarding-test`);
    await page.waitForSelector('h1');

    // Launch modal with initial state
    await page.locator('button').filter({ hasText: 'Launch Modal' }).click();
    await page.waitForSelector('[data-testid="onboarding-modal-overlay"]');

    const iframe = page.frameLocator('iframe');
    await iframe.locator('[data-testid="loading-indicator"]').waitFor({ state: 'hidden' });

    // Change state while modal is open
    await page.locator('select[data-testid="extension-status"]').selectOption('ready');
    await page.locator('select[data-testid="server-status"]').selectOption('ready');

    // Click refresh in the modal
    await iframe.locator('[data-testid="refresh-button"]').click();

    // Wait for refresh to process
    await page.waitForTimeout(500);

    // Verify modal updated to complete state
    await iframe.getByText('All Systems Ready!').waitFor();
  });
});
```

#### 6.6 Remove Old Test File

**File**: `bodhi-js/tests/onboarding-modal.test.ts` - Delete this file

The new comprehensive test suite in `onboarding-modal-integration.test.ts` replaces the old basic test file.

## Error Boundary Implementation

For the error boundary question, here's how we can handle setup-modal crashes:

**File**: `bodhi-js/src/onboarding/modal.ts`

```typescript
private setupEventListeners(): void {
  // ... existing code ...

  // Handle iframe errors
  this.iframeElement.addEventListener('error', () => {
    console.error('Setup modal failed to load');
    this.handleModalError('Modal failed to load');
  });

  // Monitor for iframe crashes via timeout
  let modalReadyTimeout: NodeJS.Timeout | null = null;
  
  const handleMessage = (event: MessageEvent) => {
    if (event.data?.type === 'modal_out:ready') {
      if (modalReadyTimeout) {
        clearTimeout(modalReadyTimeout);
        modalReadyTimeout = null;
      }
    }
    // ... rest of message handling
  };

  // Set timeout for modal ready signal
  modalReadyTimeout = setTimeout(() => {
    console.error('Setup modal failed to initialize within timeout');
    this.handleModalError('Modal initialization timeout');
  }, 10000);
}

private handleModalError(error: string): void {
  // Show fallback UI or close modal
  this.hide();
  
  // Notify callback
  this.callbacks.onDismiss?.(this.platformState);
  
  // Could show a simple fallback modal here
  console.error('Modal error:', error);
}
```

## Success Criteria

1. ✅ **Complete replacement** of existing onboarding system
2. ✅ **Type alignment** between bodhi-js and setup-modal
3. ✅ **No fallback logic** - functions throw errors for missing config
4. ✅ **Test-iframe-srcdoc pattern** implementation
5. ✅ **Proper testing** with real test applications
6. ✅ **Build integration** with setup-modal dependency
7. ✅ **Error handling** for modal failures

## Implementation Timeline

- **Week 1**: Build system + constants (Phase 1-2)
- **Week 2**: Type system + state mapping (Phase 3-4)  
- **Week 3**: Modal implementation (Phase 4-5)
- **Week 4**: Testing + error handling (Phase 6)

This plan addresses all your feedback and provides a clean, maintainable integration following proven patterns.
