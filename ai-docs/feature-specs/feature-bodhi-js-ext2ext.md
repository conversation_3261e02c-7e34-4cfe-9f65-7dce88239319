# Feature Specification: bodhi-js/ext2ext API for Extension-to-Extension Communication

## Primary Task
Expose an API in bodhi-js (ext2ext) that allows other Chrome extensions to communicate with the Bodhi Browser Extension, mirroring the API provided to web pages. This enables extension developers to leverage Bodhi LLM capabilities with minimal integration effort and consistent interfaces.

## Overview
Currently, bodhi-js simplifies access to Bodhi Browser Extension APIs for web pages. This feature aims to provide a parallel set of APIs (ext2ext) for other Chrome extensions, matching the web page interface as closely as possible. Internally, most logic will be reused, ensuring consistency and maintainability.

## Interface Inventory (as of @[bodhi-js/src])

### 1. Core Methods
- `isInstalled(): boolean` — Check if Bodhi Browser Extension is installed
- `isServerAvailable(): Promise<boolean>` — Check if the LLM server is reachable
- `ping(): Promise<{ message: string }>` — Ping the extension/server

### 2. Chat API
- `chat.completions.create(params: ChatRequest): Promise<ChatResponse | Stream<ChatCompletionChunk>>` — Create a chat completion (streaming or non-streaming)

### 3. Error Handling
- `BodhiError` class and `ErrorCode` enum for standardized error reporting

### 4. Types
- `ChatRequest`, `ChatResponse`, `ChatCompletionChunk`, `Stream<T>` (see types.ts)

## ext2ext API Design
- Expose an `ext2ext` namespace or class in bodhi-js, matching the above methods and types.
- All methods should work in extension contexts, using `chrome.runtime.sendMessage` or `chrome.runtime.connect` to communicate with the Bodhi Browser Extension.
- The API surface (method names, parameters, return types) should be identical to the web page API, except for any browser context-specific differences.

## Technical Details

### 1. Message Routing
- Use `chrome.runtime.sendMessage` or `chrome.runtime.connect` to send requests from the calling extension to the Bodhi Browser Extension.
- The Bodhi Browser Extension background script should route and handle these requests, invoking the same internal logic as web page requests.
- Responses (including errors) should be returned in a consistent, promise-based format.

### 2. Security
- All incoming requests should be validated for method, parameters, and permissions.
- Log the source extension ID for each API invocation (see feature-track-extension-client.md).

### 3. Error Handling
- Use the same error codes and error classes as the web page API.
- Provide clear error messages for unsupported methods, permission issues, or extension not installed.

### 4. Documentation
- Document the ext2ext API surface, usage examples, and security model.
- Provide a migration guide for extension developers moving from web page to extension context.

## Implementation Checklist
- [ ] Define ext2ext namespace/class in bodhi-js with matching API surface
- [ ] Implement message passing using chrome.runtime APIs
- [ ] Update Bodhi Browser Extension to route and handle ext2ext requests
- [ ] Standardize error handling and types
- [ ] Add documentation and migration guide

## Test Plan
- Unit test: ext2ext API methods mirror web API methods
- Integration test: End-to-end extension-to-extension API calls
- Security test: Only allowlisted extensions can use the API
- Error handling test: Unsupported/invalid requests are handled gracefully
- Documentation test: Examples and migration guide are accurate

## Out of Scope
- Web page API (already implemented)
- Non-Chrome extension environments
