# Feature Specification: Extension-to-Extension Communication

## Primary Task
Enable secure and efficient communication between the Bodhi Browser Extension and other Chrome extensions, allowing them to access LLM capabilities through a standardized API that matches the web interface.

## Overview
While web pages access LLM capabilities through the `bodhijs` library and global object injection, other Chrome extensions need a more direct and secure communication channel. This feature implements extension-to-extension communication using Chrome's built-in messaging APIs, providing both one-time message passing and long-lived connections for streaming responses.

## Technical Details
The implementation uses Chrome's extension messaging APIs:
- `chrome.runtime.sendMessage` for one-time requests ([docs](https://developer.chrome.com/docs/extensions/reference/runtime#method-sendMessage))
- `chrome.runtime.connect` for long-lived streaming connections ([docs](https://developer.chrome.com/docs/extensions/reference/runtime#method-connect))
- Message validation and security checks
- Extension permissions verification ([docs](https://developer.chrome.com/docs/extensions/mv3/permission_warnings))

For more details on extension messaging, refer to:
- [Message Passing Overview](https://developer.chrome.com/docs/extensions/mv3/messaging)
- [Cross-extension Messaging](https://developer.chrome.com/docs/extensions/mv3/messaging#external)
- [Long-lived Connections](https://developer.chrome.com/docs/extensions/mv3/messaging#connect)

## MVP vs Advanced Features

### MVP Features
- ✅ Basic extension-to-extension message passing
- ✅ Support for chat completions API
- ✅ Extension permissions verification
- ✅ Error handling and response formatting
- ✅ Integration with existing streaming support

### Advanced Features (Backlog)
- Connection state monitoring
- Rate limiting per extension
- Advanced error recovery mechanisms
- Enhanced security measures
- Performance optimization and monitoring

## Projects Impacted

### 1. bodhi-js Library
- Add extension mode to bodhijs client
- Implement extension-specific transport layer
- Add TypeScript types for extension messaging
- Update documentation for extension usage

### 2. bodhi-browser-ext (Chrome Extension)
- Add external message handling in background.js
- Implement extension permissions verification
- Add support for long-lived connections
- Add error handling for extension messages

### 3. integration-tests
- Add extension-to-extension test scenarios
- Create test extension for verification
- Add streaming tests for extension communication
- Test error cases and recovery

## Technical Implementation Details

### Message Protocol

#### Request Format
```typescript
interface BodhiExtensionRequest {
  type: "BODHI_REQUEST" | "BODHI_STREAM_REQUEST";
  action: string;
  payload: any;
  requestId?: string;
}
```

#### Response Format
```typescript
interface BodhiExtensionResponse {
  type: "BODHI_RESPONSE" | "BODHI_STREAM_CHUNK" | "BODHI_ERROR";
  requestId?: string;
  data?: any;
  error?: {
    code: string;
    message: string;
  };
}
```

### Communication Methods

1. **Direct Message Passing** ([docs](https://developer.chrome.com/docs/extensions/mv3/messaging#external))
```typescript
// In the client extension
chrome.runtime.sendMessage(BODHI_EXTENSION_ID, {
  type: "BODHI_REQUEST",
  action: "chat.completions.create",
  payload: {
    model: "model-name",
    messages: [{ role: "user", content: "Hello" }]
  }
}, response => {
  if (chrome.runtime.lastError) {
    // Handle error
    return;
  }
  // Process response
});
```

2. **Long-lived Connections for Streaming** ([docs](https://developer.chrome.com/docs/extensions/mv3/messaging#connect))
```typescript
// In the client extension
const port = chrome.runtime.connect(BODHI_EXTENSION_ID, {
  name: "BODHI_CHANNEL"
});

port.onMessage.addListener((message) => {
  // Handle incoming messages
});

port.postMessage({
  type: "BODHI_STREAM_REQUEST",
  action: "chat.completions.create",
  payload: {/* ... */}
});
```

### Security Implementation

1. **Extension Permissions**
Client extensions must declare permission in their manifest ([docs](https://developer.chrome.com/docs/extensions/mv3/manifest/permissions)):
```json
{
  "permissions": [
    "BODHI_EXTENSION_ID"
  ]
}
```

### Error Handling
```typescript
enum BodhiExtensionErrorCode {
  EXTENSION_NOT_FOUND = "EXTENSION_NOT_FOUND",
  UNAUTHORIZED = "UNAUTHORIZED",
  SERVER_ERROR = "SERVER_ERROR",
  INVALID_REQUEST = "INVALID_REQUEST"
}
```

## Implementation Checklist

### bodhi-js Library (MVP)
- [ ] Create extension-specific client implementation
- [ ] Add extension mode to client factory
- [ ] Implement extension transport layer
- [ ] Add TypeScript types for extension messaging
- [ ] Update documentation with extension usage

### bodhi-browser-ext (MVP)
- [ ] Add external message listener in background.js
- [ ] Implement extension permissions verification
- [ ] Add support for streaming connections
- [ ] Add error handling for extension messages

### integration-tests (MVP)
- [x] Create test extension for verification
- [x] Add extension communication tests
- [x] Test streaming functionality
- [x] Test error scenarios

### Advanced Features (Backlog)
- [ ] Add connection monitoring
- [ ] Implement rate limiting
- [ ] Add advanced error recovery
- [ ] Add performance monitoring
- [ ] Optimize message passing

## Example Usage

### Using bodhijs in Extension Mode
```typescript
import { createBodhiClient } from '@bodhiapp/bodhijs/extension';

const bodhi = createBodhiClient({
  mode: 'extension',
  extensionId: BODHI_EXTENSION_ID
});

// Use the same familiar API
const response = await bodhi.chat.completions.create({
  model: "model-name",
  messages: [{ role: "user", content: "Hello" }]
});
```

## Testing and Validation
The feature has been validated through:
1. Unit tests for message handling
2. Integration tests with a test extension (`ext2ext.test.js`)
3. HTML test pages for different scenarios:
   - Extension discovery and ID detection
   - Basic ping communication
   - Chat completion requests
   - Streaming responses
   - Error handling
4. Verification of permissions handling
5. Performance testing of streaming
6. Error handling verification

The test structure provides focused testing for extension-to-extension communication, making it easier to validate specific aspects of the feature.

## Acceptance Criteria
- [x] Extensions can send requests to Bodhi Browser Extension
- [x] Extension permissions are properly enforced
- [x] Streaming responses work correctly between extensions
- [x] Error handling works as expected
- [x] Integration tests pass for all scenarios
- [x] Documentation clearly explains extension usage 