# Feature Specification: API Changes for Resource Server Selection Parameter

## Primary Task
Update the Bodhi Browser Extension APIs and bodhi-js library to support explicit selection of the target LLM resource server for all relevant API calls. Formally define the API for exposing the list of configured servers to clients (web pages and extensions), and ensure backward compatibility.

## Context
- Multiple LLM resource servers can now be configured, named, edited, and managed via the setup dialog (see feature-setup-dialog-resource-discovery.md).
- Clients (web pages/extensions) may wish to select which resource server to use for a given API call.
- The list of available servers (with IDs, endpoints, types, and authentication status) must be exposed to clients.
- If no server is specified, the default server is used (always exactly one default).

## Requirements

### 1. API Parameter for Server Selection
- All relevant Bodhi Browser Extension APIs (proxying LLM calls, token management, etc.) must accept an optional `resourceServerId` parameter.
- bodhi-js must accept and forward this parameter in its API surface (e.g., `chat.completions.create`, `ping`, etc.).
- If the parameter is omitted, the extension routes the request to the default server.
- If the specified server does not exist or is unavailable, return a clear error (e.g., `RESOURCE_SERVER_NOT_FOUND`).

### 2. Exposing Server List to Clients
- The extension exposes a new API (e.g., `bodhiext.getAvailableResourceServers()`):
  - Returns an array of objects, each with:
    - `id` (human-readable, unique)
    - `endpoint` (URL)
    - `type` (e.g., bodhi-app, ollama, lm-studio, generic)
    - `isDefault` (boolean)
    - `isAuthenticated` (boolean)
    - `status` (online/offline/unknown)
  - Accessible to both web pages and extensions via bodhi-js.
- bodhi-js exposes a corresponding method (e.g., `getAvailableResourceServers()`), returning the same data.
- The list is always up-to-date with the extension's current configuration.

### 3. Client Integration and UX
- bodhi-js documentation and example clients must demonstrate:
  - How to query the list of available servers and display them to users (e.g., dropdown, prioritized list, or settings page).
  - How to select a server for API calls by passing the `resourceServerId` parameter.
  - How to handle the default server (top-priority or marked as default in API response).
  - How to handle errors (e.g., server not found, unavailable).
- Example UI/UX patterns:
  - Dropdown or list selection for choosing a server before making an API call.
  - Displaying server type, authentication status, and online/offline status in the UI.
  - Warning banners or icons for unauthenticated servers.
- Security and best practices:aj
  - Always show which server is currently selected/default.
  - Warn users when connecting to unauthenticated servers.
  - Handle errors gracefully and provide actionable feedback to users.
  - Encourage use of secure (authenticated) servers where possible.

### 3. Routing and Backward Compatibility
- The extension routes all proxied requests to the correct backend based on the `resourceServerId`.
- If a client does not specify a server, the default is used (as per management rules).
- Existing clients that do not provide the parameter continue to work as before.

### 4. Error Handling
- If an invalid or unavailable `resourceServerId` is provided, return a clear, actionable error.
- Document all new error codes (e.g., `RESOURCE_SERVER_NOT_FOUND`, `RESOURCE_SERVER_UNAVAILABLE`).

### 5. Documentation
- Update documentation for both the extension and bodhi-js:
  - How to query available servers (with code examples)
  - How to select a server for API calls (with code examples for web and extension clients)
  - Example UI/UX patterns for server selection (dropdown, prioritized list, etc.)
  - Security and error handling best practices
  - Guidance for handling unauthenticated servers in client UIs

## Implementation Checklist
- [ ] Add `resourceServerId` parameter to all relevant extension and bodhi-js APIs
- [ ] Implement server list API in extension and bodhi-js
- [ ] Route requests based on selected server
- [ ] Ensure backward compatibility
- [ ] Add error handling for invalid/unavailable servers
- [ ] Update documentation and usage examples

## Test Plan
- Unit test: API calls with/without `resourceServerId` parameter
- Integration test: Correct routing to selected server
- Error test: Invalid/unavailable server ID handling
- Backward compatibility test: Legacy clients work as before
- API test: Server list API returns accurate, up-to-date data
- UI test: Example clients display server selection UI and handle selection changes
- UX test: Unauthenticated server warnings and error handling in client apps

## Out of Scope
- UI/UX for server selection in client apps (see feature-client-server-selection.md)
- Discovery and management of servers (covered in setup dialog spec)
