
Onboarding.tsx
remove this file, and merge the feature with LandingPage.tsx

App.tsx
will be removing Onboarding.tsx, update the route for same

LandingPage.tsx
we are designing this test app as how a sample app is going to integrate with Bodhi Platform. Here is the new set of requirements for LandingPage.tsx:
- on load of landing page, it is going to initialize bodhi platform using bodhi-js library
- it is going to check if the platform is ready
- if the platform is not ready:
  - it is going to show BodhiPlatform is not setup
  - it is going to show a link to open the onboarding modal
  - on click of modal, call showOnboarding on the bodhi-js library
  - when showing onboarding modal, you will also need to pass the callbacks for onclose
  - onclose callback, the initialize again on the bodhi platform instance
  - check if ready, then proceed
  - check if not ready, show the same error BodhiPlatform is not setup, and show link to open the onboarding modal
- if it is ready, it is going to get the BodhiExtClient for communicating with extension
- it is going to move to show the authentication status
- if user is already authenticated, then it is going to show the user info with logout buttong
- if user is not logged in, it is going to show the Log In button
- on click of log-in, it will continue the existing flow:
  - call /request-access get the scope
  - launch the oauth flow with scope

create subcomponents, modular react code, remove older, not used components


