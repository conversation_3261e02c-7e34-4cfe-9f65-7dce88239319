# Feature Specification: Security and Warnings for Unauthenticated Resource Servers

## Purpose
Ensure users are clearly warned and must explicitly consent before adding or using unauthenticated LLM resource servers. Document the risks and provide best practices for safe usage.

## Context
- Users may add LLM resource servers that do not require authentication (e.g., local OLLAMA, open LM Studio instances).
- Unauthenticated servers can be a security risk if exposed to untrusted networks or if sensitive data is sent.
- The setup dialog already provides basic warnings and requires explicit consent to add such servers.

## Requirements

### 1. Explicit Warning and Consent
- When a user attempts to add an unauthenticated resource server:
  - Display a clear warning (icon + text) explaining the risks of unauthenticated servers.
  - Require the user to explicitly confirm (e.g., checkbox or confirmation dialog) before the server is added.
  - The warning should appear both during discovery and manual entry.
- In the configured server list, unauthenticated servers must be visually marked (e.g., with a warning icon and tooltip).

### 2. Documentation and Best Practices
- Update user documentation to explain:
  - What unauthenticated servers are and the risks involved.
  - Recommendations for safe usage (e.g., only use on trusted networks, avoid sending sensitive data).
  - How to identify unauthenticated servers in the UI.
  - How to remove or restrict access to unauthenticated servers.

### 3. (Optional/Future) Per-Server Access Restrictions
- (Out of scope for MVP, but may be considered in future):
  - Allow users to restrict which web pages/extensions can access each server.
  - UI for setting permissions per server.

## Implementation Checklist
- [ ] Display warning and require explicit consent for unauthenticated servers (discovery and manual entry).
- [ ] Mark unauthenticated servers in the configured server list (icon, tooltip).
- [ ] Update documentation with risks, safe usage, and removal instructions.

## Test Plan
- Test: Warning and consent flow when adding unauthenticated servers (both discovery and manual entry).
- Test: Visual marking of unauthenticated servers in the UI.
- Test: Documentation includes clear guidance and best practices.

## Out of Scope
- Advanced per-server access restrictions (permissions for web pages/extensions).
