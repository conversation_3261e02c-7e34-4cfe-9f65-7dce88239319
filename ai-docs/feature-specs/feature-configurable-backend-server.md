# Feature Specification: Configurable Backend URL

## Primary Task
Enable users to configure the Bodhi App backend URL through the extension's popup UI, replacing the hardcoded localhost:1135 with a user-configurable setting that persists across browser sessions.

## Overview
Currently, the Bodhi Browser Extension connects to a hardcoded backend URL (localhost:1135). This feature will make the backend URL configurable through the extension's NextJS-based popup UI, allowing users to:
- View the current backend URL
- Modify the backend URL after basic URL format validation
- Save the configuration persistently

## Technical Details
The implementation uses:
- Chrome's Storage API for persistence ([docs](https://developer.chrome.com/docs/extensions/reference/storage))
- NextJS UI components for configuration interface
- Simple URL format validation in UI

## MVP Features
- ✅ UI form to view/edit backend URL
- ✅ Basic URL format validation in UI
- ✅ Persistent storage of URL configuration
- ✅ Immediate use of new URL in subsequent API calls

## Projects Impacted

### 1. bodhi-browser-ext (Chrome Extension)
- ✅ Add simple storage handling in background script
- ✅ Update API client to use stored URL
- ✅ Add basic settings UI component

### 2. integration-tests
- ✅ Add settings storage tests
- ✅ Test URL format validation
- ✅ Test API calls use updated URL

## Technical Implementation Details

### Settings Interface

```typescript
interface BackendSettings {
  url: string;
}
```

### Storage Implementation

```typescript
// Using Chrome's storage.sync for cross-device settings
chrome.storage.sync.set({
  backendUrl: 'http://localhost:1135'
});
```

### Settings Management API

```typescript
interface SettingsManager {
  getBackendUrl(): Promise<string>;
  setBackendUrl(url: string): Promise<void>;
}
```

### UI Components

```typescript
// NextJS component structure
interface SettingsFormProps {
  currentUrl: string;
  onSave: (url: string) => Promise<void>;
}
```

## Implementation Checklist

### Background Script (MVP)
- [x] Add simple storage get/set methods
- [x] Update API client to read URL from storage
- [x] Add fallback to default URL if not configured

### Popup UI (MVP)
- [x] Create settings form component
- [x] Add basic URL format validation
- [x] Implement save functionality

### Integration Tests (MVP)
- [x] Test URL storage and retrieval (implemented in settings.test.js)
- [x] Verify URL format validation
- [x] Test API calls use updated URL

## Example Usage

## Testing and Validation
The feature has been validated through:
1. Basic URL format validation in UI
2. Storage persistence tests in dedicated settings.test.js file
3. Verification that API calls use the updated URL

### Test Organization
The extension tests have been reorganized into multiple focused test files:
- `settings.test.js` - Tests specific to backend URL configuration features
- `web2ext.test.js` - Tests basic extension-to-webpage functionality
- `ext2ext.test.js` - Tests extension-to-extension communication

This organization makes the tests more maintainable and easier to understand.

## Acceptance Criteria
- [x] Users can view current backend URL in popup UI
- [x] Users can modify backend URL through settings form
- [x] Basic URL format validation prevents invalid URLs
- [x] URL changes persist across browser sessions
- [x] API calls use the updated URL immediately after save
- [x] All tests pass successfully