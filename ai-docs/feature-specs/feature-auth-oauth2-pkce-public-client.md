# Feature Specification: OAuth2 PKCE Public Client Flow via bodhi-js

## Primary Task
Enable pure front-end web applications (public clients) to authenticate and access an OAuth2-protected LLM resource server using the PKCE flow, leveraging bodhi-js and the Bodhi Browser Extension.

> **Note:** This feature should be implemented as a separate entry point/module (e.g., `oauth2-public.js`) and exported via the `package.json` `exports` field (e.g., `"./oauth2-public": "./dist/oauth2-public.js"`). This ensures the main bodhi-js library remains lightweight and avoids unnecessary bloat for users who do not require OAuth2 PKCE functionality.

## Overview
Web applications that do not have a backend (pure front-end) must use the OAuth2 Authorization Code flow with PKCE to securely obtain access tokens. This feature describes how bodhi-js facilitates the PKCE flow, manages state, and exchanges the authorization code for an access token, enabling secure calls to the LLM resource server via the Bodhi Browser Extension.

## Interface

### PKCE Flow Initiation
```typescript
startOAuth2PKCE(options: {
  clientId: string; // REQUIRED: OAuth2 client ID
  redirectUri: string; // REQUIRED: Must match registered redirect URI
  authorizationEndpoint?: string; // OPTIONAL: Defaults to known backend auth endpoint, but can be overridden for staging/testing
  codeChallenge?: string; // OPTIONAL: If not provided, generated internally
  codeVerifier?: string; // OPTIONAL: If not provided, generated and stored internally
  scope?: string; // OPTIONAL: Defaults to 'openid profile email' or as required
  state?: string; // OPTIONAL: If not provided, generated internally
  extraParams?: Record<string, string>; // OPTIONAL: Any additional params to append to auth URL
}): void;
```
- Redirects the user to the OAuth2 authorization endpoint with all required PKCE parameters.
- Stores codeVerifier, state, and redirectUri in session/local storage for use in callback.

### PKCE Callback/Token Exchange
```typescript
exchangeOAuth2CodeForToken(options?: {
  code?: string; // OPTIONAL: If not provided, parsed from current URL
  codeVerifier?: string; // OPTIONAL: If not provided, loaded from session/local storage
  redirectUri?: string; // OPTIONAL: If not provided, loaded from session/local storage
  tokenEndpoint?: string; // OPTIONAL: Defaults to known backend token endpoint, can be overridden
  clientId?: string; // OPTIONAL: If not provided, loaded from session/local storage
  extraParams?: Record<string, string>; // OPTIONAL: Any extra params for token exchange
}): Promise<{
  access_token: string;
  refresh_token?: string;
  expires_in?: number;
  id_token?: string;
  token_type: string;
  scope?: string;
  [key: string]: any;
}>;
```
- Exchanges the authorization code for an access token (and refresh token, if available).
- Returns the OAuth2 token response object.
- Handles parsing values from URL/session if not explicitly provided.

---

## Technical Details

### 1. PKCE Flow Initiation
- bodhi-js provides a method to initiate the OAuth2 PKCE flow.
- Generates a code verifier and code challenge, stores them in browser session/local storage.
- Constructs the authorization URL with the client ID, redirect URI, code challenge, and other required parameters.
- Redirects the user to the OAuth2 authorization endpoint.

### 2. Handling the Callback
- On redirect back to the web page (callback URL), bodhi-js detects the presence of the authorization code in the URL.
- bodhi-js retrieves the code verifier from storage, and exchanges the authorization code for an access token via the OAuth2 token endpoint.
- Stores the obtained access token securely (session/local storage).

### 3. Using the Access Token
- The web page uses bodhi-js to make authenticated requests to the LLM resource server, including the access token in the Authorization header.
- bodhi-js proxies these requests through the Bodhi Browser Extension, which forwards them to the LLM resource server.

### 4. Security Considerations
- Code verifier/challenge is stored only in session/local storage.
- Access tokens are never exposed to third-party scripts.
- All token exchanges happen over HTTPS.

## Flow Diagram

```
[Web Page] --(bodhi-js.startOAuth2PKCE())--> [OAuth2 Authorization Endpoint]
     | (redirects with code)
[Web Page] <--(code)-- [OAuth2 Authorization Endpoint]
     | (bodhi-js.exchangeCodeForToken())
[Web Page] --(token request)--> [OAuth2 Token Endpoint]
     | (receives access token)
[Web Page] --(API request w/ token via bodhi-js)--> [bodhi-browser-ext] --> [LLM Resource Server]
```

## Implementation Checklist
- [ ] bodhi-js: Add PKCE helper methods in a separate entry point (e.g., `src/oauth2-public.ts` → `dist/oauth2-public.js`)
- [ ] Export the PKCE module via `package.json` `exports` field as `"./oauth2-public": "./dist/oauth2-public.js"`
- [ ] bodhi-js: Store/retrieve code verifier and access token
- [ ] bodhi-js: Handle callback and token exchange
- [ ] bodhi-js: Provide API to get/set token for authenticated requests
- [ ] Documentation: Add PKCE usage examples and document the separate import/usage

## Test Plan
- Unit tests for PKCE code verifier/challenge generation
- Unit tests for authorization URL construction
- Integration test: Full PKCE flow with real OAuth2 server (mocked)
- Test: Access token is stored securely and not leaked
- Test: Authenticated API request includes correct Authorization header
- Test: Error handling for invalid/missing code, token exchange failures
- Test: Token expiration and refresh (if supported by OAuth2 server)

## Out of Scope
- Confidential client flows (handled in a separate feature)
- Backend-to-backend token exchange
