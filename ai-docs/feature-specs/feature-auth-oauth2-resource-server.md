# Feature Specification: OAuth2 Resource Server Integration

## Primary Task
Integrate the LLM backend as an OAuth2 resource server, supporting secure access and token validation/exchange for authenticated API requests proxied via the Bodhi Browser Extension.

## Overview
The LLM backend (resource server) must validate OAuth2 access tokens presented by clients (web pages or extensions) and support token exchange flows if the incoming token is not directly valid for the resource server. This ensures only authorized clients can access LLM capabilities, following OAuth2 best practices (e.g., as implemented by Keycloak).

## Technical Details

### 1. Access Token Validation
- The resource server receives requests with `Authorization: Bearer <token>`.
- Validates the token:
  - Checks signature, issuer, audience, expiry, and scopes as per OAuth2 spec.
  - If the token is valid for the resource server, processes the request.

### 2. Token Exchange (Optional)
- If the incoming token is valid for another client (e.g., the web page), the resource server supports OAuth2 Token Exchange ([RFC 8693](https://datatracker.ietf.org/doc/html/rfc8693)).
- Exchanges the provided token for a new token valid for the resource server.
- Uses Keycloak's token exchange endpoint if available.
- Proceeds with the request using the exchanged token.

### 3. Response Handling
- On success: Returns the requested LLM response.
- On failure: Returns appropriate OAuth2 error (e.g., `401 Unauthorized`, `invalid_token`).

### 4. Security Considerations
- All requests must use HTTPS.
- Tokens are never logged or exposed in error messages.
- Rate limiting and abuse protection should be enforced.

## Flow Diagram
```
[Web Page/Extension] --(API request w/ token)--> [bodhi-browser-ext] --(proxy)--> [LLM Resource Server]
     | (validates or exchanges token)
[LLM Resource Server] --(response)--> [bodhi-browser-ext] --(response)--> [Web Page/Extension]
```

## Implementation Checklist
- [ ] Resource server: Validate access tokens (signature, issuer, audience, expiry, scope)
- [ ] Resource server: Implement token exchange endpoint support (if needed)
- [ ] Resource server: Return appropriate OAuth2 errors
- [ ] Documentation: Describe expected request/response format
- [ ] Integration: Test with Bodhi Browser Extension and bodhi-js

## Test Plan
- Unit test: Token validation (valid/invalid/expired tokens)
- Integration test: End-to-end request with valid/invalid token from web page
- Integration test: Token exchange flow (web page token exchanged for resource server token)
- Test: Error propagation (invalid_token, unauthorized)
- Security test: Ensure no sensitive data is logged
- Test: Rate limiting and abuse scenarios

## Out of Scope
- Front-end token acquisition (handled in other features)
- Extension-specific flows (covered elsewhere)
