# Bodhi.js Test App OAuth Integration

## Overview

Implement OAuth login/callback functionality in the existing bodhi-js test app to replace the direct access grant method with the new OAuth flow that includes the request-access step. This will enable proper testing of the complete OAuth integration pattern that third-party applications need to follow.

## Domain Context

### Business Rules
- Direct access grant is now disabled for resource clients, requiring OAuth flow for token acquisition
- OAuth flow must include request-access step to get resource client scope
- Test app must serve all routes properly to support OAuth callback handling
- Playwright tests must automate the complete OAuth flow including credential entry
- Token acquisition must happen through the UI for test validation and debugging

### Domain Entities
- **Test App**: React application demonstrating bodhi-js OAuth integration
- **OAuth Flow**: Request-access → OAuth authorization → Token exchange → API testing
- **Resource Client**: App client that needs resource access scope from BodhiApp
- **Bearer Token**: OAuth access token used for authenticated API requests
- **Callback Handler**: OAuth callback page that exchanges authorization code for tokens

### Workflow Patterns
1. **App Serving**: Build → Copy index.html as 404.html → Serve with 'serve' library
2. **OAuth Login**: Landing page → Request access → OAuth redirect → User consent
3. **Token Exchange**: Callback page → Code exchange → Token display → API testing
4. **Test Automation**: Playwright navigates flow → Enters credentials → Captures token → Tests APIs

## Functional Requirements

### User Stories

**As a developer testing bodhi-js**, I want an OAuth login flow in the test app so that I can see how to implement proper authentication in my application.

**As a test runner**, I want automated OAuth flow testing so that I can validate the complete integration without manual intervention.

**As a debugging developer**, I want to see the acquired token in the UI so that I can verify the OAuth flow is working correctly.

### Acceptance Criteria

#### App Serving Enhancement
- [ ] **Serve Library Integration**: Replace python http.server with npm 'serve' library
- [ ] **404 Handling**: Copy index.html to 404.html during build for SPA routing
- [ ] **Route Support**: Enable /login and /callback routes to work properly
- [ ] **Build Process**: Update build script to handle file copying
- [ ] **Test Setup**: Update test setup to use serve instead of Vite dev server

#### OAuth Implementation
- [ ] **Landing Page**: Create /login route with Login button and extension status
- [ ] **Request Access**: Call /bodhi/v1/auth/request-access via bodhi-js → window.bodhiext
- [ ] **OAuth Redirect**: Build OAuth URL with proper scopes including resource scope
- [ ] **Callback Page**: Create /callback route to handle OAuth authorization code
- [ ] **Token Exchange**: Exchange authorization code for access token
- [ ] **Token Display**: Show acquired token in UI for debugging and validation
- [ ] **API Testing**: Navigate to API testing page with token pre-filled

#### Test Automation
- [ ] **Playwright Integration**: Automate complete OAuth flow in tests
- [ ] **Credential Entry**: Programmatically enter username/password on OAuth server
- [ ] **Token Capture**: Extract token from callback page for test use
- [ ] **API Validation**: Use captured token for authenticated API requests
- [ ] **Error Handling**: Handle OAuth errors and edge cases in tests

## Project Integration

### Architecture References
- [OAuth Flow](../../context/bodhi-browser-ext.md#oauth-integration) - Extension OAuth patterns
- [Extension Message Format](../../context/extension-message-format.md) - Request-access message format
- [bodhi-js library](../../context/bodhi-js.md) - Bodhi-js library usage patterns

### Existing Patterns
- Follow OAuth patterns from: `bodhi-browser-ext/tests/test-app-oauth/src/oauth.ts`
- Reference constants setup: `bodhi-browser-ext/tests/test-app-oauth/src/constants.ts`
- Use PKCE utilities from: `bodhi-browser-ext/tests/test-app-oauth/src/core.ts`
- Follow test patterns from: `bodhi-browser-ext/tests/web2ext-oauth-app.test.ts`

### Dependencies
- **serve**: npm library for serving SPA with 404 fallback
- **@bodhiapp/bodhijs**: Library for extension communication
- **react-router-dom**: Already installed for routing
- **crypto**: Browser Web Crypto API for PKCE implementation

## Implementation Tasks

### Task 1: App Serving Infrastructure
**Objective**: Replace python http.server with serve library for proper SPA routing

**Files to Modify**:
- `bodhi-js/tests/test-app/package.json` - Add serve dependency and scripts
- `bodhi-js/tests/test-app/vite.config.ts` - Update build configuration
- `bodhi-js/tests/setup.ts` - Update test app startup to use serve

**Implementation Steps**:
1. **Add serve dependency**: `npm install --save-dev serve`
2. **Update package.json scripts**:
   ```json
   {
     "scripts": {
       "build": "vite build && cp dist/index.html dist/404.html",
       "serve": "serve -s dist -p 3000",
       "serve:port": "serve -s dist -p"
     }
   }
   ```
3. **Update test setup**: Replace Vite dev server with serve in `setup.ts`
4. **Update build process**: Ensure 404.html is created for SPA routing

**Acceptance Criteria**:
- [ ] serve library serves the app with 404 fallback to index.html
- [ ] /login and /callback routes work without 404 errors
- [ ] Test setup successfully starts app using serve
- [ ] Build process creates both index.html and 404.html

### Task 2: OAuth Constants and Utilities
**Objective**: Create OAuth configuration and utility functions

**Files to Create**:
- `bodhi-js/tests/test-app/src/utils/constants.ts` - OAuth configuration
- `bodhi-js/tests/test-app/src/utils/oauth.ts` - OAuth flow utilities
- `bodhi-js/tests/test-app/src/utils/pkce.ts` - PKCE utilities

**Implementation Steps**:
1. **Create constants.ts**:
   ```typescript
   export const APP_CLIENT_ID = 'test-app-bodhijs-client';
   export const BODHI_AUTH_URL = process.env.INTEG_TEST_AUTH_URL || 'https://main-id.getbodhi.app';
   export const AUTH_REALM = process.env.INTEG_TEST_AUTH_REALM || 'bodhi';
   export const REDIRECT_URI = `${window.location.origin}/callback`;
   
   export const STORAGE_KEYS = {
     STATE: 'oauth_state',
     CODE_VERIFIER: 'oauth_code_verifier',
     RESOURCE_SCOPE: 'resource_scope',
     ACCESS_TOKEN: 'access_token',
   };
   ```

2. **Create PKCE utilities**:
   ```typescript
   export const generateRandomString = (length: number): string => { /* implementation */ };
   export const generatePKCEChallenge = async (verifier: string): Promise<string> => { /* implementation */ };
   ```

3. **Create OAuth manager**:
   ```typescript
   export class OAuthManager {
     async requestResourceAccess(): Promise<string> { /* implementation */ }
     async buildAuthUrl(): Promise<string> { /* implementation */ }
     async exchangeCodeForTokens(code: string, state: string): Promise<string> { /* implementation */ }
   }
   ```

**Acceptance Criteria**:
- [ ] Constants are properly configured for test environment
- [ ] PKCE utilities generate secure random strings and challenges
- [ ] OAuth manager handles request-access → OAuth → token exchange flow
- [ ] Error handling for each step of OAuth process

### Task 3: Login Page Implementation
**Objective**: Create /login route with OAuth initiation

**Files to Create/Modify**:
- `bodhi-js/tests/test-app/src/pages/LoginPage.tsx` - Login page component
- `bodhi-js/tests/test-app/src/App.tsx` - Add login route

**Implementation Steps**:
1. **Create LoginPage component**:
   ```tsx
   export const LoginPage: React.FC = () => {
     const [extensionStatus, setExtensionStatus] = useState('checking...');
     const [isLoading, setIsLoading] = useState(false);
     const [error, setError] = useState<string | null>(null);
     
     // Extension detection logic
     // Login button handler with request-access call
     // Error display and status management
   };
   ```

2. **Add route to App.tsx**:
   ```tsx
   <Routes>
     <Route path="/login" element={<LoginPage />} />
     <Route path="/callback" element={<CallbackPage />} />
     <Route path="/" element={<Navigate to="/login" replace />} />
     <Route path="/test" element={<TestForm />} />
   </Routes>
   ```

3. **Implement OAuth initiation**:
   - Detect extension availability
   - Call request-access via bodhi-js
   - Build OAuth URL with resource scope
   - Redirect to OAuth server

**Acceptance Criteria**:
- [ ] Login page displays extension status and login button
- [ ] Request-access call retrieves resource client scope
- [ ] OAuth URL includes all required scopes and PKCE parameters
- [ ] Proper error handling and user feedback
- [ ] Loading states during OAuth initiation

### Task 4: Callback Page Implementation
**Objective**: Create /callback route for OAuth code exchange

**Files to Create**:
- `bodhi-js/tests/test-app/src/pages/CallbackPage.tsx` - Callback page component

**Implementation Steps**:
1. **Create CallbackPage component**:
   ```tsx
   export const CallbackPage: React.FC = () => {
     const [status, setStatus] = useState('processing...');
     const [token, setToken] = useState<string | null>(null);
     const [error, setError] = useState<string | null>(null);
     
     useEffect(() => {
       processCallback();
     }, []);
     
     // Process OAuth callback and exchange code for token
   };
   ```

2. **Implement token exchange**:
   - Parse URL parameters (code, state, error)
   - Validate state parameter
   - Exchange authorization code for access token
   - Display token in UI for debugging
   - Handle OAuth errors

3. **Add navigation to test page**:
   - Button to navigate to API testing with token
   - Pre-fill authorization header with Bearer token

**Acceptance Criteria**:
- [ ] Callback page processes OAuth authorization code
- [ ] Token exchange validates state parameter for security
- [ ] Access token is displayed in UI for debugging
- [ ] Navigation to test page with token pre-filled
- [ ] Comprehensive error handling for OAuth errors

### Task 5: Test Integration Enhancement
**Objective**: Update tests to use OAuth flow instead of direct access grant

**Files to Modify**:
- `bodhi-js/tests/test-utils.ts` - Remove directAccessGrant, add OAuth helpers
- `bodhi-js/tests/bodhijs-with-test-app.test.ts` - Update tests to use OAuth flow
- `bodhi-js/tests/setup.ts` - Update app startup to use serve

**Implementation Steps**:
1. **Remove direct access grant**:
   ```typescript
   // Remove: export async function directAccessGrant(...)
   ```

2. **Add OAuth test helpers**:
   ```typescript
   export async function performOAuthLogin(
     page: Page,
     username: string,
     password: string
   ): Promise<string> {
     // Navigate to /login
     // Click login button
     // Handle OAuth server login
     // Return to callback and extract token
   }
   ```

3. **Update test setup**:
   ```typescript
   // Replace Vite dev server with serve
   const childProcess = spawn('npm', ['run', 'serve:port', port.toString()], {
     cwd: testAppDir,
     stdio: ['pipe', 'pipe', 'pipe'],
   });
   ```

4. **Update test cases**:
   ```typescript
   test('should successfully complete OAuth flow and make authenticated request', async () => {
     const token = await performOAuthLogin(page, username, password);
     // Use token for API testing
   });
   ```

**Acceptance Criteria**:
- [ ] Direct access grant method is removed
- [ ] OAuth flow is automated in Playwright tests
- [ ] Test app is served using serve library
- [ ] Token acquisition happens through UI flow
- [ ] All existing test scenarios work with OAuth tokens

## Technical Implementation Details

### OAuth Flow Sequence
1. **Landing Page** (`/login`):
   - Check extension availability
   - Display login button when ready
   - Show extension status and errors

2. **Login Initiation**:
   - Call `POST /bodhi/v1/auth/request-access` via bodhi-js
   - Receive resource client scope
   - Generate PKCE parameters (code_verifier, code_challenge)
   - Build OAuth authorization URL
   - Redirect to OAuth server

3. **OAuth Authorization**:
   - User consents on OAuth server
   - OAuth server redirects to `/callback` with authorization code

4. **Token Exchange** (`/callback`):
   - Parse authorization code and state from URL
   - Validate state parameter
   - Exchange code for access token using PKCE
   - Display token in UI
   - Navigate to API testing page

5. **API Testing**:
   - Pre-fill authorization header with Bearer token
   - Test authenticated API requests

### PKCE Implementation
```typescript
// Generate secure random string for code_verifier
const generateRandomString = (length: number): string => {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => 
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~'[byte % 64]
  ).join('');
};

// Generate code_challenge from code_verifier
const generatePKCEChallenge = async (verifier: string): Promise<string> => {
  const data = new TextEncoder().encode(verifier);
  const digest = await crypto.subtle.digest('SHA-256', data);
  return btoa(String.fromCharCode(...new Uint8Array(digest)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};
```

### Serve Configuration
```json
{
  "scripts": {
    "build": "vite build && cp dist/index.html dist/404.html",
    "serve": "serve -s dist -p 3000",
    "serve:port": "serve -s dist -p"
  }
}
```

### Test App Environment Variables
The test app will use environment variables from the test setup:
- `INTEG_TEST_AUTH_URL` - OAuth server URL
- `INTEG_TEST_AUTH_REALM` - OAuth realm
- `INTEG_TEST_APP_CLIENT_ID` - App client ID for request-access
- `INTEG_TEST_USERNAME` - Test user credentials
- `INTEG_TEST_PASSWORD` - Test user credentials

## Implementation Progress

### Completion Status
- [ ] App serving infrastructure with serve library
- [ ] OAuth constants and utility functions
- [ ] Login page with request-access integration
- [ ] Callback page with token exchange
- [ ] Test integration with OAuth flow automation
- [ ] Documentation and validation

### Current Phase
**Phase**: Planning
**Last Updated**: 2024-01-20
**Next Milestone**: App serving infrastructure setup

### Implementation Notes
This implementation replaces the direct access grant method with a complete OAuth flow that matches the patterns used by real applications integrating with BodhiApp. The test app becomes a reference implementation for developers.

## AI Development Changelog

### 2024-01-20 - Initial Specification
- **Completed**: Detailed analysis of current direct access grant usage
- **Approach**: Replace with complete OAuth flow including request-access step
- **Next Steps**: Begin with app serving infrastructure changes
- **Context**: Focus on production-ready OAuth patterns for developer reference

## Success Criteria

### Functional Validation
- [ ] OAuth flow completes successfully from login to token acquisition
- [ ] Test app serves all routes properly with serve library
- [ ] Playwright tests automate complete OAuth flow including credentials
- [ ] Acquired tokens work for authenticated API requests
- [ ] Error scenarios are handled gracefully

### Integration Validation
- [ ] All existing test scenarios work with OAuth tokens
- [ ] Test setup reliably starts app using serve library
- [ ] Extension communication works through OAuth flow
- [ ] Token display enables debugging and validation
- [ ] Test app serves as reference implementation for developers 