# Feature Specification: API Token Authentication for LLM Resource Server (Web Page)

## Primary Task
Enable web pages to connect to authenticated API endpoints of the LLM Resource Server using a long-lived API token provided interactively by the user.

## Overview
Some users may generate an API token (offline token) out-of-band (e.g., from an admin portal or CLI) and wish to use it for authenticated access to the LLM Resource Server. This feature enables web pages to prompt users for such a token, store it securely, and use it to authenticate API requests via bodhi-js and the Bodhi Browser Extension. The API token is long-lived and does not expire. There is no API endpoint for programmatic token issuance; the flow is entirely interactive.

## Technical Details

### 1. Extension/Server Availability Detection
- The web page uses bodhi-js to detect if the Bodhi Browser Extension is installed and if the LLM Resource Server is available.
- If either is missing/unavailable, the web page prompts the user accordingly.

### 2. User Token Entry Flow
- If authenticated API access is required, the web page displays a secure pop-up/modal prompting the user to enter their API token.
- The UI should:
  - Clearly explain where to obtain the API token (e.g., a link to documentation or admin portal)
  - Mask the input (password-style) for privacy
  - Validate the token format (valid jwt, token_type offline_token, issuer, audience, etc.)
  - Allow the user to update/replace the token at any time

### 3. Token Storage and Security
- The API token is stored in browser localStorage (never in cookies).
- Storing in localStorage ensures the token persists across browser sessions for better user experience, but increases risk if the page is vulnerable to XSS.
- The token is never exposed to third-party scripts or sent to any endpoint other than the LLM Resource Server via the extension.
- The token is only used in the `Authorization: Bearer <api_token>` header for authenticated API requests.

### 4. Making Authenticated API Requests
- bodhi-js includes the API token in the `Authorization` header for requests to the LLM Resource Server.
- The Bodhi Browser Extension proxies these requests as usual.
- The LLM Resource Server validates the API token and grants/denies access accordingly.

### 5. Token Management
- The web page provides a UI for users to view (masked), update, or remove their API token at any time.
- Removing the token disables authenticated API access until a new token is provided.

### 6. Security Considerations
- API tokens are never persisted in localStorage or cookies.
- Input is masked and never logged.
- All requests are made over HTTPS.
- The UI should warn users to only use tokens from trusted sources and never share them.

## Flow Diagram

```
[Web Page] --(detect extension/server via bodhi-js)--> [Bodhi Browser Extension/LLM Resource Server]
     | (if needed)
[Web Page] --(prompt user for API token)--> [User]
     | (user provides API token)
[Web Page] --(store token in session/memory)--> [Web Page]
[Web Page] --(API request w/ token via bodhi-js)--> [Bodhi Browser Extension] --> [LLM Resource Server]
     | (response)
[Web Page] <--(response)-- [Bodhi Browser Extension]
```

## Implementation Checklist
- [ ] bodhi-js: Add API for securely setting, getting, and clearing the API token
- [ ] bodhi-js: Include API token in Authorization header for authenticated requests
- [ ] bodhi-js: Provide helper for extension/server detection
- [ ] Example web page: Add UI for secure token entry and management
- [ ] Documentation: Add usage examples and security best practices

## Test Plan
- Unit test: API token is only accepted via user input, not via URL or script
- Unit test: API token is stored only in localStorage, never in cookies
- Integration test: Authenticated API request with valid/invalid token
- Integration test: Removing token disables authenticated access
- Security test: Ensure token is never leaked to third-party scripts or endpoints
- UI test: Input masking, update/removal flows, and user warnings

## Out of Scope
- Programmatic token issuance (no API endpoint for token generation)
- Token expiry/refresh (API tokens are long-lived)
- Extension or backend flows (covered in separate specs)

> **Note:** Storing the API token in localStorage improves user experience by persisting the token across sessions, but increases risk if your site is vulnerable to XSS. Developers should ensure strong XSS protections are in place.
