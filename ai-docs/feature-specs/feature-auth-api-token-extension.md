# Feature Specification: API Token Authentication for Chrome Extension (bodhi-js)

## Primary Task
Enable Chrome extensions (including Bodhi Browser Extension or third-party extensions using bodhi-js) to connect to authenticated API endpoints of the LLM Resource Server using a long-lived API token provided interactively by the user.

## Overview
Extensions may need to access the LLM Resource Server as authenticated clients, but implementing a full OAuth2 flow can be complex. As an alternative, this feature enables extensions to prompt users for an API token (personal access token or offline token), store it securely, and use it for authenticated API requests. The API token is long-lived and does not expire. There is no API endpoint for programmatic token issuance; the flow is entirely interactive and user-driven.

## Technical Details

### 1. Extension/Server Availability Detection
- The extension uses bodhi-js to detect if the Bodhi Browser Extension is installed and if the LLM Resource Server is available.
- If either is missing/unavailable, the extension prompts the user accordingly.

### 2. User Token Entry Flow
- If authenticated API access is required, the extension displays a secure pop-up/modal requesting the user to enter their API token.
- The UI should:
  - Clearly explain where to obtain the API token (e.g., a link to documentation or admin portal)
  - Mask the input (password-style) for privacy
  - Validate the token format (valid JWT, token_type offline_token, issuer, audience, etc.)
  - Allow the user to update/replace the token at any time

### 3. Token Storage and Security
- The API token is stored securely in Chrome extension storage (using `chrome.storage.local` or `chrome.storage.sync`), never in cookies or web-accessible storage.
- The token is never exposed to web pages or third-party scripts.
- The token is only used in the `Authorization: Bearer <api_token>` header for authenticated API requests to the LLM Resource Server.

### 4. Making Authenticated API Requests
- bodhi-js includes the API token in the `Authorization` header for requests to the LLM Resource Server.
- The Bodhi Browser Extension proxies these requests as usual.
- The LLM Resource Server validates the API token and grants/denies access accordingly.

### 5. Token Management
- The extension provides a UI for users to view (masked), update, or remove their API token at any time.
- Removing the token disables authenticated API access until a new token is provided.

### 6. Security Considerations
- API tokens are never persisted in cookies or web-accessible storage.
- Input is masked and never logged.
- All requests are made over HTTPS.
- The UI should warn users to only use tokens from trusted sources and never share them.

## Flow Diagram

```
[Extension] --(detect extension/server via bodhi-js)--> [Bodhi Browser Extension/LLM Resource Server]
     | (if needed)
[Extension] --(prompt user for API token)--> [User]
     | (user provides API token)
[Extension] --(store token in chrome.storage)--> [Extension]
[Extension] --(API request w/ token via bodhi-js)--> [Bodhi Browser Extension] --> [LLM Resource Server]
     | (response)
[Extension] <--(response)-- [Bodhi Browser Extension]
```

## Implementation Checklist
- [ ] bodhi-js: Add API for securely setting, getting, and clearing the API token in extension context
- [ ] bodhi-js: Include API token in Authorization header for authenticated requests from extension
- [ ] bodhi-js: Provide helper for extension/server detection
- [ ] Example extension: Add UI for secure token entry and management
- [ ] Documentation: Add usage examples and security best practices

## Test Plan
- Unit test: API token is only accepted via user input, not via URL or script
- Unit test: API token is stored only in Chrome extension storage, never in cookies or web-accessible storage
- Integration test: Authenticated API request with valid/invalid token
- Integration test: Removing token disables authenticated access
- Security test: Ensure token is never leaked to web pages or third-party scripts
- UI test: Input masking, update/removal flows, and user warnings

## Out of Scope
- Programmatic token issuance (no API endpoint for token generation)
- Token expiry/refresh (API tokens are long-lived)
- Web page flows (covered in separate specs)

---

> **Note for bodhi-js users:** This pattern provides a simple alternative to OAuth2 for authenticated API access in Chrome extensions. It is recommended for scenarios where implementing OAuth2 is impractical. Ensure users are informed about token security and privacy best practices.
