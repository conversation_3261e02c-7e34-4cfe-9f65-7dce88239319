# Feature Specification: Setup Dialog and Resource Server Discovery for Bodhi Browser Extension

## Primary Task
On installation (and via settings), present a setup dialog that allows users to configure multiple LLM resource servers for the Bodhi Browser Extension, supporting manual entry and scanning of known local/network endpoints.

## Context Recap
- The extension should support connecting to Bodhi app LLM Resource Server and other LLM resource servers (e.g., OLLAMA, LM Studio).
- On install, a setup dialog opens for server configuration. The dialog can be re-accessed via settings.
- Users can manually enter endpoints or scan for known resource servers (limited to known ports for now; UDP/network discovery is out of scope for this feature).
- Server identification is best-effort: use default ports, probe known endpoints, and guess type if possible. Otherwise, label as generic LLM resource.
- The extension will attempt to auto-detect authentication requirements by probing typical endpoints.
- No maximum number of servers; users can add as many as desired.
- A default/fallback server is required (Bodhi App by default, or user selection).
- Human-readable IDs are auto-assigned (with auto-increment for duplicates) but user-editable.
- Configured servers are stored locally (Chrome storage, not synced).
- No export/import of server configs.
- Warnings for unauthenticated servers are informational (icon + text), not blocking.
- No per-client access restrictions; the list is exposed by default to all web pages/extensions.

## Feature Scope
1. Setup dialog (on install or via settings/options)
2. Manual entry of LLM resource endpoints
3. Scanning for known LLM resource servers (limited to known ports and best-effort identification)
4. Server identification and authentication detection
5. UI for server selection, naming (with editable IDs), and default selection
6. Local storage of server configuration
7. Informational warning for unauthenticated servers
8. Exposing the list of servers to clients (web pages/extensions)

## Out of Scope
- UDP/network-wide discovery (future research required)
- Export/import of server configuration
- Per-client server access restrictions

---

## Setup Dialog UI/UX Outline

### A. Entry Point
- Automatically opens on extension install.
- Accessible anytime via extension settings/options page.

### B. Main Sections

#### 1. Welcome & Introduction
- Brief description of what LLM resource servers are and why configuration is needed.
- Link to documentation for more info.

#### 2. Discovered Resource Servers & Manual Entry
- **Scan for Servers**
  - Button: “Scan for Local/Network Servers”
  - Progress indicator during scan.
  - List of discovered servers (with best-effort identification: Bodhi App, OLLAMA, LM Studio, Generic LLM, etc.)
  - For each discovered server:
    - Checkbox to select
    - Editable name (human-readable ID, auto-increment for duplicates)
    - Server type/icon
    - Authentication status (lock/unlock icon, tooltip)
    - Info/warning icon for unauthenticated servers (with explanatory tooltip)
- **Manual Entry**
  - Button: “Add Resource Server Manually” (placed near the discovered servers list)
  - When clicked, opens input fields:
    - Server Name / Human-Readable ID (auto-filled, user-editable)
    - Server Endpoint (URL)
    - Add/Cancel buttons
  - Inline validation for endpoint format and duplicate names.

#### 3. Server List & Management
- Table or card list of all configured servers, displayed as a prioritized list:
  - Name/ID (editable)
  - Endpoint (editable after creation)
  - Type (icon)
  - Authentication status (icon)
  - Default badge on the top server (always the default)
  - Remove (trash icon)
- Users can rearrange servers (drag-and-drop or up/down arrows); the top server is always the default and displayed with a "Default" badge.
- If the default server is deleted, the next server in the list becomes the new default (and receives the badge).
- **Validation and Conflict Handling:**
  - Endpoints must be valid URLs and unique among all configured servers.
  - Duplicate endpoints are not allowed; user receives an error if attempted.

#### 4. Summary & Confirmation
- Recap of selected servers and default.
- Button: “Save & Continue” (enabled only if at least one server is configured)
- Option to revisit this dialog later via settings.

### C. Additional UI Elements
- Warning/info banners for unauthenticated servers.
- Error messages for invalid endpoints or failed scans.
- Tooltips and help icons for each field and action.

### D. Accessibility & Responsiveness
- Fully keyboard navigable.
- Responsive layout for various screen sizes.

#### Wireframe Example (Textual)

```
------------------------------------------------------
|  [Logo]  Welcome to Bodhi LLM Resource Setup       |
|----------------------------------------------------|
|  Configured Servers (Prioritized List):            |
|   [≡] bodhi-app  ************:8080  [Default] [🗑]  |
|   [≡] ollama     localhost:11434         [🗑]       |
|   [≡] ...                                         |
|   (Drag to reorder, top is always default)         |
|                                                    |
|  [Save & Continue]                                 |
|                                                    |
|  Discovered Resource Servers:                      |
|   [Scan for Local/Network Servers]                 |
|   [Progress...]                                    |
|                                                    |
|   [ ] [ollama]  localhost:11434   [Add]            |
|   [ ] [bodhi-app]  ************:8080 [Add]         |
|   [ ] [Generic LLM] ... [Add]                      |
|                                                    |
|   [Add Resource Server Manually]                   |
|                                                    |
|   (Manual entry form appears here when clicked)     |
------------------------------------------------------
```

---

## Implementation Story (to be generated next)
- Detailed UI/UX flow for setup dialog
- Manual and scan-based server addition
- Server identification logic and API probing
- Storage and management of server list
- API changes for exposing server list and supporting selection
- Security and warning UI for unauthenticated servers
