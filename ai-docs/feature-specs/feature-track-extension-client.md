# Feature Specification: Tracking Extension Clients Invoking Extension APIs

## Primary Task
Enable the Bodhi Browser Extension to reliably track and log which Chrome extension is invoking its APIs via extension-to-extension communication, for auditing, security, and analytics.

## Overview
When another Chrome extension communicates with the Bodhi Browser Extension (for LLM queries, authentication, etc.), it is important to capture and record the identity of the invoking extension. This enables:
- Auditing which extensions are using the Bodhi extension
- Security enforcement (e.g., allow/block lists)
- Analytics on extension usage
- Debugging and support

This feature details how the extension can reliably capture the source extension ID and relevant metadata for each API call made from another extension.

## Technical Details

### 1. Communication Flow
- Chrome extensions can communicate with each other using `chrome.runtime.sendMessage` or `chrome.runtime.connect`.
- The Bodhi extension's background script receives incoming messages from other extensions.

### 2. Capturing the Source Extension
- The `sender` parameter in the message listener (e.g., `chrome.runtime.onMessage` or `chrome.runtime.onConnect`) includes the `id` of the calling extension.
- Capture the following for each incoming message:
  - `sender.id` (the extension ID of the caller)
  - `sender.origin` (if available)
  - Any identifying information from the message payload (e.g., client app name/version if provided)
- Look up the extension's name and permissions (if possible) using the Chrome Web Store API or internal registry for enhanced logging.

### 3. Security and Reliability
- Never trust identifying data provided in the message payload—always use the `sender` object from the Chrome API.
- Implement allow/block lists for known extension IDs if needed.
- Consider edge cases:
  - Multiple simultaneous extension clients
  - Incognito mode
  - Disabled or uninstalled extensions
- Ensure that no sensitive user data is logged—only extension ID and metadata.

### 4. Logging and Usage
- Store extension ID and metadata with each API invocation in extension logs (memory, Chrome storage, or external analytics if user consents).
- Optionally, provide a UI in the extension for users to review which extensions have accessed the Bodhi extension.

## Flow Diagram

```
[Other Extension] --(chrome.runtime.sendMessage/connect)--> [Bodhi Extension background.js]
     | (captures sender.id, metadata)
[Bodhi Extension background.js] --(logs extension ID/metadata)--> [Log/Analytics]
     | (proxies API request)
[Bodhi Extension background.js] --(API request)--> [LLM Resource Server]
```

## Implementation Checklist
- [ ] background.js: Capture sender.id and metadata for each incoming extension message
- [ ] Log/store extension ID and metadata with each API invocation
- [ ] Implement allow/block lists for extension IDs (optional)
- [ ] UI: Optionally display extension usage to user
- [ ] Documentation: Describe tracking and privacy implications

## Test Plan
- Unit test: Correct extension ID is captured for various extension-to-extension calls
- Integration test: Extension ID is logged for API calls from multiple extensions
- Test: Incognito and disabled/uninstalled extension scenarios
- Security test: Ensure only extension ID/metadata (not sensitive data) is logged
- UI test: Display of tracked extensions (if implemented)

## Out of Scope
- Tracking web page invocations (covered in a separate feature)
- Tracking user identity (only extension ID/metadata is tracked)
