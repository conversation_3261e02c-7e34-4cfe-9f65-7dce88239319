# Feature Specification: Extension Token Forwarding and Validation

## Primary Task
Enable the Bodhi Browser Extension to accept OAuth2 access tokens from web pages (via bodhi-js), perform basic format validation, and forward authenticated requests to the LLM resource server.

## Overview
When a web page obtains an OAuth2 access token (e.g., via PKCE flow), it must include this token in requests to the LLM resource server. The Bodhi Browser Extension, acting as a proxy, receives these requests via bodhi-js, checks the presence and format of the token, and forwards the request to the backend. This feature ensures secure, standards-compliant handling of tokens and requests.

## Technical Details

### 1. Request Structure
- Web page uses bodhi-js to make an authenticated request, including the `Authorization: Bearer <token>` header.
- bodhi-js forwards the request to the extension via message passing.
- The extension (background.js) captures the identity of the caller (who invoked the request) using Chrome's `chrome.runtime.onMessage` or `chrome.runtime.onMessageExternal` API, which provides a `MessageSender` object. This object includes details such as the `origin`, `tab`, and `id` of the sender (web page, content script, or extension).
- The extension checks:
  - The presence of the Authorization header
  - That the token is a valid JWT in format (3 dot-separated base64url parts)
  - No signature or issuer validation is performed at this stage
  - Logs or records the invoker's identity for auditing and debugging

### 2. Forwarding to Resource Server
- The extension proxies the request to the LLM resource server, including the Authorization header as received.
- The resource server validates the token (or exchanges it for a new one if needed).
- The response from the resource server is returned to the extension, which relays it back to the web page via bodhi-js.

### 3. Error Handling
- If the Authorization header is missing or the token format is invalid, the extension returns an error to the web page.
- All errors are logged in the extension for debugging.

### 4. Security Considerations
- The extension never stores tokens; they are only forwarded per request.
- No sensitive token data is exposed to other web pages or extensions.
- The extension captures and logs the invoker's identity (using `MessageSender`) for every forwarded request. This helps in auditing, security review, and debugging.
- Only requests from authorized origins/extensions are processed; others are rejected.

## Flow Diagram
```
[Web Page] --(API request w/ token via bodhi-js)--> [bodhi-browser-ext]
     | (checks Authorization header & JWT format)
[bodhi-browser-ext] --(proxies request)--> [LLM Resource Server]
     | (response)
[bodhi-browser-ext] --(response)--> [Web Page]
```

## Implementation Checklist
- [ ] bodhi-js: Ensure Authorization header is set for authenticated requests
- [ ] bodhi-browser-ext: Validate presence and format of JWT
- [ ] bodhi-browser-ext: Capture and log invoker identity using `chrome.runtime.MessageSender`
- [ ] bodhi-browser-ext: Proxy request to LLM resource server
- [ ] bodhi-browser-ext: Relay response or error to web page
- [ ] bodhi-browser-ext: Reject requests from unauthorized origins/extensions
- [ ] Documentation: Update API usage and error codes

## Test Plan
- Unit test: Extension rejects requests with missing/invalid Authorization header
- Unit test: Extension accepts requests with valid JWT format
- Unit test: Extension correctly captures and logs invoker identity (using `MessageSender`)
- Integration test: End-to-end request from web page to resource server with valid token
- Integration test: Error propagation from resource server to web page
- Security test: Ensure tokens are not persisted or leaked
- Security test: Extension rejects requests from unauthorized origins/extensions
- Test: Logging of error cases and invoker identity in extension

## Out of Scope
- Token validation against issuer (handled by resource server)
- Token refresh/rotation
- Extension-to-extension authenticated requests (covered elsewhere)
