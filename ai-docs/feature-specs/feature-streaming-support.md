# Feature Specification: Streaming Response Support

## Primary Task
Add support for streaming API responses in the Bodhi Browser system, allowing web applications to receive and process LLM completions in real-time as they are generated, rather than waiting for the entire response to complete.

## Overview
Currently, the Bodhi Browser system supports only non-streaming requests to the LLM API. This feature will add streaming support throughout the stack, enabling web applications to receive tokens incrementally as they are generated by the LLM. This will provide a better user experience by showing responses as they are being generated, similar to how ChatGPT and other modern AI interfaces work.

## Technical Details
The streaming implementation will follow the OpenAI-compatible Server-Sent Events (SSE) format, as demonstrated in the curl example:

```
data: {"choices":[{"finish_reason":null,"index":0,"delta":{"content":"The"}}],...}
data: {"choices":[{"finish_reason":null,"index":0,"delta":{"content":" days"}}],...}
...
data: [DONE]
```

Each data chunk represents a token or part of the response, and clients will need to incrementally build the complete response by combining these chunks.

## MVP vs Advanced Features

This feature development is split into MVP (minimum viable product) and advanced features that will be implemented in future iterations.

### MVP Features
- ✅ Basic streaming implementation matching OpenAI SDK's pattern
- ✅ TypeScript interfaces for streaming responses
- ✅ Message passing implementation between extension components
- ✅ Basic error handling for streaming scenarios
- ✅ Integration tests for core streaming functionality

### Advanced Features (Backlog)
- Timeout handling for stalled connections
- Stream cancellation mechanism
- Connection error recovery
- Browser compatibility testing (Chrome, Firefox, Edge)
- Backpressure handling with fixed memory storage

## Projects Impacted

### 1. bodhi-js Library
- ✅ Extend the chat completions API to support streaming mode
- ✅ Implement async iterator pattern for handling streaming responses
- ✅ Update TypeScript types to support streaming response format
- ✅ Implement stream parsing compatible with OpenAI's SDK pattern

### 2. bodhi-browser-ext (Chrome Extension)
- ✅ Update inject.js to support streaming responses
- ✅ Modify content.js to handle streaming message passing
- ✅ Update background.js to implement fetch with streaming support
- ✅ Ensure proper error handling for streaming requests

### 3. integration-tests
- ✅ Add new tests for streaming functionality
- ✅ Create test page examples demonstrating streaming
- ✅ Mock LLM server needs streaming response support

## Implementation Status

All MVP features have been successfully implemented across all components. The implementation includes:

1. Full streaming support in bodhi-js library with proper TypeScript typing
2. Streaming support in the Chrome extension with proper message passing
3. Comprehensive integration tests for both script tag and NPM usage patterns
4. Mock LLM server with streaming capabilities for testing
5. Updated documentation and examples

## Implementation Checklist

### bodhi-js Library (MVP)
- [x] Update ChatRequest interface to include `stream` boolean parameter
- [x] Extend core.ts with streaming support helpers
- [x] Implement async iterator pattern in api.ts for processing streamed responses
- [x] Ensure compatibility with OpenAI SDK's streaming approach
- [x] Update TypeScript declarations for window.bodhiext interface
- [x] Document streaming usage in README.md

### bodhi-browser-ext (MVP)
- [x] Update inject.js to add streaming support to bodhiext interface
- [x] Modify window.postMessage handling in inject.js for streaming chunks
- [x] Update content.js message handling for streaming responses
- [x] Implement fetch with streaming support in background.js
- [x] Add proper error handling for streaming responses
- [x] Update README.md with streaming usage examples

### integration-tests (MVP)
- [x] Update mock-llm-server.ts to support streaming mode
- [x] Add streaming endpoint that simulates token-by-token responses
- [x] Create streaming test page example in test-page
- [x] Implement streaming version of end-to-end.test.ts (implemented as end-to-end-streaming.test.ts)
- [x] Add tests that verify proper message handling
- [x] Test error scenarios in streaming mode

### Advanced Features (Backlog)
- [ ] Implement timeout handling for stalled connections
- [ ] Add cancellation mechanism for in-progress streams
- [ ] Develop connection error recovery strategy
- [ ] Perform browser compatibility testing
- [ ] Implement backpressure handling

## Technical Implementation Details

### Message Passing for Streaming

The key technical challenge is maintaining the streaming connection across the extension's architectural boundaries. Here's how the implementation works:

1. **Web Page to bodhijs**:
   - When a streaming request is made with `stream: true`, bodhijs creates an AsyncIterator that yields chunks
   - The iterator is backed by an internal message queue that receives chunks from the extension

2. **bodhijs to inject.js**:
   - bodhijs sends an initial message with `stream: true` to window.bodhiext
   - inject.js creates a request ID and maintains an open channel for this stream

3. **inject.js to content.js**:
   - inject.js uses window.postMessage to send the initial streaming request
   - content.js receives this message and establishes a long-lived message channel with background.js

4. **content.js to background.js**:
   - A message is sent to background.js with the streaming request and request ID
   - Chrome extension message port is used instead of one-off messages to maintain the streaming connection

5. **background.js to Bodhi App**:
   - background.js uses fetch API with `{ method: 'POST', headers: {...}, body: JSON.stringify(body) }`
   - The response is processed using a ReadableStream and TextDecoder to parse SSE format
   - Each chunk is parsed from the SSE format and sent back through the message chain

6. **Response Path**:
   - When background.js receives a chunk, it sends it to content.js with the original request ID
   - content.js forwards each chunk to inject.js via window.postMessage
   - inject.js receives each chunk and adds it to the internal queue for the AsyncIterator
   - The for-await loop in the client code receives each chunk as it arrives

7. **Stream Completion**:
   - When the special `data: [DONE]` message is received, the stream is closed
   - All message channels are cleaned up and the AsyncIterator is completed

8. **Error Handling**:
   - If any component in the chain encounters an error, it propagates an error message with the same request ID
   - This error is thrown by the AsyncIterator, which can be caught by the client code

## Example Usage (Implemented)

### Using the bodhijs Library
```javascript
import { bodhijs } from '@bodhiapp/bodhijs';

async function streamingExample() {
  try {    
    // Create a streaming request by setting stream: true
    const stream = await bodhijs.chat.completions.create({
      model: 'bartowski/Meta-Llama-3.1-8B-Instruct-GGUF:Q4_K_M',
      messages: [
        { role: 'user', content: 'Write a short poem about AI' }
      ],
      stream: true // Enable streaming
    });
    
    // Process stream chunks as they arrive
    for await (const chunk of stream) {
      // Each chunk has a `choices` array with deltas
      if (chunk.choices[0].delta.content) {
        // Process each token as it arrives
        process.stdout.write(chunk.choices[0].delta.content);
      }
    }
  } catch (error) {
    console.error('Streaming error:', error);
  }
}

streamingExample();
```

## TypeScript Interface Details

The TypeScript interfaces follow OpenAI's pattern:

```typescript
// Import from @bodhiapp/ts-client where possible
import type { ChatRequest as TSClientChatRequest } from '@bodhiapp/ts-client';

// Re-export the types from ts-client
export type ChatRequest = TSClientChatRequest & {
  // Add streaming support if not already in ts-client
  stream?: boolean;
};

// Standard chat response
export interface ChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Chat completion chunk for streaming responses
export interface ChatCompletionChunk {
  id: string;
  object: 'chat.completion.chunk';
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: {
      content?: string;
      role?: string;
      function_call?: any;
      tool_calls?: any;
    };
    finish_reason: string | null;
  }>;
}

// Stream interface compatible with OpenAI SDK
export interface Stream<T> {
  [Symbol.asyncIterator](): AsyncIterator<T>;
}
```

## Testing and Validation
Streaming functionality has been validated with comprehensive integration tests that:
1. Verify streaming requests are sent correctly
2. Ensure chunks are received and processed properly
3. Confirm async iterator works properly for processing chunks
4. Test error scenarios in streaming mode

Both regular HTTP test pages and NPM package usage patterns have dedicated streaming test files to ensure all use cases are covered.

## Acceptance Criteria
- ✅ Web applications can send streaming requests through bodhijs
- ✅ Tokens are received incrementally as the model generates them
- ✅ Streaming API matches OpenAI SDK pattern for drop-in compatibility
- ✅ Errors are properly propagated and handled
- ✅ Integration tests pass for all streaming scenarios
- ✅ Documentation clearly explains streaming usage 