# Feature Specification: OAuth2 Confidential Client Flow via bodhi-js

## Primary Task
Enable backend-enabled web applications or extensions (confidential clients) to authenticate and access an OAuth2-protected LLM resource server using the OAuth2 Client Credentials flow (or Authorization Code flow with client secret), leveraging a dedicated bodhi-js module and the Bodhi Browser Extension.

> **Note:** This feature should be implemented as a separate entry point/module (e.g., `oauth2-confidential.js`) and exported via the `package.json` `exports` field (e.g., `"./oauth2-confidential": "./dist/oauth2-confidential.js"`). This ensures the main bodhi-js library remains lightweight and avoids unnecessary bloat for users who do not require confidential client OAuth2 functionality.

## Overview
Confidential clients (such as backend servers or browser extensions with secure storage) can securely store a client secret and use OAuth2 flows that require client authentication. This feature describes how bodhi-js facilitates the confidential client flow, manages credentials, obtains access tokens, and enables secure calls to the LLM resource server via the Bodhi Browser Extension.

## Interface

<!--
NOTE: Client Credentials flow is intentionally omitted because this grant type is not enabled/supported in the backend. This spec is scoped strictly to the Authorization Code flow with client secret for confidential clients.
-->

### Authorization Code Flow (with Client Secret)

#### Step 1: Initiate Flow
```typescript
startOAuth2AuthorizationCodeConfidential(options: {
  clientId: string; // REQUIRED
  redirectUri: string; // REQUIRED
  authorizationEndpoint?: string; // OPTIONAL: Defaults to known backend auth endpoint
  scope?: string; // OPTIONAL: Defaults to 'openid profile email'
  state?: string; // OPTIONAL: If not provided, generated internally
  codeChallenge?: string; // OPTIONAL: If PKCE is used, can be provided or generated
  codeVerifier?: string; // OPTIONAL: If PKCE is used, can be provided or generated
  extraParams?: Record<string, string>; // OPTIONAL: Any additional params to append to auth URL
}): {
  url: string; // The authorization URL to redirect the user to
  sessionData: Record<string, string>; // Key/values (e.g., state, code_verifier, etc.) to store in session for exchange
};
```
- Returns the authorization URL and session data to be stored for later use in the exchange step.

#### Step 2: Exchange Code for Token
```typescript
exchangeOAuth2CodeForTokenConfidential(options: {
  code: string; // REQUIRED: Authorization code (must be provided)
  clientId: string; // REQUIRED
  clientSecret: string; // REQUIRED
  redirectUri: string; // REQUIRED
  tokenEndpoint?: string; // OPTIONAL: Defaults to known backend token endpoint
  sessionData?: Record<string, string>; // REQUIRED: The session data returned from start method, or loaded from session
  extraParams?: Record<string, string>; // OPTIONAL: Any extra params for token exchange
}): Promise<{
  access_token: string;
  refresh_token?: string;
  expires_in?: number;
  id_token?: string;
  token_type: string;
  scope?: string;
  [key: string]: any;
}>;
```
- Exchanges the authorization code for an access token (and refresh token, if available), using the client secret for confidential client authentication.
- Requires the session data (e.g., state, code_verifier) returned from the start method or loaded from session.
- Returns the OAuth2 token response object.
- Handles optional PKCE values and extra params as needed.

---

## Technical Details

### 1. Authorization Code Flow (with Client Secret)
- bodhi-js provides a method to initiate the OAuth2 Authorization Code flow for confidential clients, returning the authorization URL and session data to be stored.
- The client secret is used during the code exchange step to authenticate the confidential client.
- Handles redirect, code exchange, and token storage as appropriate for confidential clients.

### 2. Using the Access Token
- The backend or extension uses bodhi-js to make authenticated requests to the LLM resource server, including the access token in the Authorization header.
- bodhi-js proxies these requests through the Bodhi Browser Extension, which forwards them to the LLM resource server.

### 4. Security Considerations
- Client secret is never exposed to the browser or front-end JavaScript.
- All token exchanges and storage occur in secure contexts (backend, extension storage, or secure browser APIs).
- Access tokens are never exposed to third-party scripts.
- All token exchanges happen over HTTPS.

## Flow Diagram

```
[Backend/Extension] --(bodhi-js.startOAuth2AuthorizationCodeConfidential())--> [OAuth2 Authorization Endpoint]
     | (redirects with code)
[Backend/Extension] <--(code)-- [OAuth2 Authorization Endpoint]
     | (bodhi-js.exchangeOAuth2CodeForTokenConfidential())
[Backend/Extension] --(token request)--> [OAuth2 Token Endpoint]
     | (receives access token)
[Backend/Extension] --(API request w/ token via bodhi-js)--> [bodhi-browser-ext] --> [LLM Resource Server]
```

## Implementation Checklist
- [ ] bodhi-js: Add confidential client helper methods in a separate entry point (e.g., `src/oauth2-confidential.ts` → `dist/oauth2-confidential.js`)
- [ ] Export the confidential client module via `package.json` `exports` field as `"./oauth2-confidential": "./dist/oauth2-confidential.js"`
- [ ] bodhi-js: Store/retrieve access token securely
- [ ] bodhi-js: Support both Client Credentials and Authorization Code flows for confidential clients
- [ ] bodhi-js: Provide API to get/set token for authenticated requests
- [ ] Documentation: Add confidential client usage examples and document the separate import/usage

## Test Plan
- Unit tests for confidential client credential handling
- Unit tests for token endpoint request/response
- Integration test: Full client credentials flow with real OAuth2 server (mocked)
- Test: Access token is stored securely and not leaked
- Test: Authenticated API request includes correct Authorization header
- Test: Error handling for invalid/missing credentials, token exchange failures
- Test: Token expiration and refresh (if supported by OAuth2 server)

## Out of Scope
- PKCE/public client flows (handled in a separate feature)
- Front-end only token acquisition
