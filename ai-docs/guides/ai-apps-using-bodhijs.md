# Building AI-Powered Apps with bodhijs: A Complete Integration Guide

This guide provides comprehensive instructions for AI coding assistants to help developers build AI-powered React SPA applications using the bodhijs library. The bodhijs library enables web applications to interact with locally running LLM servers through the Bodhi Browser Extension, providing privacy-preserving AI capabilities directly from the user's device.

## System Architecture Overview

The bodhijs ecosystem consists of three main components:

1. **bodhijs Library** (`@bodhiapp/bodhijs`): A JavaScript/TypeScript library that provides a clean API for web applications
2. **Bodhi Browser Extension**: A Chrome extension that bridges web pages to the local server
3. **BodhiApp Server**: A locally running LLM service that provides OpenAI-compatible APIs

```
React App ←→ bodhijs Library ←→ Browser Extension ←→ BodhiApp Server
          (your code)      (window.bodhiext)      (local LLM)
```

## Installation and Setup

### Installing bodhijs in Your React Project

```bash
npm install @bodhiapp/bodhijs
```

### Project Dependencies for TypeScript React Apps

Your `package.json` should include:

```json
{
  "dependencies": {
    "@bodhiapp/bodhijs": "^1.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "typescript": "^5.0.0"
  }
}
```

### Basic Import Patterns

```typescript
// Named imports (recommended)
import { 
  isInstalled, 
  isServerAvailable, 
  ping, 
  chat, 
  sendApiRequest,
  BodhiError, 
  ErrorCode 
} from '@bodhiapp/bodhijs';

// Default import (backward compatibility)
import bodhijs from '@bodhiapp/bodhijs';

// Type imports
import type { ChatRequest, ChatResponse } from '@bodhiapp/bodhijs';
```

## Core API Reference

### Extension Detection

Always check if the extension is installed before making API calls:

```typescript
import { isInstalled, isServerAvailable } from '@bodhiapp/bodhijs';

const checkSystem = async (): Promise<boolean> => {
  if (!isInstalled()) {
    console.error('Bodhi Browser Extension is not installed');
    return false;
  }
  
  const serverAvailable = await isServerAvailable();
  if (!serverAvailable) {
    console.error('Bodhi server is not running');
    return false;
  }
  
  return true;
};
```

### Ping API

Test connectivity with the local server:

```typescript
import { ping } from '@bodhiapp/bodhijs';

const testConnection = async () => {
  try {
    const result = await ping();
    console.log('Server response:', result.message); // "pong"
    return true;
  } catch (error) {
    console.error('Ping failed:', error);
    return false;
  }
};
```

### Chat Completions API

The chat API follows OpenAI's format for easy integration. **Note: Streaming is not currently supported.**

```typescript
import { chat } from '@bodhiapp/bodhijs';
import type { ChatRequest, ChatResponse } from '@bodhiapp/bodhijs';

const sendChatMessage = async (message: string): Promise<ChatResponse | null> => {
  try {
    const request: ChatRequest = {
      model: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M',
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: message }
      ],
      temperature: 0.7,
      max_tokens: 500
    };
    
    const response = await chat.completions.create(request);
    return response;
  } catch (error) {
    console.error('Chat completion failed:', error);
    return null;
  }
};
```

### Generic API Request Function

For advanced use cases, you can make direct API requests to the BodhiApp server:

```typescript
import { sendApiRequest } from '@bodhiapp/bodhijs';

const makeCustomRequest = async () => {
  try {
    const response = await sendApiRequest({
      method: 'GET',
      endpoint: '/v1/models',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Models:', response.data);
    return response.data;
  } catch (error) {
    console.error('API request failed:', error);
    return null;
  }
};
```

## BodhiApp Server APIs

The BodhiApp server provides several API endpoints that can be accessed through the bodhi-js library:

### OpenAI-Compatible Endpoints

#### List Available Models
```typescript
import { sendApiRequest } from '@bodhiapp/bodhijs';

const getModels = async () => {
  try {
    const response = await sendApiRequest({
      method: 'GET',
      endpoint: '/v1/models',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Failed to fetch models:', error);
    return null;
  }
};
```

#### Chat Completions
```typescript
const chatCompletion = async (model: string, messages: any[]) => {
  try {
    const response = await sendApiRequest({
      method: 'POST',
      endpoint: '/v1/chat/completions',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN',
        'Content-Type': 'application/json'
      },
      body: {
        model: model,
        messages: messages,
        stream: false // Streaming is not supported in the current library
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Chat completion failed:', error);
    return null;
  }
};
```

### Bodhi-Specific Endpoints

#### Get Detailed Model Information
```typescript
const getBodhiModels = async () => {
  try {
    const response = await sendApiRequest({
      method: 'GET',
      endpoint: '/bodhi/v1/models',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Failed to fetch Bodhi models:', error);
    return null;
  }
};
```

#### Create/Update Model (Power User)
```typescript
const createModel = async (modelConfig: any) => {
  try {
    const response = await sendApiRequest({
      method: 'POST',
      endpoint: '/bodhi/v1/models',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN',
        'Content-Type': 'application/json'
      },
      body: modelConfig
    });
    
    return response.data;
  } catch (error) {
    console.error('Failed to create model:', error);
    return null;
  }
};
```

## React Component Examples

### Basic Chat Component

```typescript
import React, { useState, useEffect } from 'react';
import { isInstalled, ping, chat, BodhiError, ErrorCode } from '@bodhiapp/bodhijs';
import type { ChatResponse } from '@bodhiapp/bodhijs';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const ChatComponent: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [systemReady, setSystemReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkSystemAvailability();
  }, []);

  const checkSystemAvailability = async () => {
    try {
      if (!isInstalled()) {
        setError('Bodhi Browser Extension is not installed');
        return;
      }

      await ping();
      setSystemReady(true);
      setError(null);
    } catch (error) {
      setError('Bodhi server is not available');
    }
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || loading || !systemReady) return;

    const userMessage: Message = {
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);

    try {
      const response = await chat.completions.create({
        model: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M',
        messages: [
          ...messages.map(m => ({ role: m.role, content: m.content })),
          { role: 'user', content: inputValue }
        ]
      });

      const assistantMessage: Message = {
        role: 'assistant',
        content: response.choices[0].message.content,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      if (error instanceof BodhiError) {
        switch (error.code) {
          case ErrorCode.EXTENSION_NOT_INSTALLED:
            setError('Extension not installed');
            break;
          case ErrorCode.SERVER_UNAVAILABLE:
            setError('Server unavailable');
            break;
          default:
            setError(`Error: ${error.message}`);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  if (!systemReady) {
    return (
      <div className="chat-error">
        <p>{error || 'Checking system availability...'}</p>
        <button onClick={checkSystemAvailability}>Retry</button>
      </div>
    );
  }

  return (
    <div className="chat-container">
      <div className="messages">
        {messages.map((message, index) => (
          <div key={index} className={`message ${message.role}`}>
            <strong>{message.role}:</strong> {message.content}
          </div>
        ))}
        {loading && <div className="loading">AI is thinking...</div>}
      </div>
      
      <div className="input-area">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          placeholder="Type your message..."
          disabled={loading}
        />
        <button onClick={sendMessage} disabled={loading || !inputValue.trim()}>
          Send
        </button>
      </div>
    </div>
  );
};

export default ChatComponent;
```

### Model Selection Component

```typescript
import React, { useState, useEffect } from 'react';
import { sendApiRequest } from '@bodhiapp/bodhijs';

interface ModelInfo {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

const ModelSelector: React.FC<{
  selectedModel: string;
  onModelChange: (modelId: string) => void;
}> = ({ selectedModel, onModelChange }) => {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAvailableModels();
  }, []);

  const loadAvailableModels = async () => {
    try {
      const response = await sendApiRequest({
        method: 'GET',
        endpoint: '/v1/models',
        headers: {
          'Authorization': 'Bearer YOUR_API_TOKEN'
        }
      });

      if (response.data && response.data.data) {
        setModels(response.data.data);
        if (!selectedModel && response.data.data.length > 0) {
          onModelChange(response.data.data[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to load models:', error);
      // Fallback to common models
      const commonModels: ModelInfo[] = [
        { 
          id: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M', 
          object: 'model',
          created: Date.now(),
          owned_by: 'microsoft'
        },
        { 
          id: 'test-model',
          object: 'model',
          created: Date.now(),
          owned_by: 'test'
        }
      ];
      setModels(commonModels);
      if (!selectedModel && commonModels.length > 0) {
        onModelChange(commonModels[0].id);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>Loading models...</div>;
  }

  return (
    <div className="model-selector">
      <label htmlFor="model-select">Select Model:</label>
      <select
        id="model-select"
        value={selectedModel}
        onChange={(e) => onModelChange(e.target.value)}
      >
        {models.map((model) => (
          <option key={model.id} value={model.id}>
            {model.id}
          </option>
        ))}
      </select>
    </div>
  );
};

export default ModelSelector;
```

### Complete Chat Application

```typescript
import React, { useState, useEffect } from 'react';
import { isInstalled, isServerAvailable, chat, sendApiRequest, BodhiError, ErrorCode } from '@bodhiapp/bodhijs';
import type { ChatResponse } from '@bodhiapp/bodhijs';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ModelInfo {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

const ChatApp: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<'checking' | 'ready' | 'error'>('checking');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<string>('bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M');
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [models, setModels] = useState<ModelInfo[]>([]);

  useEffect(() => {
    checkSystemStatus();
  }, []);

  const checkSystemStatus = async () => {
    try {
      if (!isInstalled()) {
        setSystemStatus('error');
        setErrorMessage('Bodhi Browser Extension is not installed. Please install it to use this app.');
        return;
      }

      const serverAvailable = await isServerAvailable();
      if (!serverAvailable) {
        setSystemStatus('error');
        setErrorMessage('Bodhi server is not running. Please start your local BodhiApp.');
        return;
      }

      setSystemStatus('ready');
      setErrorMessage('');
      await loadModels();
    } catch (error) {
      setSystemStatus('error');
      setErrorMessage('Failed to connect to Bodhi system');
    }
  };

  const loadModels = async () => {
    try {
      const response = await sendApiRequest({
        method: 'GET',
        endpoint: '/v1/models',
        headers: {
          'Authorization': 'Bearer YOUR_API_TOKEN'
        }
      });

      if (response.data && response.data.data) {
        setModels(response.data.data);
      }
    } catch (error) {
      console.error('Failed to load models:', error);
      // Use default models if API fails
      setModels([
        { 
          id: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M', 
          object: 'model',
          created: Date.now(),
          owned_by: 'microsoft'
        }
      ]);
    }
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || loading) return;

    const userMessage: Message = {
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageToSend = inputValue;
    setInputValue('');
    setLoading(true);

    try {
      const response = await chat.completions.create({
        model: selectedModel,
        messages: [
          ...messages.map(m => ({ role: m.role, content: m.content })),
          { role: 'user', content: messageToSend }
        ]
      });

      const assistantMessage: Message = {
        role: 'assistant',
        content: response.choices[0].message.content,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Chat completion failed:', error);
      let errorMsg = 'Failed to send message';
      
      if (error instanceof BodhiError) {
        switch (error.code) {
          case ErrorCode.EXTENSION_NOT_INSTALLED:
            errorMsg = 'Extension not installed';
            break;
          case ErrorCode.SERVER_UNAVAILABLE:
            errorMsg = 'Server unavailable';
            break;
          default:
            errorMsg = error.message;
        }
      }

      const errorMessage: Message = {
        role: 'assistant',
        content: `Error: ${errorMsg}`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  if (systemStatus === 'checking') {
    return (
      <div className="app-loading">
        <h2>Initializing AI Chat</h2>
        <p>Checking system availability...</p>
      </div>
    );
  }

  if (systemStatus === 'error') {
    return (
      <div className="app-error">
        <h2>System Error</h2>
        <p>{errorMessage}</p>
        <button onClick={checkSystemStatus}>Retry</button>
      </div>
    );
  }

  return (
    <div className="chat-app">
      <header className="app-header">
        <h1>AI Chat Assistant</h1>
        <div className="model-selector">
          <label>Model:</label>
          <select 
            value={selectedModel} 
            onChange={(e) => setSelectedModel(e.target.value)}
          >
            {models.map((model) => (
              <option key={model.id} value={model.id}>
                {model.id}
              </option>
            ))}
          </select>
        </div>
      </header>

      <div className="chat-container">
        <div className="messages">
          {messages.map((message, index) => (
            <div key={index} className={`message ${message.role}`}>
              <div className="message-content">
                <strong>{message.role === 'user' ? 'You' : 'AI'}:</strong>
                <p>{message.content}</p>
              </div>
              <div className="message-time">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          ))}
          
          {loading && (
            <div className="message assistant loading">
              <div className="message-content">
                <strong>AI:</strong>
                <p>Thinking...</p>
              </div>
            </div>
          )}
        </div>

        <div className="input-area">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
              }
            }}
            placeholder="Type your message... (Shift+Enter for new line)"
            disabled={loading}
            rows={3}
          />
          <button 
            onClick={sendMessage} 
            disabled={loading || !inputValue.trim()}
          >
            {loading ? 'Sending...' : 'Send'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatApp;
```

## Error Handling Patterns

### Comprehensive Error Handling

```typescript
import { BodhiError, ErrorCode } from '@bodhiapp/bodhijs';

const handleBodhiError = (error: unknown): string => {
  if (error instanceof BodhiError) {
    switch (error.code) {
      case ErrorCode.EXTENSION_NOT_INSTALLED:
        return 'Please install the Bodhi Browser Extension to use AI features.';
      case ErrorCode.SERVER_UNAVAILABLE:
        return 'Bodhi server is not running. Please start your local BodhiApp.';
      case ErrorCode.UNKNOWN_ERROR:
        return `An unexpected error occurred: ${error.message}`;
      default:
        return 'An unknown error occurred.';
    }
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'An unexpected error occurred.';
};

// Usage in components
const [errorState, setErrorState] = useState<string | null>(null);

const safeApiCall = async () => {
  try {
    setErrorState(null);
    const result = await chat.completions.create(request);
    return result;
  } catch (error) {
    const errorMessage = handleBodhiError(error);
    setErrorState(errorMessage);
    return null;
  }
};
```

### Retry Logic with Exponential Backoff

```typescript
const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      if (error instanceof BodhiError && error.code === ErrorCode.EXTENSION_NOT_INSTALLED) {
        // Don't retry if extension is not installed
        throw error;
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new Error('Max retries exceeded');
};

// Usage
const robustPing = () => retryWithBackoff(() => ping(), 3, 1000);
```

## Authentication and API Tokens

When working with authenticated endpoints, you'll need to handle API tokens:

```typescript
import { sendApiRequest } from '@bodhiapp/bodhijs';

// Store API token securely (e.g., in environment variables or secure storage)
const API_TOKEN = process.env.REACT_APP_BODHI_API_TOKEN || 'your-api-token';

const authenticatedRequest = async (endpoint: string, options: any = {}) => {
  try {
    const response = await sendApiRequest({
      method: options.method || 'GET',
      endpoint: endpoint,
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: options.body
    });
    
    return response.data;
  } catch (error) {
    console.error('Authenticated request failed:', error);
    throw error;
  }
};

// Usage
const models = await authenticatedRequest('/v1/models');
const chatResponse = await authenticatedRequest('/v1/chat/completions', {
  method: 'POST',
  body: {
    model: 'your-model',
    messages: [{ role: 'user', content: 'Hello' }]
  }
});
```

## Performance Optimization

### Message Batching and Debouncing

```typescript
import { useCallback, useRef } from 'react';

const useDebounce = <T extends any[]>(
  callback: (...args: T) => void,
  delay: number
) => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback((...args: T) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

// Usage in component
const debouncedSend = useDebounce((message: string) => {
  sendChatMessage(message);
}, 500);
```

### Memory Management for Large Conversations

```typescript
const useConversationManager = (maxMessages: number = 20) => {
  const [messages, setMessages] = useState<Message[]>([]);

  const addMessage = useCallback((message: Message) => {
    setMessages(prev => {
      const newMessages = [...prev, message];
      return newMessages.length > maxMessages 
        ? newMessages.slice(-maxMessages) 
        : newMessages;
    });
  }, [maxMessages]);

  const clearConversation = useCallback(() => {
    setMessages([]);
  }, []);

  return { messages, addMessage, clearConversation };
};
```

## Testing Patterns

### Unit Testing with Mocks

```typescript
// __tests__/chat.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import ChatComponent from '../ChatComponent';
import * as bodhijs from '@bodhiapp/bodhijs';

// Mock the bodhijs module
vi.mock('@bodhiapp/bodhijs', () => ({
  isInstalled: vi.fn(),
  ping: vi.fn(),
  chat: {
    completions: {
      create: vi.fn()
    }
  },
  BodhiError: class BodhiError extends Error {
    constructor(message: string, public code: string) {
      super(message);
    }
  },
  ErrorCode: {
    EXTENSION_NOT_INSTALLED: 'EXTENSION_NOT_INSTALLED',
    SERVER_UNAVAILABLE: 'SERVER_UNAVAILABLE',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR'
  }
}));

describe('ChatComponent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('shows error when extension is not installed', async () => {
    (bodhijs.isInstalled as any).mockReturnValue(false);
    
    render(<ChatComponent />);
    
    await waitFor(() => {
      expect(screen.getByText(/extension is not installed/i)).toBeInTheDocument();
    });
  });

  test('sends message successfully', async () => {
    (bodhijs.isInstalled as any).mockReturnValue(true);
    (bodhijs.ping as any).mockResolvedValue({ message: 'pong' });
    (bodhijs.chat.completions.create as any).mockResolvedValue({
      choices: [{ message: { content: 'Hello response' } }]
    });

    render(<ChatComponent />);
    
    await waitFor(() => {
      expect(screen.queryByText(/checking system/i)).not.toBeInTheDocument();
    });

    const input = screen.getByPlaceholderText(/type your message/i);
    const sendButton = screen.getByText(/send/i);

    fireEvent.change(input, { target: { value: 'Hello' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText(/hello response/i)).toBeInTheDocument();
    });
  });
});
```

### Integration Testing with Test App

The bodhi-js library includes a test app that can be used for integration testing:

```typescript
// Integration test setup
import { sendApiRequest } from '@bodhiapp/bodhijs';

const testApiIntegration = async () => {
  // Test ping
  const pingResponse = await sendApiRequest({
    method: 'GET',
    endpoint: '/ping'
  });
  
  console.log('Ping response:', pingResponse.data);
  
  // Test chat completion
  const chatResponse = await sendApiRequest({
    method: 'POST',
    endpoint: '/v1/chat/completions',
    headers: {
      'Authorization': 'Bearer YOUR_API_TOKEN',
      'Content-Type': 'application/json'
    },
    body: {
      model: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M',
      messages: [{ role: 'user', content: 'Hello' }],
      stream: false
    }
  });
  
  console.log('Chat response:', chatResponse.data);
};
```

## Styling Examples

### CSS for Chat Interface

```css
/* styles/chat.css */
.chat-app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.chat-container {
  height: 600px;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.message {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  max-width: 80%;
}

.message.user {
  background-color: #007bff;
  color: white;
  margin-left: auto;
  text-align: right;
}

.message.assistant {
  background-color: white;
  border: 1px solid #e0e0e0;
  margin-right: auto;
}

.message.loading {
  opacity: 0.8;
  font-style: italic;
}

.input-area {
  display: flex;
  padding: 20px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
}

.input-area textarea {
  flex: 1;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
  min-height: 60px;
  max-height: 120px;
}

.input-area button {
  margin-left: 10px;
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
}

.input-area button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.app-loading, .app-error {
  text-align: center;
  padding: 60px 20px;
}

.app-error {
  color: #dc3545;
}

.loading {
  text-align: center;
  font-style: italic;
  color: #666;
  padding: 10px;
}
```

## Deployment Considerations

### Build Configuration

Ensure your bundler (Webpack, Vite, etc.) is configured to handle the bodhijs library:

```javascript
// webpack.config.js
module.exports = {
  // ... other config
  resolve: {
    fallback: {
      // bodhijs doesn't need Node.js polyfills
    }
  }
};

// vite.config.js
export default {
  // ... other config
  define: {
    // Ensure window object is available
    global: 'globalThis',
  }
};
```

### Environment Variables

```typescript
// config/env.ts
export const isDevelopment = process.env.NODE_ENV === 'development';
export const defaultModel = process.env.REACT_APP_DEFAULT_MODEL || 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M';
export const apiToken = process.env.REACT_APP_BODHI_API_TOKEN;

// Use in components
const modelId = isDevelopment ? 'test-model' : defaultModel;
```

## Best Practices Summary

1. **Always Check Extension Status**: Verify the extension is installed and the server is available before making API calls
2. **Handle Errors Gracefully**: Provide clear error messages and recovery options for users
3. **No Streaming Support**: The current version does not support streaming responses
4. **Manage Context Length**: Limit conversation history to prevent token limit issues
5. **Optimize Re-renders**: Use React.memo, useMemo, and useCallback for performance
6. **Type Safety**: Leverage TypeScript interfaces provided by bodhijs
7. **Test Thoroughly**: Include both unit tests with mocks and integration tests
8. **User Experience**: Provide loading states, error recovery, and clear status indicators
9. **Authentication**: Handle API tokens securely for authenticated endpoints
10. **Model Selection**: Allow users to select from available models

## Common Patterns and Solutions

### Auto-retry on Connection Loss

```typescript
const useAutoReconnect = () => {
  const [isConnected, setIsConnected] = useState(false);
  
  useEffect(() => {
    const checkConnection = async () => {
      try {
        if (isInstalled()) {
          await ping();
          setIsConnected(true);
        }
      } catch {
        setIsConnected(false);
        // Retry after 5 seconds
        setTimeout(checkConnection, 5000);
      }
    };
    
    checkConnection();
  }, []);
  
  return isConnected;
};
```

### Context-Aware Conversations

```typescript
const useContextManager = () => {
  const [context, setContext] = useState<Message[]>([]);
  
  const addToContext = (message: Message) => {
    setContext(prev => {
      const newContext = [...prev, message];
      // Keep last 10 messages to manage token usage
      return newContext.slice(-10);
    });
  };
  
  const getContextForAPI = () => {
    return context.map(msg => ({ role: msg.role, content: msg.content }));
  };
  
  return { context, addToContext, getContextForAPI };
};
```

## API Reference Summary

The bodhi-js library provides the following main functions:

- `isInstalled()`: Check if the Bodhi Browser Extension is installed
- `isServerAvailable()`: Check if the BodhiApp server is running
- `ping()`: Test connectivity with the server
- `chat.completions.create(request)`: Create chat completions (no streaming)
- `sendApiRequest(options)`: Make generic API requests to the BodhiApp server

The library integrates with the BodhiApp server APIs including:
- OpenAI-compatible endpoints (`/v1/`)
- Bodhi-specific endpoints (`/bodhi/v1/`)
- Authentication via API tokens
- Model management for Power Users

This comprehensive guide provides AI coding assistants with all the necessary information to help developers build robust, feature-rich AI applications using the current version of the bodhijs library. 