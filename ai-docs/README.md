# Bodhi Browser Documentation

This directory contains comprehensive documentation for the Bodhi Browser project, designed to provide context for both human developers and AI systems. These files help explain the purpose, architecture, implementation details, and usage patterns of the Bodhi Browser system.

## Documentation Index

### Core Architecture and Domain

#### [context/architecture.md](./context/architecture.md)

Technical architecture of the Bodhi Browser system, including component structure, message flow, communication patterns, build processes, and testing infrastructure. Covers the complete system architecture with focus on project-specific implementation details.

#### [context/domain.md](./context/domain.md)

Problem domain, system components, use cases, and deployment model. Explains how browser security limitations create the need for this extension and how the components work together to solve the problem.

### Component Implementation

#### [context/bodhi-browser-ext.md](./context/bodhi-browser-ext.md)

Detailed implementation documentation for the `bodhi-browser-ext` Chrome extension, including API methods, unified message formats, streaming implementation, configuration management, and comprehensive testing patterns.

#### [context/bodhi-js.md](./context/bodhi-js.md)

Implementation documentation for the `@bodhiapp/bodhijs` NPM library, including API structure, extension detection, error handling, TypeScript integration, and current limitations.

### Technical Specifications

#### [context/extension-message-format.md](./context/extension-message-format.md)

Comprehensive specification for the unified message format used throughout the extension for communication between inject script, content script, and background script.

#### [context/README.md](./context/README.md)

Index and overview of all context documentation files with usage guidelines for AI coding assistants and developers.

### Development Infrastructure

#### [mock-llm-server.md](./mock-llm-server.md)

Documentation for the mock LLM server used in testing, providing OpenAI-compatible endpoints for development and CI environments.

#### [linting.md](./linting.md)

Code quality standards and linting configuration across the project components.

### Research and Design

#### [research/](./research/)

Research documentation analyzing successful patterns from other integration libraries and providing data-driven recommendations for architectural decisions. Includes comprehensive analysis of embedded UI onboarding patterns and concrete interface proposals for new features.

## Feature Implementation Status

| Feature                      | Status            | Component(s)                |
| ---------------------------- | ----------------- | --------------------------- |
| Chrome Extension Bridge      | Implemented       | bodhi-browser-ext           |
| Unified Message Format       | Implemented       | bodhi-browser-ext           |
| Streaming API Support        | Implemented (ext) | bodhi-browser-ext           |
| Extension Detection          | Implemented       | bodhijs                     |
| Configuration UI             | Implemented       | bodhi-browser-ext (Next.js) |
| Extension-to-Extension Comm. | Implemented       | bodhi-browser-ext           |
| Testing Infrastructure       | Implemented       | Both components             |
| Real Browser Testing         | Implemented       | Vitest + Playwright         |
| Mock LLM Server              | Implemented       | @bodhiapp/mock-llm-server   |

## Project Components

### bodhi-browser-ext (Chrome Extension)

- **Purpose**: Bridge between web pages and local Bodhi App servers
- **Architecture**: Background script + Content script + Inject script + Next.js UI
- **Key Features**: Unified message format, streaming support, configuration UI, extension-to-extension communication
- **Testing**: Comprehensive test suite with real browser automation

### bodhijs (NPM Library)

- **Purpose**: JavaScript/TypeScript library for web applications
- **Package**: `@bodhiapp/bodhijs`
- **Key Features**: Extension detection, API abstraction, structured error handling
- **Current Limitation**: No streaming support (extension supports streaming)
- **Testing**: Integration tests with real extension and servers

### Testing Infrastructure

- **Framework**: Vitest with Playwright for browser automation
- **Approach**: Real browser testing with actual extension loading
- **Mock Server**: OpenAI-compatible endpoints for controlled testing
- **Integration**: Tests with real Bodhi servers using NAPI bindings

## Using This Documentation

### For AI Coding Assistants

These documents provide project-specific context to help AI systems understand:

- Current implementation state and patterns
- Component relationships and communication flows
- Testing approaches and infrastructure
- Build processes and deployment considerations
- Project-specific conventions and limitations

Focus on the `context/` folder for implementation details and current state information.

### For New Developers

Start with this recommended reading order:

1. **[context/domain.md](./context/domain.md)** - Understand the problem space and system components
2. **[context/architecture.md](./context/architecture.md)** - Grasp the technical architecture and communication flows
3. **[context/bodhi-browser-ext.md](./context/bodhi-browser-ext.md)** or **[context/bodhi-js.md](./context/bodhi-js.md)** - Dive into specific component implementation
4. **[context/extension-message-format.md](./context/extension-message-format.md)** - Understand the communication protocol

### For API Integration

Focus on:

- **[context/bodhi-js.md](./context/bodhi-js.md)** for library usage patterns
- **[context/bodhi-browser-ext.md](./context/bodhi-browser-ext.md)** for direct extension API usage
- **[context/extension-message-format.md](./context/extension-message-format.md)** for extension-to-extension communication

### For Extension Development

Reference:

- **[context/bodhi-browser-ext.md](./context/bodhi-browser-ext.md)** for implementation patterns
- **[context/architecture.md](./context/architecture.md)** for system design
- **[context/extension-message-format.md](./context/extension-message-format.md)** for message protocols

## Documentation Maintenance

These documents reflect the current state of the codebase and are updated to match actual implementation. They focus on:

- **Current State Only**: No planned features or deprecated functionality
- **Project-Specific Details**: Implementation patterns unique to this project
- **Source Code Truth**: Documentation verified against actual source code
- **AI Assistant Context**: Information needed for effective code assistance

## Quick Reference

| Need                     | Document                                                                     | Key Information                           |
| ------------------------ | ---------------------------------------------------------------------------- | ----------------------------------------- |
| System Overview          | [context/domain.md](./context/domain.md)                                     | Components, use cases, limitations        |
| Technical Architecture   | [context/architecture.md](./context/architecture.md)                         | Communication flows, build processes      |
| Extension Implementation | [context/bodhi-browser-ext.md](./context/bodhi-browser-ext.md)             | API methods, streaming, testing           |
| Library Implementation   | [context/bodhi-js.md](./context/bodhi-js.md)               | NPM package, error handling, types        |
| Message Protocol         | [context/extension-message-format.md](./context/extension-message-format.md) | Unified message format                    |
| Testing Setup            | [mock-llm-server.md](./mock-llm-server.md)                                   | Mock server for testing                   |
| Design Research          | [research/](./research/)                                                     | Embedded UI patterns, interface proposals |

## Integration & Testing

Integration and end-to-end tests are provided using Playwright and Vitest, with a mock LLM server for local testing and real server integration for comprehensive testing. See component-specific documentation for detailed testing patterns.

## Tech Stack Summary

- **Extension**: Chrome Manifest V3, TypeScript, Webpack, Next.js + React + TailwindCSS
- **Library**: TypeScript, Rollup (CJS/ESM/UMD), Vitest
- **Testing**: Vitest + Playwright, real browser automation, mock and real server integration
- **Build**: Component-specific build processes with shared testing infrastructure
