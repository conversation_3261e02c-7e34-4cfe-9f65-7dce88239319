# Bodhi Browser Extension Implementation

The `bodhi-browser-ext` is a Manifest V3 Chrome extension that bridges web pages and locally running Bodhi App servers through a sophisticated three-layer communication architecture. It provides secure API access with both synchronous and asynchronous streaming patterns, supporting web page integration and extension-to-extension communication.

## Architecture Components

### Component Structure

```
bodhi-browser-ext/
├── src-ext/                    # Extension scripts (TypeScript)
│   ├── background.ts           # Service worker (background script)
│   ├── content.ts              # Content script (injected into pages)
│   ├── inject.ts               # Page script (creates window.bodhiext)
│   ├── shared/
│   │   ├── constants.ts        # Shared constants and message types
│   │   ├── types.ts            # TypeScript type definitions
│   │   └── utils.ts            # Shared utility functions
│   └── webpack.config.js       # Build configuration for extension scripts
├── src/                        # Next.js UI (TypeScript/React)
│   ├── components/Settings/    # Settings component for popup
│   └── pages/                  # Next.js pages
├── public/
│   ├── manifest.json           # Extension manifest
│   └── icons/                  # Extension icons
└── tests/                      # Comprehensive test suite
    ├── test-extension/         # Test extension for ext2ext testing
    ├── test-page/              # HTML test pages
    └── *.test.ts               # Vitest test files
```

### Communication Architecture

The extension uses a three-layer communication system:

1. **Web Page ↔ Inject Script**: `window.bodhiext` API with `window.postMessage`
2. **Inject Script ↔ Content Script**: Message forwarding via `window.postMessage`
3. **Content Script ↔ Background Script**: Chrome runtime messaging

## Core Components

### Background Script (Service Worker)

**File**: `src-ext/background.ts`

**Purpose**: Handles HTTP requests to local Bodhi App servers and manages extension lifecycle.

**Key Features**:
- **Default Backend**: `http://localhost:1135` (configurable via Chrome storage)
- **Message Handling**: 
  - `chrome.runtime.onMessage` for standard API requests
  - `chrome.runtime.onConnect` for streaming requests
  - `chrome.runtime.onMessageExternal` and `chrome.runtime.onConnectExternal` for extension-to-extension communication
- **Streaming**: Uses `ReadableStream` and `TextDecoder` for Server-Sent Events (SSE) parsing
- **Error Handling**: Centralized error handling with structured responses
- **Configuration**: Listens for backend URL changes via `chrome.storage.onChanged`

**API Processing**:
```typescript
// Unified message format processing
const parseApiRequestMessage = (message: ApiRequestMessage): ApiRequest | ApiResponseMessage => {
  const { type, requestId, request } = message;
  // Validates required fields: type, requestId, request.method, request.endpoint
  // Returns structured error for malformed requests
}
```

### Content Script

**File**: `src-ext/content.ts`

**Purpose**: Bridge between web pages and extension background script.

**Key Features**:
- **Script Injection**: Automatically injects `inject.js` into page DOM when document is ready
- **Message Routing**: Forwards messages bidirectionally without modification
- **Stream Management**: Maintains active stream connections with automatic cleanup
- **Type Guards**: Uses TypeScript type guards for message validation

**Message Handlers**:
- `handleApiMessage()`: Forwards standard API requests
- `handleStreamingMessage()`: Establishes long-lived connections for streaming
- `handleGetExtensionId()`: Provides extension ID to pages

### Inject Script

**File**: `src-ext/inject.ts`

**Purpose**: Creates the `window.bodhiext` API that web applications use through an IIFE (Immediately Invoked Function Expression) to avoid global namespace pollution.

**API Methods**:
- `ping()`: Connectivity check returning `{message: 'pong'}`
- `serverState()`: Server status endpoint returning `ServerStateInfo` with status, version, and error details
- `chat.completions.create(params)`: OpenAI-compatible chat completions with streaming support
- `sendApiRequest(method, endpoint, body?, headers?)`: Generic API method returning Promise
- `sendStreamRequest(method, endpoint, body?, headers?)`: Generic streaming method returning ReadableStream
- `getExtensionId()`: Returns Chrome extension ID asynchronously

**Streaming Implementation**:
- Uses `ReadableStream` with `ReadableStreamDefaultController` for chunk processing
- Implements `AsyncIterableIterator` pattern for `for await` loops with `Symbol.asyncIterator`
- Stream timeout of 60 seconds vs 30 seconds for regular requests
- Automatic cleanup via stream cancellation and connection closure
- Stream controller maintains enqueue/error/complete methods for chunk management

**Request Management**:
- Private request tracking with `Map<string, RequestCallbacks>` for pending requests
- Private stream tracking with `Map<string, StreamController>` for active streams
- Secure request ID generation using `crypto.randomUUID()` with Math.random() fallback
- Proper timeout handling with `setTimeout` and automatic cleanup

**Security Features**:
- `PublicBodhiExtInterface` exposed with frozen object and non-configurable property
- Private methods encapsulated in `_private` object not accessible to web pages
- Origin validation using `window.origin` with wildcard fallback
- Event-driven extension ID initialization with `EVENT_INITIALIZED` custom event

### Next.js UI

**Files**: `src/components/Settings/`, `src/pages/`

**Purpose**: Provides configuration interface via extension popup.

**Features**:
- **Backend URL Configuration**: Input field with validation
- **Real-time Feedback**: Success/error messages for configuration changes
- **Persistent Storage**: Uses Chrome storage API for settings
- **Build Process**: Static export compatible with Chrome extension restrictions

**Settings Component**:
- **Dimensions**: Fixed 350x400px popup window with responsive design  
- **Styling**: TailwindCSS with gradient background and white card layout
- **State Management**: React hooks for URL input, saving state, and message display
- **Validation**: `new URL()` constructor for URL format validation
- **Storage**: Chrome storage local API with immediate persistence

```typescript
// URL validation and storage implementation
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  setIsSaving(true);
  try {
    new URL(backendUrl); // Validate URL format
    await chrome.storage.local.set({ backendUrl });
    setMessage({ type: 'success', text: 'Settings saved successfully!' });
  } catch {
    setMessage({
      type: 'error', 
      text: `Input url '${backendUrl}' is not a valid URL. Please enter a valid URL`
    });
  } finally {
    setIsSaving(false);
  }
};
```

**Manifest Configuration**:
```json
{
  "manifest_version": 3,
  "name": "Bodhi Browser Chrome Extension",
  "version": "0.0.5",
  "background": {
    "service_worker": "background.js",
    "type": "module"
  },
  "content_scripts": [{
    "matches": ["https://*/*", "http://*/*"],
    "js": ["content.js"],
    "run_at": "document_idle"
  }],
  "host_permissions": ["<all_urls>"],
  "permissions": ["storage"],
  "web_accessible_resources": [{
    "resources": ["index.html", "inject.js"],
    "matches": ["https://*/*", "http://*/*"]
  }],
  "externally_connectable": {
    "matches": ["*://*/*"],
    "ids": ["*"],
    "accepts_tls_channel_id": false
  },
  "action": {
    "default_popup": "index.html"
  }
}
```

## Message Format

### Unified Message Structure

All communication uses a consistent message format:

```typescript
// Request messages
{
  type: 'BODHI_API_REQUEST' | 'BODHI_STREAM_REQUEST',
  requestId: string,                    // Unique ID for request correlation
  request: {
    method: string,                     // HTTP method
    endpoint: string,                   // API endpoint
    body?: any,                         // Request body
    headers?: Record<string, string>    // HTTP headers
  }
}

// Response messages
{
  type: 'BODHI_API_RESPONSE' | 'BODHI_STREAM_CHUNK' | 'BODHI_STREAM_ERROR',
  requestId: string,                    // Matches request ID
  response: {
    body: any,                          // Response data or error
    status: number,                     // HTTP status code
    headers: Record<string, string>     // Response headers
  }
}
```

### Message Types

Defined in `src-ext/shared/constants.ts`:

```typescript
export const MESSAGE_TYPES = {
  API_REQUEST: 'BODHI_API_REQUEST',
  API_RESPONSE: 'BODHI_API_RESPONSE',
  STREAM_REQUEST: 'BODHI_STREAM_REQUEST',
  STREAM_CHUNK: 'BODHI_STREAM_CHUNK',
  STREAM_ERROR: 'BODHI_STREAM_ERROR',
  ERROR: 'BODHI_ERROR',
  GET_EXTENSION_ID: 'BODHI_GET_EXTENSION_ID',
  SET_EXTENSION_ID: 'BODHI_SET_EXTENSION_ID',
};
```

### Shared Constants and Configuration

**Core Configuration** (`src-ext/shared/constants.ts`):
- `DEFAULT_API_BASE_URL`: `'http://localhost:1135'` - Default Bodhi server endpoint
- `DEFAULT_REQUEST_TIMEOUT`: `30000` (30 seconds) - Standard API request timeout
- `BODHI_STREAM_PORT`: `'BODHI_STREAM_PORT'` - Chrome runtime port name for streaming
- `STORAGE_KEY_BACKEND_URL`: `'backendUrl'` - Chrome storage key for backend configuration
- `SSE_CHUNK_DELIMITER`: `'\n\n'` - Server-Sent Events chunk separator

**HTTP Constants**:
- `HTTP_METHOD_GET`, `HTTP_METHOD_POST` - HTTP method constants
- `ENDPOINT_PING`: `'/ping'` - Health check endpoint
- `ENDPOINT_CHAT_COMPLETIONS`: `'/v1/chat/completions'` - OpenAI-compatible chat endpoint

**State and Event Constants**:
- `DOCUMENT_STATE_COMPLETE`: `'complete'` - Document ready state
- `EVENT_INITIALIZED`: `'bodhi-ext-initialized'` - Extension initialization event
- `ORIGIN_WILDCARD`: `'*'` - Wildcard origin for message passing

**Shared Type Definitions** (`src-ext/shared/types.ts`):
```typescript
export interface ServerStateInfo {
  status: 'setup' | 'ready' | 'resource-admin' | 'error' | 'unreachable';
  version?: string;
  url?: string;
  error?: {
    message: string;
    type?: string;
    code?: string;
    param?: string;
  };
}

export interface SSEChunk {
  done?: boolean;
  [key: string]: any;
}

export interface StreamController {
  enqueue: (chunk: any) => void;
  error: (err: Error) => void;
  complete: () => void;
}
```

**Shared Utility Functions** (`src-ext/shared/utils.ts`):
```typescript
// SSE processing utilities
export const processSSEChunk = (chunk: string): any | null
export const createLogger = (component: string) // Consistent logging system
export const createRequestOptions = (method: string, headers: any, body: any): RequestInit
export const parseResponse = (response: Response): Promise<ApiResponse>
export const makeHttpRequest = (url: string, options: RequestInit): Promise<Response>
export const handleFetchError = (error: Error, responseHandler: Function, requestId: string, isStream: boolean)

// Error handling utilities
export const createErrorWithDetails = (message: string, status: number, body: any): Error
export const getErrorMessageFromResponse = (response: ApiResponse, fallback: string): string
export const isSuccessResponse = (status: number): boolean
export const createErrorResponse = (message: string, requestId: string, isStream?: boolean): ErrorMessage
export const handleError = (error: Error, context: string, metadata?: any): Error
```

## API Implementation

### Standard API Flow

```
Web Page → window.bodhiext.ping()
  ↓ createRequest with unique ID
Inject Script → window.postMessage(BODHI_API_REQUEST)
  ↓ forward message
Content Script → chrome.runtime.sendMessage()
  ↓ HTTP request
Background Script → fetch(http://localhost:1135/ping)
  ↓ parse response
Background Script → sendResponse(BODHI_API_RESPONSE)
  ↓ forward response
Content Script → window.postMessage()
  ↓ resolve Promise
Inject Script → return response.body
  ↓ 
Web Page ← {message: 'pong'}
```

### Streaming API Flow

```
Web Page → window.bodhiext.chat.completions.create({stream: true})
  ↓ return AsyncIterator
Inject Script → ReadableStream + window.postMessage(BODHI_STREAM_REQUEST)
  ↓ establish connection
Content Script → chrome.runtime.connect({name: 'BODHI_STREAM_PORT'})
  ↓ streaming HTTP request
Background Script → fetch with SSE parsing
  ↓ process chunks
Background Script → port.postMessage(BODHI_STREAM_CHUNK)
  ↓ forward chunks
Content Script → window.postMessage()
  ↓ enqueue chunks
Inject Script → ReadableStream controller
  ↓ yield chunks
Web Page ← for await (const chunk of stream) { ... }
```

### Extension-to-Extension Communication

Other Chrome extensions can communicate with the Bodhi extension:

**Standard API**:
```typescript
chrome.runtime.sendMessage(bodhiExtensionId, {
  type: 'BODHI_API_REQUEST',
  requestId: 'unique-id',
  request: {
    method: 'GET',
    endpoint: '/ping'
  }
});
```

**Streaming API**:
```typescript
const port = chrome.runtime.connect(bodhiExtensionId, {name: 'BODHI_CHANNEL'});
port.postMessage({
  type: 'BODHI_STREAM_REQUEST',
  requestId: 'unique-id',
  request: {
    method: 'POST',
    endpoint: '/v1/chat/completions',
    body: { messages: [...], stream: true }
  }
});
```

## Configuration Management

### Backend URL Configuration

- **Storage Key**: `chrome.storage.local` with key `STORAGE_KEY_BACKEND_URL` (`'backendUrl'`)
- **Default**: `DEFAULT_API_BASE_URL` (`http://localhost:1135`)
- **Validation**: URL format validation using `new URL()` constructor in settings UI
- **Dynamic Updates**: Background script listens via `chrome.storage.onChanged` for real-time application
- **Settings Persistence**: URL loaded on component mount and saved on form submission
- **Error Feedback**: Detailed error messages for invalid URLs with original input displayed

### Settings UI

- **Access**: Extension popup (`chrome-extension://{id}/index.html`)
- **Framework**: Next.js with static export
- **Styling**: TailwindCSS with responsive design
- **Feedback**: Real-time success/error messages

## Build Process

### Extension Scripts Build

**Tool**: Webpack 5 with TypeScript  
**Configuration**: `src-ext/webpack.config.js`

**Entry Points and Output**:
```typescript
entry: {
  background: path.resolve(__dirname, 'background.ts'),
  content: path.resolve(__dirname, 'content.ts'),
  inject: path.resolve(__dirname, 'inject.ts'),
}
output: {
  path: path.resolve(__dirname, '../dist'),
  filename: '[name].js',
  clean: false, // Don't clear dist folder (shared with UI build)
}
```

**Build Features**:
- **TypeScript Processing**: ts-loader with extension-specific configuration
- **Module Resolution**: `.js` extension alias for `.ts` files (`extensionAlias`)
- **ES Module Target**: `es2020` with `esnext` modules for Chrome extension compatibility
- **Code Splitting Disabled**: `splitChunks: false, runtimeChunk: false` for extension requirements
- **Output Module Experiments**: `experiments.outputModule: true` for modern module output

**Production Optimizations**:
- **webpack-obfuscator**: Comprehensive obfuscation with control flow flattening
- **String Array Encoding**: Base64 encoding with 75% threshold
- **Dead Code Injection**: 40% threshold for security
- **Chrome Extension Safe**: `selfDefending: false, debugProtection: false` to prevent extension breakage

### UI Build

**Tool**: Next.js 15 with Static Export  
**Configuration**: `next.config.ts`

**Static Export Configuration**:
```typescript
const nextConfig: NextConfig = {
  reactStrictMode: true,
  output: 'export',        // Static export for Chrome extension
  distDir: './dist',       // Shared output directory with extension scripts
}
```

**Build Features**:
- **React 19**: Latest React with TypeScript support
- **TailwindCSS 4**: Utility-first CSS framework with PostCSS processing
- **Path Correction**: `fix-paths.mjs` post-build script for Chrome extension compatibility
- **Production Obfuscation**: webpack-obfuscator applied to client-side bundles only
- **Extension-Safe Obfuscation**: Excludes critical files like `_app`, `_document`, `_error`

## Error Handling

### Structured Error Format

All errors follow consistent structure:

```typescript
{
  type: 'BODHI_API_RESPONSE' | 'BODHI_STREAM_ERROR',
  requestId: string,
  response: {
    body: {
      error: {
        message: string
      }
    },
    status: number,    // 0 for network errors, HTTP status otherwise
    headers: {}
  }
}
```

### Error Categories

1. **Network Errors**: Connection failures to Bodhi App (status: 0)
2. **HTTP Errors**: Server errors with HTTP status codes
3. **Validation Errors**: Malformed requests (status: 0)
4. **Timeout Errors**: Request timeouts (30s) or stream timeouts (60s)

### Logging System

**Development Mode**:
- Detailed console logging with context
- Format: `[Bodhi/{component}] {message}`
- Structured data logging for debugging

**Production Mode**:
- Error logging only
- No sensitive information in logs

## Testing Infrastructure

### Test Framework

- **Framework**: Vitest with Playwright for browser automation
- **Browser**: Real Chrome/Chromium with extension loaded
- **Isolation**: Fresh browser context per test suite

### Test Categories

1. **Web-to-Extension** (`web2ext.test.ts`):
   - Extension detection and API functionality
   - Standard and streaming API calls
   - Error handling scenarios

2. **Extension-to-Extension** (`ext2ext.test.ts`):
   - Extension discovery and messaging
   - Standard and streaming communication
   - Test extension interaction

3. **Settings** (`settings.test.ts`):
   - Backend URL configuration
   - Settings persistence and validation
   - UI feedback testing

4. **Real Server Integration** (`web2ext-real-llm-server.test.ts`):
   - Authentication with real Bodhi servers
   - Actual LLM model interactions
   - End-to-end testing

### Test Infrastructure Components

**Mock LLM Server**:
- Provides OpenAI-compatible endpoints (`/ping`, `/v1/chat/completions`)
- Supports streaming responses with SSE format
- Automatic startup/shutdown in test suites

**Test Pages**:
- HTML pages in `tests/test-page/` for UI-driven testing
- JavaScript helpers in `tests/test-page/js/common.js`
- CSS styling for test interface

**Test Extension**:
- Companion extension in `tests/test-extension/` for ext2ext testing
- Provides `window.bodhiTestExtension` API
- Tests extension-to-extension communication patterns

### Test Helpers

**Browser Management**:
```typescript
// Create browser with extension loaded
const browserManager = await createBrowserManager({
  loadTestExtension: false,  // or true for ext2ext tests
  staticPort,
  mockPort
});

const page = await browserManager.createPage();
```

**Extension Detection**:
```typescript
// Wait for extension to be ready
await waitForBodhiExtInfo(page);

// Get extension ID
const extensionId = await waitForExtensionId(page);
```

## Performance Considerations

### Request Optimization

- **Timeout Management**: 30-second timeout for API requests, 60-second for streaming
- **Connection Reuse**: HTTP keep-alive for multiple requests
- **Resource Cleanup**: Automatic cleanup of expired requests and streams

### Streaming Optimization

- **SSE Chunk Processing**: `TextDecoder` with streaming mode for UTF-8 decoding
- **Buffer Management**: Accumulates partial chunks until complete SSE events are received
- **Chunk Delimiter**: Uses `'\n\n'` delimiter to separate complete SSE events
- **Processing Flow**: Splits buffer on delimiters, processes complete chunks, retains incomplete
- **Done Signal Handling**: Detects `done: true` in chunks to terminate streams properly
- **Memory Management**: Automatic cleanup of reader resources and stream controllers

### Build Optimization

- **Code Splitting**: Disabled for extension compatibility
- **Tree Shaking**: Webpack tree shaking for smaller bundles
- **Obfuscation**: Production code obfuscation for security

## Security Implementation

### Content Security Policy

**Extension Pages**: `script-src 'self'; object-src 'self'; connect-src *`

### Message Security

- **Origin Validation**: Content script validates message origins
- **Request ID Correlation**: Unique IDs prevent message confusion
- **Interface Protection**: `window.bodhiext` is frozen and non-configurable

### Permissions

**Manifest Permissions**:
- `storage`: For backend URL configuration
- `host_permissions: ["<all_urls>"]`: For LLM server connectivity

**External Connectivity**:
- `externally_connectable`: Allows other extensions to connect
- `ids: ["*"]`: Permits any extension to communicate

## Current Limitations

1. **Browser Support**: Chrome/Chromium only (Manifest V3)
2. **Single Backend**: Only one backend URL supported at a time
3. **Stream Cancellation**: Streaming requests cannot be cancelled mid-stream
4. **Local Server Dependency**: Requires local Bodhi App to be running
