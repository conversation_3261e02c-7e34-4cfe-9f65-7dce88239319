# Bodhi JS Test App React - Current State Analysis

## Overview

The Bodhi JS Test App React (`bodhi-js/tests/bodhijs-test-app-react`) is a React-based test application that demonstrates and validates the @bodhiapp/bodhijs library functionality. It serves as both a testing harness for the library's core features and a development tool for debugging Bodhi platform integration. The app runs on port 12345 and requires the Bodhi browser extension to be installed for full functionality.

## Application Architecture

### Technology Stack

**Dependencies and Build System:**
- React 19.1.1 with React Router DOM 7.7.1 for SPA routing
- TypeScript 5.8.3 for type safety throughout the application
- Vite 7.0.6 as the build tool and development server
- @bodhiapp/bodhijs referenced via `file:../../` (local dependency)
- ESLint 9.32.0 with React-specific plugins for code quality

**Development Configuration:**
- Fixed port 12345 for both development and preview servers
- Path alias `@` pointing to `src` directory
- Custom Vite plugin to copy `index.html` as `404.html` for SPA routing
- `build:fast` script using custom build optimization

### Application Structure

**Core Routes:**
- `/` - LandingPage: Main dashboard with platform detection and authentication
- `/callback` - CallbackPage: OAuth callback handler for authentication flow  
- `/api-test` - ApiTestPage: Interactive API testing interface
- `/onboarding-test` - OnboardingTestPage: Onboarding modal testing environment

**Directory Organization:**
- `src/components/` - Reusable UI components (AuthenticationSection, PlatformStatus, ServerStatus)
- `src/hooks/` - Custom React hooks (usePlatformDetection, useAuthentication)
- `src/pages/` - Route-level page components
- `src/utils/` - Utility modules (oauth.ts for OAuth management)
- `src/lib/` - Type definitions (onboarding-types.ts)

## Core Functionality

### Landing Page (`src/pages/LandingPage.tsx`)

**Primary Purpose:** Central dashboard combining platform detection, authentication, and navigation.

**Key Features:**
- Real-time platform status display using `usePlatformDetection` hook
- Authentication workflow via `AuthenticationSection` component  
- Platform retry detection with window reload
- Modal triggering via `platform.showOnboarding()` with dismissible configuration
- Navigation buttons to API test and onboarding test pages

**Platform Integration:**
- Instantiates `BodhiPlatform` with 3-second timeout
- Monitors platform state through `platformState.isReady()` boolean check
- Displays platform status (detecting, detected, setup, timeout, error)
- Shows setup modal when platform not ready

### API Test Page (`src/pages/ApiTestPage.tsx`)

**Primary Purpose:** Interactive testing interface for Bodhi extension API functionality.

**Extension Detection:**
- Independent platform detection using `BodhiPlatform` with 3-second timeout
- Extension status display (detecting, detected, setup, error, timeout)
- Extension ID extraction via `client.getExtensionId()`
- Retry detection with page reload

**API Request Interface:**
- HTTP method selector (GET, POST, PUT, PATCH, DELETE)
- Endpoint input field with default `/ping`
- JSON request body textarea with syntax handling
- Custom headers input (one per line format)
- Authentication token inclusion checkbox
- Streaming mode toggle

**Response Handling:**
- Standard requests via `client.sendApiRequest()`
- Streaming requests via `client.sendStreamRequest()` with async iteration
- Real-time streaming content accumulation for chat completions
- Complete response display: status, headers, formatted JSON body
- Separate error handling for API failures

**State Management:**
- Complex TypeScript interfaces for extension and API states  
- Form persistence across interactions
- Real-time status indicators (ready, calling, streaming, completed, error)

### Onboarding Test Page (`src/pages/OnboardingTestPage.tsx`)

**Primary Purpose:** Dedicated testing environment for onboarding modal functionality.

**Platform Management:**
- Platform initialization on component mount via `initializePlatform()`
- Manual re-initialization capability with loading state
- Platform state persistence across modal interactions

**Modal Testing:**
- Modal trigger via `platform.showOnboarding()` with dismissible configuration  
- Event callback logging with timestamps for onComplete, onDismiss, onRedetection
- Real-time event log display in monospace formatting
- Clear logs functionality for test reset

**Event Logging:**
- Comprehensive callback handling with state information
- Platform re-initialization on modal completion and redetection
- Timestamped event messages for debugging modal behavior

### Callback Page (`src/pages/CallbackPage.tsx`)

**Primary Purpose:** OAuth callback handler for authentication flow completion.

**URL Parameter Processing:**
- Extraction of `code`, `state`, and `error` parameters from URL search params
- Error parameter handling from OAuth provider
- State validation for security

**Token Exchange:**
- Token exchange via `oauthManager.exchangeCodeForTokens(code, state)`
- Automatic redirection to home page on successful authentication
- Error state management with user-friendly messages

**UI States:**
- Loading state during URL parameter processing
- Processing state during token exchange
- Success state with redirect message
- Error state with retry and navigation options

## Component System

### Authentication Section (`src/components/AuthenticationSection.tsx`)

**Purpose:** Manages OAuth authentication UI and user session display.

**Authentication Flow:**
- Login button triggers `oauthManager.requestResourceAccess()` then redirects to OAuth provider
- User info display after successful authentication (email, role, token type)
- Logout functionality with localStorage cleanup
- Navigation to API test page when authenticated

**Integration:**
- Requires platform client for resource access requests
- Uses `useAuthentication` hook for state management
- Handles authentication errors with user-friendly messages

### Platform Status Components

**Components:** `PlatformStatus.tsx`, `ServerStatus.tsx` (referenced but not present in current codebase)

**Note:** These components are referenced in the existing documentation but were not found in the current source code analysis. The current implementation appears to handle platform status display directly within page components rather than through dedicated status components.

## Hook System

### Platform Detection Hook (`src/hooks/usePlatformDetection.ts`)

**Purpose:** Manages platform detection lifecycle and state.

**State Interface:**
```typescript
interface PlatformDetectionState {
  status: 'detecting' | 'detected' | 'setup' | 'timeout' | 'error'
  platform: BodhiPlatform | null
  client: BodhiExtClient | null  
  extensionId: string | null
  error: string | null
  platformState: BodhiPlatformState | null
}
```

**Detection Logic:**
- Creates `BodhiPlatform` with 3-second timeout
- Calls `platform.initialize()` to get platform state
- Determines UI status using simplified `platformState.isReady()` check
- Extracts client and extension ID when platform ready
- Uses `determineUIState()` function for status mapping

### Authentication Hook (`src/hooks/useAuthentication.ts`) 

**Purpose:** Manages OAuth authentication lifecycle.

**State Interface:**
```typescript
interface AuthenticationState {
  status: 'unauthenticated' | 'authenticating' | 'authenticated' | 'error'
  userInfo: UserInfo | null
  error: string | null
  login: () => Promise<void>
  logout: () => void
}
```

**Authentication Flow:**
- Checks existing auth when platform client becomes available
- Login triggers resource access request followed by OAuth URL redirect
- Fetches user info after successful authentication
- Logout clears localStorage and resets state

## OAuth System

### OAuth Manager (`src/utils/oauth.ts`)

**Purpose:** Complete OAuth 2.0 + PKCE implementation for Bodhi App authentication.

**Configuration:**
- Client ID: `app-a05c53c5-3fc4-409d-833d-f4acc90e1611`
- Auth Server: `https://main-id.getbodhi.app`
- Realm: `bodhi`
- Redirect URI: `${window.location.origin}/callback`

**PKCE Implementation:**
- Code verifier generation (128 characters)
- SHA-256 code challenge generation
- State parameter for security
- Local storage for temporary values

**Token Management:**
- Access token and refresh token storage in localStorage
- User info caching
- Duplicate token exchange prevention
- Concurrent request protection via `isExchangingTokens` flag

**API Integration:**
- Resource access requests to `/bodhi/v1/auth/request-access`
- User info fetching from `/bodhi/v1/user`
- Authorization header management for authenticated requests

## Library Integration

### BodhiJS Library Usage

**Core Classes:**
- `BodhiPlatform`: Main platform detection and initialization
- `BodhiExtClient`: API client for extension communication  
- `BodhiPlatformState`: Platform state wrapper with `isReady()` method
- `BodhiOnboardingModal`: Modal functionality (accessed via `platform.showOnboarding()`)

**Type System:**
- Extensive use of library-provided TypeScript types
- `ApiResponse`, `StreamChunk`, `ChatRequest`, `ChatResponse` interfaces
- Platform detection types and error codes
- Authentication state types

**API Patterns:**
- Standard requests: `client.sendApiRequest(method, endpoint, body, headers)`
- Streaming requests: `client.sendStreamRequest()` with async iteration
- Platform detection: `platform.initialize()` with timeout configuration
- Modal integration: `platform.showOnboarding(config)` with callbacks

### Current State Summary

**Application Status:** The test app is a fully functional React application that demonstrates all core features of the @bodhiapp/bodhijs library. It provides comprehensive testing interfaces for platform detection, authentication, API interactions, streaming responses, and onboarding modal functionality.

**Key Capabilities:**
- Real-time platform and extension detection
- Complete OAuth 2.0 authentication flow with PKCE
- Interactive API testing with both standard and streaming requests
- Onboarding modal testing with event logging
- Comprehensive error handling and user feedback

**Testing Focus:** The app serves as both a development tool and integration test suite, providing visual interfaces for validating library functionality across different platform states and use cases.