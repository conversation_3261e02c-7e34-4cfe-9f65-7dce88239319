# Bodhi Platform Setup Wizard Modal - Application Behavior Documentation

## Overview

The Bodhi Platform Setup Wizard Modal is a React-based application designed to be embedded as an iframe component. It guides users through the setup process for connecting to local AI services provided by the Bodhi Platform, ensuring browser extensions are installed and local servers are configured.

## Application Architecture

### Entry Point & Initialization

**File: `src/index.tsx`**
- Renders the main `App` component into a DOM element with id "root"
- Uses `@/App` import path alias for clean imports
- Uses React 18's non-deprecated root API (`createRoot`) for mounting

**File: `src/index.css`**
- Implements Tailwind CSS with custom CSS variables for theming
- Defines light and dark mode color schemes via HSL color values
- CSS custom properties for semantic theming (background, foreground, borders)
- Dark mode support via selector-based approach

### Vite Configuration

**File: `vite.config.ts`**
- Configured with `@` path alias pointing to `./src` directory
- All imports throughout the codebase use `@/` prefix instead of relative paths
- Single-file build output with `vite-plugin-singlefile`
- All assets inlined with 100MB limit, uses Terser for compression
- Target: ESNext for modern browser compatibility

### Type System Architecture

**File: `src/lib/types.ts`**

All TypeScript interfaces and constants are consolidated into a single location:

#### Message Type Constants

```typescript
// Inbound messages (parent → modal)
export type ModalMessageType = 'modal:state';
export const MODAL_MESSAGE_STATE: ModalMessageType = 'modal:state';

// Outbound messages (modal → parent)
export type ModalOutMessageType = 'modal_out:ready' | 'modal_out:refresh' | 'modal_out:complete' | 'modal_out:close';
export const MODAL_OUT_READY: ModalOutMessageType = 'modal_out:ready';
export const MODAL_OUT_REFRESH: ModalOutMessageType = 'modal_out:refresh';
export const MODAL_OUT_COMPLETE: ModalOutMessageType = 'modal_out:complete';
export const MODAL_OUT_CLOSE: ModalOutMessageType = 'modal_out:close';
```

#### Error Code Constants

```typescript
// Server error codes with constants
export type ServerErrorCode = 'server-pending-ext-ready' | 'server-conn-refused' | 'server-conn-timeout' | 'server-not-found' | 'server-network-unreachable' | 'server-service-unavailable' | 'server-unexpected-error' | 'server-in-setup-status' | 'server-in-admin-status';
export const SERVER_PENDING_EXT_READY: ServerErrorCode = 'server-pending-ext-ready';
// ... additional constants

// Extension error codes with constants  
export type ExtensionErrorCode = 'ext-not-installed' | 'ext-connection-failed' | 'ext-unsupported-version';
export const EXT_NOT_INSTALLED: ExtensionErrorCode = 'ext-not-installed';
// ... additional constants
```

#### Step Navigation Enum

```typescript
export enum SetupStep {
  PLATFORM_CHECK = 'platform-check',
  EXTENSION_SETUP = 'extension-setup',
  SERVER_SETUP = 'server-setup',
  COMPLETE = 'complete'
}
```

#### Core State Interfaces

1. **`SetupState`** - Root state container:
   - `extension: ExtensionState` - Extension installation/connection status
   - `server: ServerState` - Server connection and configuration status  
   - `env: EnvState` - Environment detection (OS/browser)
   - `browsers: Browser[]` - Browser platform configurations (provided by parent)
   - `os: OS[]` - Operating system configurations (provided by parent)

2. **`Browser` (Discriminated Union)** - Browser platform definitions:
   - `SupportedBrowser`: id + status='supported' + name + extension_url
   - `NotSupportedBrowser`: id + status='not-supported' + name + optional github_issue_url
   - **Enhanced Unknown Browser Support**: Dynamic browser entries with actual browser names (e.g., "Brave", "Opera") and browser-specific GitHub issue URLs

3. **`OS` (Discriminated Union)** - Operating system platform definitions:
   - `SupportedOS`: id + status='supported' + name + download_url
   - `NotSupportedOS`: id + status='not-supported' + name + optional github_issue_url

4. **`ExtensionState` (Discriminated Union)**:
   - `ExtensionStateReady`: status='ready', required version & id
   - `ExtensionStateNotReady`: status='unreachable'|'not-installed'|'unsupported', required error

5. **`ServerState` (Discriminated Union)**:
   - `ServerStateReady`: status='ready', required version & url
   - `ServerStateReachable`: status='setup'|'resource-admin', required version & url & error
   - `ServerStatePending`: status='pending-extension-ready', required error
   - `ServerStateUnreachable`: status='unreachable', required error
   - `ServerStateError`: status='error', required error

6. **Error Structure**:
   - `message: string` - Human-readable error message
   - `code: ExtensionErrorCode | ServerErrorCode` - Typed error code constants

7. **Type Definitions**:
   - `BrowserType`: 'chrome'|'edge'|'firefox'|'safari'|'unknown' (removed 'other')
   - `OSType`: 'macos'|'windows'|'linux'|'unknown' (removed 'other')
   - `SetupStateOrNull`: `SetupState | null` - New nullable type for loading states

#### State Initialization & Loading States

**File: `src/App.tsx:5-25`**
```typescript
// Updated to use null initial state to fix timing issues
const [setupState, setSetupState] = useState<SetupStateOrNull>(null);
```

**Null State Loading Pattern**: Modal now starts with `null` state to prevent race conditions and synchronization issues. This ensures:
- No flash of incorrect default values (e.g., "Unknown Browser" when Safari is actually detected)
- Proper loading indicators while waiting for parent state
- Correct step navigation that doesn't bypass platform detection
- Reactive components that use live data instead of stale local state

**Loading State Handling**: All components now properly handle null state with loading spinners and defer rendering until real data arrives via postMessage.

**Modal Configuration Philosophy**: The modal starts with empty `browsers` and `os` arrays, making it completely configurable by the parent application. All platform support, URLs, and configuration data is provided dynamically via postMessage communication. **Enhanced Dynamic Support**: The parent can now inject actual browser names (e.g., "Brave", "Opera") for unknown browsers with browser-specific GitHub issue tracking. The modal is "very dumb" and reactive to state only.

### Iframe Communication System

**File: `src/hooks/useSetupModal.ts`**

The application implements a **minimal** bidirectional postMessage communication system via the useSetupModal hook:

#### Outbound Messages (Modal → Parent) - `modal_out:` prefix

**Essential Messages Only**:
1. **Ready Message** - Sent on component mount:
   ```javascript
   window.parent.postMessage({
     type: MODAL_OUT_READY,
     requestId: Date.now().toString()
   }, '*');
   ```

2. **Refresh Message** - User clicks refresh:
   ```javascript
   sendAction(MODAL_OUT_REFRESH);
   ```

3. **Complete Message** - Setup completed successfully:
   ```javascript
   sendAction(MODAL_OUT_COMPLETE);
   ```

4. **Close Message** - User closes modal:
   ```javascript
   sendAction(MODAL_OUT_CLOSE);
   ```

#### Inbound Messages (Parent → Modal) - `modal:` prefix

- **State Updates**: Messages with `type: MODAL_MESSAGE_STATE` trigger `setSetupState(event.data.data)`
- Modal is **completely passive** - never modifies its own state
- Parent manages all state validation and updates

### Component Hierarchy

#### Root Component - App

**File: `src/App.tsx`**
- Uses `useSetupModal` hook for state management and communication
- Pure composition pattern with minimal logic (12 lines total)
- Passes `setupState` and `sendAction` from hook to SetupWizard
- Clean separation between communication logic and UI rendering

#### Main Wizard Component - SetupWizard  

**File: `src/components/SetupWizard/SetupWizard.tsx`**

##### Step Navigation with Enum & Fixed Logic
- Uses `SetupStep` enum instead of numbers
- **Fixed initial step determination** - always starts with Platform Check for unsupported browsers:
  ```typescript
  const determineInitialStep = (state: SetupStateOrNull): SetupStep => {
    if (!state) return SetupStep.PLATFORM_CHECK;
    
    const { extension, server, env, browsers, os } = state;
    const isBrowserSupported = browsers.some(b => b.id === env.browser && b.status === 'supported');
    const isOSSupported = os.some(o => o.id === env.os && o.status === 'supported');
    
    // Always show platform check first if platform is uncertain or not fully supported
    if (!isBrowserSupported || !isOSSupported) {
      return SetupStep.PLATFORM_CHECK;
    }
    
    // Only proceed to other steps if platform is confirmed supported
    if (extension.status !== 'ready') return SetupStep.EXTENSION_SETUP;
    if (server.status !== 'ready') return SetupStep.SERVER_SETUP;
    return SetupStep.COMPLETE;
  };
  ```

**Key Fix**: Removed logic that bypassed Platform Check for unsupported browsers, ensuring proper flow through all setup steps.

##### Header with Controls
- **Logo display**: Base64 icon imported using `// @ts-ignore` for `.txt?raw` file
- **Refresh button**: Sends `MODAL_OUT_REFRESH` with loading animation states (2-second timeout)
- **Close button**: Sends `MODAL_OUT_CLOSE` with red hover styling
- **Responsive layout**: Flexbox design with proper spacing

##### Navigation Behavior
- **Smart initial step**: Uses `determineInitialStep()` function for state-based navigation
- **Auto-navigation**: `useEffect` triggers navigation on state changes
- **Manual navigation**: All steps accessible via `handleStepClick` regardless of completion
- **Visual feedback**: Step indicators show completion status but don't block access

### Step Components

#### Step Indicator Component

**File: `src/components/SetupWizard/StepIndicator.tsx`**
- Uses `SetupStep` enum throughout for type safety
- **Null state handling**: Shows loading state indicators when `setupState` is null
- **Dynamic status calculation**: Determines step status based on current setupState
- **Visual indicators**: Complete (green), error (red), current (blue), accessible (gray)
- **Gradient connectors**: Visual lines between completed steps
- **Click handlers**: Allow manual navigation to any step via `handleStepClick`
- **Loading pattern**: Early return with simplified indicators for null state

#### Step 1 - Platform Check (Simplified)

**File: `src/components/SetupWizard/Steps/PlatformCheck.tsx`**

**Completely State-Driven Architecture**:
- **No hardcoded values**: All platform names, statuses, and links from setupState
- **Platform detection display**: Shows detected browser/OS from `env` with react-icons
- **Enhanced Unknown Browser Display**: Shows actual browser names (e.g., "Brave - Coming Soon") instead of generic "Unknown Browser"
- **Support status indicators**: Derived from `browsers` and `os` arrays status fields
- **Browser-specific GitHub issue links**: Unknown browsers show targeted `github_issue_url` links (e.g., "brave-support")
- **Pure display component**: Read-only, no state modifications or actions

#### Step 2 - Extension Setup (Fixed State Management)

**File: `src/components/SetupWizard/Steps/ExtensionSetup.tsx`**

**Fixed State-Driven Architecture**:
- **Direct state usage**: Uses `extension.error.message` and `extension.error.code` from setupState
- **Reactive dropdown state**: Auto-syncs with `env.browser` changes via `useEffect` to prevent Safari/unknown browser display issues
- **Enhanced Browser Display**: Shows actual browser names in dropdown (e.g., "Brave - Coming Soon", "Safari")
- **Synchronized selection**: Dropdown initializes and updates with `env.browser` changes, preventing stale state
- **Extension store URLs**: All links from `browsers` array `extension_url` field
- **StatusBadge integration**: Visual status indicators with refresh capability
- **Browser-specific GitHub issue tracking**: Links for unknown browsers from dynamically generated `github_issue_url`

**Key Fix**: Added `useEffect(() => setSelectedBrowser(env.browser), [env.browser])` to sync dropdown with actual detected browser, fixing Safari display issue.

#### Step 3 - Server Setup (Fixed State Management)

**File: `src/components/SetupWizard/Steps/ServerSetup.tsx`**

**Fixed State-Driven Pattern**:
- **Direct state usage**: Uses `server.error.message` and `server.error.code` from setupState
- **Reactive OS dropdown**: Auto-syncs with `env.os` changes via `useEffect` matching ExtensionSetup pattern
- **Download URLs**: All links from `os` array `download_url` field
- **State-specific UI**: Different displays for `setup` vs `resource-admin` server states
- **StatusBadge integration**: Consistent visual status system with ExtensionSetup

**Key Fix**: Added same `useEffect(() => setSelectedOS(env.os), [env.os])` pattern as ExtensionSetup for state synchronization.

#### Step 4 - Success State (Complete Simplification)

**File: `src/components/SetupWizard/Steps/SuccessState.tsx`**

**Complete State-Driven Display**:
- **Platform names from state**: Browser/OS names from `browsers` and `os` arrays
- **Completion action**: "Continue" button sends `MODAL_OUT_COMPLETE`
- **Dynamic UI**: Different displays based on `allReady` calculation from setupState
- **Status summary**: Shows all component statuses (extension, server, platform)

### Utility Components

#### PlatformDropdown Component

**File: `src/components/common/PlatformDropdown.tsx`**

**Completely Configurable**:
- **No hardcoded options**: All dropdown options from `supportedOptions` prop
- **Enhanced Unknown Browser Support**: Displays actual browser names (e.g., "Brave", "Opera") instead of generic "Unknown Browser"
- **Generic fallbacks**: Unknown platforms use generic react-icons and names
- **Support indicators**: Shows "Coming Soon" for unsupported options
- **Icon system**: Uses react-icons with proper fallback handling

#### StatusBadge Component

**File: `src/components/SetupWizard/StatusBadge.tsx`**

**Standardized Status Display**:
- **5 status types**: success, error, warning, loading, neutral with consistent colors
- **Visual consistency**: Standardized icons and styling across all steps  
- **Refresh integration**: Built-in refresh functionality when applicable
- **Props interface**: `{ status, message, onRefresh?, showRefresh? }`
- **Conditional rendering**: Shows refresh button only when `showRefresh=true` and `onRefresh` provided

## Build Configuration

**File: `vite.config.ts`**
- **Single-file output**: Creates self-contained HTML with all assets inlined
- **Asset handling**: 100MB inline limit with base64 encoding for images/fonts
- **Compression**: Terser minification with console/debugger removal
- **Path aliases**: `@` resolves to `./src` for clean imports
- **Target compatibility**: ESNext for modern browser features

**File: `scripts/build-fast.mjs`**
- **Fast build optimization**: Hashes source files to skip unnecessary rebuilds
- **Cache management**: Stores build hashes for incremental builds
- **Development efficiency**: Reduces build time during development cycles

## Key Behavioral Patterns

### State Flow Dependencies
1. **All steps are accessible** regardless of completion status
2. **Smart initial navigation** based on current state requirements
3. **Visual feedback** indicates step completion but doesn't block access
4. **Refresh-driven updates**: Parent manages all state, modal is passive

### Error Handling Strategy
- **Typed error codes**: All error codes are typed constants with namespacing
- **State-driven messages**: Error messages come from setupState, not hardcoded
- **Required error fields**: ExtensionStateNotReady always has error field (including 'not-installed')
- **Context-specific troubleshooting**: Still provides guidance but from state data

### Communication Protocol
- **Minimal message types**: Only essential messages (ready, refresh, complete, close)
- **Typed constants**: All message types use exported constants
- **Passive modal philosophy**: Modal never modifies its own state
- **Parent-driven state**: All validation and updates handled by parent

### Hook-Based Architecture
- **useSetupModal hook**: State management extracted to `src/hooks/useSetupModal.ts`
- **App.tsx composition**: Reduced to 12 lines using pure composition pattern
- **Separation of concerns**: Hooks handle communication, components handle UI
- **Testable architecture**: Components can be tested in isolation with mock state

### Dynamic Configuration
- **Complete parent control**: Modal starts with empty arrays and receives all configuration
- **Enhanced Unknown Browser Injection**: Parent dynamically injects actual browser names (e.g., "Brave", "Opera") for unknown browsers
- **No hardcoded values**: All platform names, URLs, error messages from setupState
- **Read-only setupState**: Components never modify setupState values
- **Discriminated union types**: Platform support defined via `status` field
- **Browser-specific GitHub issue integration**: Unknown browsers get targeted GitHub issue URLs (e.g., "brave-support", "opera-support")

### Import Path Consistency
- **All imports use `@/` prefix**: Consistent import paths throughout codebase
- **Vite alias configuration**: `@` maps to `src/` directory
- **Clean import statements**: No relative imports (`./` or `../`)

### Testing Integration
- **Comprehensive test coverage**: All tests passing across test files using Vitest and React Testing Library
- **Hook isolation testing**: useSetupModal hook tested independently
- **Component testing**: Complete coverage of SetupWizard, ExtensionSetup, ServerSetup, PlatformCheck, and SuccessState
- **Test selectors**: Components include comprehensive `data-testid` attributes for automated testing
- **StepIndicator selectors**: Each step has `data-testid="step-{step-id}"` for navigation testing
- **Action button selectors**: Refresh and close buttons have `data-testid` for interaction testing
- **Component selectors**: All major UI elements have `data-testid` attributes for reliable test selection
- **Parameterized testing**: Extensive use of `test.each()` for testing multiple state combinations
- **Mock factories**: Reusable test data generators in `src/test/mock-factories.ts`
- **Black box testing**: Modal designed to be testable through external interface only

The architecture provides a robust, completely configurable setup experience with clean separation of concerns and minimal hardcoding. The modal is designed as a "dumb component" that purely displays state and communicates actions, making it highly testable and predictable.