# Domain: Bodhi Browser Extension

## Problem Domain

Web browsers implement strict security policies that prevent web pages from accessing locally running services. This security measure protects users from malicious websites but creates limitations when legitimate web applications need to interact with local services, such as locally-running Large Language Models (LLMs).

The Bodhi Browser extension addresses this limitation by creating a secure bridge between web pages and locally running Bodhi App servers, enabling users to leverage local computational resources while browsing the web without compromising browser security.

## System Components

### Bodhi App (Local LLM Server)

**Bodhi App** is the locally running LLM service that:

- Provides OpenAI-compatible REST APIs for LLM capabilities
- Manages local LLM models and performs inference
- Implements authentication and security measures
- Supports both request/response and streaming interfaces
- Runs on configurable ports (default: `localhost:1135`)

### bodhi-browser-ext (Chrome Extension)

**bodhi-browser-ext** serves as the bridge component that:

- Acts as intermediary between web pages and local Bodhi App servers
- Injects `window.bodhiext` API into web pages for programmatic access
- Proxies API calls using a unified message format
- Supports both standard and streaming responses
- Provides configuration UI for backend server settings
- Works across Chromium-based browsers (Chrome, Edge, Brave)

### bodhijs (Web Integration Library)

**bodhijs** is the NPM package (`@bodhiapp/bodhijs`) that:

- Provides a clean, consistent API for web developers
- Handles extension detection and availability checking
- Abstracts message passing complexity with the extension
- Offers structured error handling with specific error codes
- Uses TypeScript types from `@bodhiapp/ts-client` for compatibility
- Currently supports non-streaming requests (streaming planned)

## Component Interactions

```
Web Page ←→ bodhijs ←→ bodhi-browser-ext ←→ Bodhi App
         (NPM pkg)     (window.bodhiext)      (HTTP/SSE)
```

### Communication Flow

1. **Web Application**: Includes bodhijs library and uses its API
2. **bodhijs Library**: Detects extension and forwards requests to `window.bodhiext`
3. **Extension**: Receives requests via message passing and makes HTTP calls to Bodhi App
4. **Bodhi App**: Processes LLM requests and returns responses (standard or streaming)
5. **Response Path**: Follows reverse path back to web application

## Use Cases

### Primary Use Cases

1. **Local AI Integration**: Web applications can integrate LLM capabilities without sending data to remote servers
2. **Privacy-Preserving AI**: Users maintain full control over their data and AI interactions
3. **Enhanced Web Experiences**: Websites can offer AI-powered features using user's local models
4. **Development and Testing**: Developers can build and test AI features without external dependencies

### Technical Use Cases

1. **Chat Completions**: OpenAI-compatible chat completion API with streaming support
2. **Content Analysis**: Send web page content to local LLM for analysis or summarization
3. **Extension-to-Extension**: Other Chrome extensions can communicate with Bodhi extension
4. **Configuration Management**: Users can configure backend server URLs and settings

## Technical Architecture

### API Communication

- **Standard Requests**: Request/response pattern for single-shot API calls
- **Streaming Requests**: Server-Sent Events (SSE) for incremental responses
- **Message Format**: Unified format with `type`, `requestId`, and `request`/`response` objects
- **Error Handling**: Structured error responses with HTTP status codes

### Security Model

- **Browser Security**: Extension maintains browser security by acting as controlled proxy
- **Origin Validation**: Content script validates message origins to prevent abuse
- **Permission Model**: Minimal Chrome permissions with configurable host access
- **Interface Protection**: `window.bodhiext` API is frozen and non-configurable

### Configuration

- **Backend URL**: Configurable via extension popup (default: `http://localhost:1135`)
- **Persistent Storage**: Settings stored in Chrome storage and applied immediately
- **Validation**: URL format validation with user feedback

## Deployment Model

### User Requirements

Users need:

1. **Chrome Extension**: Install bodhi-browser-ext from Chrome Web Store or load unpacked
2. **Bodhi App**: Run local Bodhi App server with LLM models
3. **Network Access**: Extension needs permission to connect to local server

### Developer Integration

Web developers need:

1. **NPM Package**: Install `@bodhiapp/bodhijs` via npm
2. **Extension Detection**: Check if extension is installed before using API
3. **Error Handling**: Handle cases where extension or server is unavailable

### Integration Examples

#### NPM Package Usage

```javascript
import { isInstalled, ping, chat } from '@bodhiapp/bodhijs';

// Check extension availability
if (isInstalled()) {
  // Verify server connectivity
  const response = await ping();
  
  // Make chat completion request
  const result = await chat.completions.create({
    model: 'llama3',
    messages: [{ role: 'user', content: 'Hello!' }]
  });
}
```

#### Direct Extension API Usage

```javascript
// Check if extension is available
if (window.bodhiext) {
  // Use extension API directly
  const response = await window.bodhiext.ping();
  
  // Streaming chat completion
  const stream = await window.bodhiext.chat.completions.create({
    model: 'llama3',
    messages: [{ role: 'user', content: 'Write a story' }],
    stream: true
  });
  
  for await (const chunk of stream) {
    console.log(chunk);
  }
}
```

## Testing Infrastructure

### Test Architecture

- **Framework**: Vitest with Playwright for browser automation
- **Real Browser Testing**: Tests run with actual Chrome/Chromium and loaded extension
- **Mock LLM Server**: `@bodhiapp/mock-llm-server` provides OpenAI-compatible endpoints
- **Test Categories**: Web-to-extension, extension-to-extension, settings, real server integration

### Mock LLM Server

- **Purpose**: Provides controlled testing environment without external dependencies
- **Endpoints**: `/ping`, `/v1/chat/completions` with streaming support
- **Features**: CORS enabled, configurable responses, automatic lifecycle management

### Test Patterns

- **Extension Loading**: Tests verify extension loads correctly and injects API
- **API Functionality**: Tests verify all API methods work as expected
- **Streaming**: Tests verify streaming responses work with AsyncIterator pattern
- **Error Handling**: Tests verify proper error responses and handling
- **Configuration**: Tests verify settings UI and backend URL configuration

## Current Limitations

### bodhijs Library Limitations

1. **No Streaming Support**: Library currently doesn't support streaming (extension does)
2. **Extension Dependency**: Requires extension to be installed and running
3. **Browser Only**: Designed for browser environments, not Node.js

### Extension Limitations

1. **Browser Support**: Currently Chrome/Chromium only (Manifest V3)
2. **Local Server Dependency**: Requires local Bodhi App to be running
3. **Single Backend**: Only supports one backend URL at a time

### General Limitations

1. **Network Connectivity**: Requires network access to local server
2. **CORS**: Local server must support CORS for cross-origin requests
3. **Authentication**: Currently relies on Bodhi App's authentication mechanisms

## Target Users

### Primary Users

1. **Web Developers**: Integrate local LLM capabilities into web applications
2. **End Users**: Enhance web browsing with local AI capabilities
3. **Extension Developers**: Build extensions that communicate with Bodhi extension

### Use Case Examples

- **Content Creators**: Websites offering AI writing assistance using local models
- **Educational Platforms**: Learning applications with local AI tutoring
- **Development Tools**: Code editors with local AI code completion
- **Research Applications**: Data analysis tools with local AI processing

This architecture enables a growing ecosystem of web applications that can leverage local AI capabilities while maintaining user privacy and data control.
