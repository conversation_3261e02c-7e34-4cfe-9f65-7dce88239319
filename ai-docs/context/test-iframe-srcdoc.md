# Bodhi Platform Setup Wizard Modal - Test Application Documentation

## Overview

The test application is a React-based testing environment designed to embed and test the Bodhi Platform Setup Wizard Modal. It provides comprehensive state simulation, message logging, and testing utilities for validating modal functionality in isolation.

## Application Architecture

### Entry Point & Initialization

**File: `test-iframe-srcdoc/src/main.tsx`**
- Renders the main test `App` component using React 18's `createRoot` API
- Uses `@/App` import path alias for consistency with modal codebase
- Mounts to DOM element with id "root"

**File: `test-iframe-srcdoc/src/styles.css`**
- Comprehensive CSS styling for test application interface
- Control panel styling with grid layouts and visual status indicators
- Message log styling with monospace font and dark theme
- Modal container styling with responsive design
- URL testing section styling for link validation

### Vite Configuration

**File: `test-iframe-srcdoc/vite.config.ts`**
- Configured with `@` path alias pointing to `./src` directory
- JSX support with React plugin configuration
- Build process copies modal HTML for iframe embedding
- Preview server configuration for testing

### Type System Integration

**File: `test-iframe-srcdoc/src/types.ts`**
- Re-exports all types from main modal codebase
- Ensures type consistency between modal and test app
- Provides centralized type definitions for test environment

### Component Hierarchy

#### Root Component - App

**File: `test-iframe-srcdoc/src/App.tsx`**

**State Management**:
- **Modal lifecycle state**: `showModal` boolean for modal visibility
- **Setup state**: Complete `SetupState` from factory function
- **Message logging**: Array of timestamped message strings
- **State synchronization**: Real-time updates between simulation controls and modal

**Message Handling**:
```typescript
const handleModalMessage = (message: any) => {
  const action = message.type.replace('modal_out:', '');
  logMessage('received', `Action from modal: ${action}`, message);

  switch (message.type) {
    case MODAL_OUT_READY:
      logMessage('info', '🚀 Modal is ready - initial state will be sent automatically');
      break;
    case MODAL_OUT_REFRESH:
      logMessage('info', '🔄 Modal requested refresh - sending current simulation state');
      break;
    case MODAL_OUT_COMPLETE:
      logMessage('info', '✅ Setup completed successfully! Modal will close automatically.');
      break;
    case MODAL_OUT_CLOSE:
      logMessage('info', '❌ Modal closed by user');
      break;
  }
};
```

**Modal Lifecycle Management**:
- **Launch**: Creates new modal instance with current state
- **Close**: Removes modal from DOM and updates UI
- **Auto-close**: Handles automatic closing on complete/close messages
- **Reopen**: Supports multiple modal sessions with message history preservation

#### ModalContainer Component

**File: `test-iframe-srcdoc/src/components/ModalContainer.tsx`**

**Iframe Management**:
- **srcDoc embedding**: Uses built modal HTML directly as iframe content
- **Message forwarding**: Handles bidirectional postMessage communication
- **State synchronization**: Sends state updates to modal on changes
- **Auto-close handling**: Responds to complete/close messages from modal

**Implementation**:
```typescript
const sendStateToModal = () => {
  if (iframeRef.current?.contentWindow) {
    iframeRef.current.contentWindow.postMessage({
      type: MODAL_MESSAGE_STATE,
      data: setupState
    }, '*');
  }
};
```

**Modal Header**:
- **Test context UI**: Clear identification as "Bodhi Modal Test"
- **Close button**: Manual modal closing capability
- **Responsive design**: Proper spacing and accessibility

#### SimulationPanel Component

**File: `test-iframe-srcdoc/src/components/SimulationPanel.tsx`**

**State Simulation Features**:
- **Quick actions**: Pre-configured state scenarios (successful setup, reset to initial)
- **Launch control**: Modal launching with current state
- **State preview**: Real-time display of current setupState values
- **URL testing**: Links to all configured URLs for manual validation

**URL Testing Integration**:
```typescript
// Extension URLs (Supported Browsers)
{state.browsers.filter(b => b.status === 'supported').map(browser => (
  <li key={browser.id}>
    <strong>{browser.name}:</strong>{' '}
    <a href={browser.extension_url} target="_blank" rel="noopener noreferrer">
      {browser.extension_url}
    </a>
  </li>
))}

// Server Download URLs (Supported OS)
{state.os.filter(o => o.status === 'supported').map(os => (
  <li key={os.id}>
    <strong>{os.name}:</strong>{' '}
    <a href={os.download_url} target="_blank" rel="noopener noreferrer">
      {os.download_url}
    </a>
  </li>
))}
```

**Quick Actions**:
- **Simulate Successful Setup**: Sets extension and server to 'ready' state
- **Reset to Initial State**: Returns to default not-installed/pending state
- **Launch Modal**: Creates new modal instance with current state

#### ControlPanel Component

**File: `test-iframe-srcdoc/src/components/ControlPanel.tsx`**

**Detailed State Controls**:
- **Browser selection**: Dropdown with all supported browser types
- **OS selection**: Dropdown with all supported operating systems
- **Extension status**: Dropdown with all extension state options
- **Server status**: Dropdown with all server state options

**Test Selectors**:
- **data-testid attributes**: `browser-select`, `os-select`, `extension-status`, `server-status`
- **Form integration**: Real-time state updates with visual sync indicators
- **State validation**: Proper state object construction with typed error codes

**Error Code Integration**:
```typescript
switch (value) {
  case 'unreachable':
    errorCode = EXT_CONNECTION_FAILED;
    errorMessage = 'Could not connect to extension';
    break;
  case 'unsupported':
    errorCode = EXT_UNSUPPORTED_VERSION;
    errorMessage = 'Extension version is unsupported';
    break;
}
```

#### MessageLog Component

**File: `test-iframe-srcdoc/src/components/MessageLog.tsx`**

**Communication Logging**:
- **Message history**: Chronological log of all modal communications
- **Message types**: Sent, received, and info messages with timestamps
- **JSON formatting**: Structured display of message data
- **Console integration**: Dual logging to both UI and browser console

**Message Format**:
```typescript
const timestamp = new Date().toLocaleTimeString();
const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}${data ? '\n' + JSON.stringify(data, null, 2) : ''}\n\n`;
```

### State Management System

#### Complete State Factory

**File: `test-iframe-srcdoc/src/utils.ts`**

**State Generation**:
- **Comprehensive browser support**: Chrome, Edge (supported), Firefox, Safari (not supported)
- **OS platform support**: macOS, Windows (supported), Linux (not supported)
- **Realistic URLs**: Actual extension store URLs and GitHub download links
- **Error code integration**: Proper error objects with typed constants

**Sample State Structure**:
```typescript
export const createCompleteSetupState = (): SetupState => ({
  extension: {
    status: 'not-installed',
    error: {
      message: 'Extension is not installed/or not detected',
      code: EXT_NOT_INSTALLED
    }
  },
  server: {
    status: 'pending-extension-ready',
    error: {
      message: 'Server waiting for extension',
      code: SERVER_PENDING_EXT_READY
    }
  },
  env: { browser: 'chrome', os: 'macos' },
  browsers: [
    {
      id: 'chrome',
      status: 'supported',
      name: 'Google Chrome',
      extension_url: 'https://chrome.google.com/webstore/detail/bodhi-extension/placeholder-extension-id'
    },
    {
      id: 'firefox', 
      status: 'not-supported',
      name: 'Firefox',
      github_issue_url: 'https://github.com/bodhiapps/extension/issues/firefox-support'
    }
    // ... additional browsers
  ],
  os: [
    {
      id: 'macos',
      status: 'supported',
      name: 'macOS', 
      download_url: 'https://github.com/bodhiapps/server/releases/download/latest/bodhi-server-macos.dmg'
    }
    // ... additional operating systems
  ]
});
```

### Build Process

**File: `test-iframe-srcdoc/package.json`**

**Build Scripts**:
- **copy-modal**: Copies built modal HTML from main project
- **build**: Runs modal copy then Vite build
- **preview**: Starts preview server for built application
- **dev**: Development server with hot reloading

**Modal Integration Process**:
1. Main modal project builds to `dist/index.html`
2. Test app copies modal HTML to `src/modal.html`
3. ModalContainer imports modal HTML as raw string
4. Modal embedded in iframe using `srcDoc` attribute

### Testing Integration

**File: `test-iframe-srcdoc/tests/setup.ts`**

**Test Environment Setup**:
- **Server management**: Automated server startup with random ports
- **Build automation**: Runs both main modal and test app builds
- **Global configuration**: Provides test URL to all test files
- **Resource cleanup**: Proper server shutdown and cleanup

**File: `test-iframe-srcdoc/vitest.config.ts`**

**Test Configuration**:
- **Environment**: Node.js environment for server management
- **Timeout configuration**: 60 second test timeout, 30 second hook timeout
- **Setup files**: Automatic server startup via setup.ts
- **Parallel execution**: Disabled for reliable server management

## Key Features

### Modal Testing Capabilities
- **Complete state simulation**: All possible state combinations
- **Real URL testing**: Functional links to actual services
- **Message protocol validation**: Full postMessage communication testing
- **Responsive design testing**: Multiple viewport size support
- **Lifecycle testing**: Modal open/close/reopen scenarios

### Development Utilities
- **Visual state debugging**: Real-time state preview with debug information
- **Message logging**: Complete communication history with timestamps
- **Quick state scenarios**: One-click state changes for common test cases
- **URL validation**: Clickable links for manual testing of all configured URLs

### Integration Testing Support
- **Black box testing**: Tests only externally visible behavior
- **Browser automation**: Playwright integration for real browser testing
- **Deterministic testing**: Reliable test patterns without conditional logic
- **Cross-platform testing**: Support for different browser and OS combinations

The test application provides a comprehensive environment for validating modal functionality, enabling thorough testing of all modal features while maintaining separation of concerns between the modal implementation and its testing infrastructure.