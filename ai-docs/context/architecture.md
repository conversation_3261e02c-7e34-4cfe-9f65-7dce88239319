# Architecture: Bodhi Browser System

This document outlines the technical architecture of the Bodhi Browser system, which enables web pages to interact with locally running LLM services through a Chrome extension bridge.

## System Overview

The Bodhi Browser system consists of three main components:

1. **bodhi-browser-ext**: Chrome extension that acts as a bridge between web pages and local LLM servers
2. **bodhijs Library**: JavaScript/TypeScript library for web applications to interact with the extension
3. **Bodhi App**: Locally running LLM server (external dependency)

```
┌───────────────┐     ┌───────────────────┐     ┌───────────────────┐     ┌───────────────┐
│   Web Page    │◄───►│    bodhijs        │◄───►│  bodhi-browser    │◄───►│   Bodhi App   │
│               │     │    Library        │     │    -ext           │     │  (LLM Server) │
│               │     │                   │     │                   │     │               │
└───────────────┘     └───────────────────┘     └───────────────────┘     └───────────────┘
      Browser               NPM Package            Chrome Extension         Local Server
```

## bodhi-browser-ext Architecture

### Component Structure

The extension consists of four main components:

```
bodhi-browser-ext/
├── src-ext/                    # Extension scripts
│   ├── background.ts           # Service worker (background script)
│   ├── content.ts              # Content script (injected into pages)
│   ├── inject.ts               # Page script (creates window.bodhiext)
│   └── shared/                 # Shared utilities and types
├── src/                        # Next.js UI components
│   ├── components/Settings/    # Settings component
│   └── pages/                  # Next.js pages
└── public/                     # Static assets and manifest
```

### Message Flow Architecture

The extension uses a unified message format with three-layer communication:

1. **Web Page ↔ Inject Script**: `window.postMessage` with `window.bodhiext` API
2. **Inject Script ↔ Content Script**: `window.postMessage` with message forwarding
3. **Content Script ↔ Background Script**: Chrome runtime messaging

#### Message Format

All messages follow a unified structure:

```typescript
// Request messages
{
  type: 'BODHI_API_REQUEST' | 'BODHI_STREAM_REQUEST',
  requestId: string,
  request: {
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  }
}

// Response messages
{
  type: 'BODHI_API_RESPONSE' | 'BODHI_STREAM_CHUNK' | 'BODHI_STREAM_ERROR',
  requestId: string,
  response: {
    body: any,
    status: number,
    headers: Record<string, string>
  }
}
```

### Background Script (Service Worker)

- **Purpose**: Handles HTTP requests to local Bodhi App server
- **Default Backend**: `http://localhost:1135` (configurable via UI)
- **Communication Patterns**:
  - One-time messages: `chrome.runtime.onMessage` for standard API calls
  - Long-lived connections: `chrome.runtime.onConnect` for streaming
  - External extensions: `chrome.runtime.onMessageExternal` and `chrome.runtime.onConnectExternal`
- **Streaming Implementation**: Uses `ReadableStream` and `TextDecoder` for SSE parsing
- **Error Handling**: Centralized error handling with structured error responses

### Content Script

- **Purpose**: Bridge between web page and extension background
- **Injection**: Automatically injects `inject.js` into page DOM
- **Message Routing**: Forwards messages bidirectionally without modification
- **Stream Management**: Maintains active stream connections with cleanup

### Inject Script

- **Purpose**: Creates `window.bodhiext` API for web applications
- **API Methods**:
  - `ping()`: Connectivity check
  - `chat.completions.create()`: Chat completions with streaming support
  - `sendApiRequest()`: Generic API method
  - `sendStreamRequest()`: Generic streaming method
  - `getExtensionId()`: Returns extension ID
- **Streaming**: Implements AsyncIterator pattern with ReadableStream
- **Security**: Object is frozen and non-configurable

### Next.js UI

- **Framework**: Next.js with static export for Chrome extension compatibility
- **Components**: Settings component for backend URL configuration
- **Build Process**: Static export to `dist/` directory
- **Styling**: TailwindCSS for UI components

## Communication Patterns

### Standard API Flow

```
Web Page → window.bodhiext.ping()
  ↓ window.postMessage
Inject Script → creates request with unique ID
  ↓ window.postMessage  
Content Script → forwards request
  ↓ chrome.runtime.sendMessage
Background Script → makes HTTP request to Bodhi App
  ↓ fetch response
Background Script → sends response back
  ↓ sendResponse
Content Script → forwards response
  ↓ window.postMessage
Inject Script → resolves Promise
  ↓ return
Web Page ← receives response
```

### Streaming Flow

```
Web Page → window.bodhiext.chat.completions.create({stream: true})
  ↓ returns AsyncIterator
Inject Script → creates ReadableStream
  ↓ window.postMessage (STREAM_REQUEST)
Content Script → establishes long-lived connection
  ↓ chrome.runtime.connect
Background Script → makes streaming HTTP request
  ↓ processes SSE chunks
Background Script → sends chunks via port.postMessage
  ↓ STREAM_CHUNK messages
Content Script → forwards chunks
  ↓ window.postMessage
Inject Script → enqueues chunks to ReadableStream
  ↓ AsyncIterator yields chunks
Web Page ← processes chunks in for-await loop
```

### Extension-to-Extension Communication

The extension supports communication from other Chrome extensions:

- **Discovery**: Extensions can find Bodhi extension ID via manifest
- **Standard API**: `chrome.runtime.sendMessage(extensionId, message)`
- **Streaming**: `chrome.runtime.connect(extensionId, {name: 'BODHI_CHANNEL'})`
- **Message Format**: Same unified format as web page communication

## Configuration Architecture

### Backend URL Configuration

- **Storage**: Chrome storage API (`chrome.storage.local`)
- **UI**: Settings component accessible via extension popup
- **Validation**: URL format validation with real-time feedback
- **Default**: `http://localhost:1135`
- **Updates**: Changes applied immediately to all requests

### Build Architecture

#### Extension Scripts Build

- **Tool**: Webpack with TypeScript
- **Entry Points**: `background.ts`, `content.ts`, `inject.ts`
- **Output**: Individual `.js` files in `dist/`
- **Features**: Source maps, code obfuscation (production)

#### UI Build

- **Tool**: Next.js with static export
- **Output**: Static HTML/CSS/JS in `dist/`
- **Features**: TailwindCSS processing, code obfuscation (production)

## Testing Architecture

### Test Infrastructure

- **Framework**: Vitest with Playwright for browser automation
- **Browser**: Real Chrome/Chromium with extension loaded
- **Servers**: Mock LLM server and static file server
- **Isolation**: Fresh browser context per test suite

### Test Categories

1. **Web-to-Extension**: Tests `window.bodhiext` API functionality
2. **Extension-to-Extension**: Tests Chrome extension messaging
3. **Settings**: Tests configuration UI
4. **Real Server Integration**: Tests with actual Bodhi servers

### Mock LLM Server

- **Purpose**: Provides OpenAI-compatible endpoints for testing
- **Endpoints**: `/ping`, `/v1/chat/completions`
- **Features**: Streaming support, CORS enabled
- **Usage**: Automatically started/stopped in test suites

## Security Architecture

### Content Security Policy

- **Extension Pages**: `script-src 'self'; object-src 'self'; connect-src *`
- **External Connectivity**: Allows connections to any URL for LLM server flexibility

### Message Validation

- **Origin Verification**: Content script validates message origins
- **Request ID Correlation**: Unique IDs prevent message confusion
- **Interface Protection**: `window.bodhiext` is frozen and non-configurable

### Permissions

- **Minimal Permissions**: Only `storage` permission requested
- **Host Permissions**: `<all_urls>` for LLM server connectivity
- **External Connectivity**: Allows other extensions to connect

## Error Handling Architecture

### Structured Error Responses

All errors follow a consistent format:

```typescript
{
  type: 'BODHI_API_RESPONSE' | 'BODHI_STREAM_ERROR',
  requestId: string,
  response: {
    body: {
      error: {
        message: string
      }
    },
    status: number,
    headers: {}
  }
}
```

### Error Categories

1. **Network Errors**: Connection failures to Bodhi App
2. **Validation Errors**: Malformed requests or responses
3. **Timeout Errors**: Request/stream timeouts (30s/60s respectively)
4. **Extension Errors**: Extension not installed or unavailable

### Logging

- **Development**: Detailed console logging with context
- **Production**: Error logging only
- **Format**: `[Bodhi/{component}] {message}` with structured data

## Performance Architecture

### Request Optimization

- **Connection Reuse**: HTTP keep-alive for multiple requests
- **Timeout Management**: 30-second timeout for API requests, 60-second for streaming
- **Resource Cleanup**: Automatic cleanup of expired requests and streams

### Streaming Optimization

- **Chunk Processing**: Efficient SSE parsing with minimal overhead
- **Buffer Management**: Streaming buffer management for large responses
- **Memory Management**: Automatic cleanup of completed streams

### Build Optimization

- **Code Splitting**: Disabled for extension compatibility
- **Tree Shaking**: Webpack tree shaking for smaller bundles
- **Obfuscation**: Production code obfuscation for security
