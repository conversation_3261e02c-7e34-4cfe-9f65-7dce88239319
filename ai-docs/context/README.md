# Bodhi Browser Context Documentation

This directory contains context documentation for the Bodhi Browser project, designed to provide current implementation details for AI coding assistants and developers. These documents focus on project-specific architecture, implementation patterns, and current state rather than general framework concepts.

## Architecture and Domain

### [architecture.md](./architecture.md)
Technical architecture of the Bodhi Browser system, including component structure, message flow, communication patterns, and build processes. Covers the complete architecture of the Chrome extension, testing infrastructure, and performance considerations.

### [domain.md](./domain.md)
Problem domain, system components, and use cases. Explains how the browser security model creates the need for this extension and how the components work together to solve the problem.

## Component Implementation

### [bodhi-browser-ext.md](./bodhi-browser-ext.md)
Detailed implementation documentation for the `bodhi-browser-ext` Chrome extension, including API methods, message formats, streaming implementation, configuration, and testing patterns.

### [bodhi-js.md](./bodhi-js.md)
Implementation documentation for the `bodhijs` NPM library, including API structure, extension detection, error handling, and current limitations.

## Technical Specifications

### [extension-message-format.md](./extension-message-format.md)
Comprehensive specification for the unified message format used throughout the extension for communication between inject script, content script, and background script.

## Context Usage Guidelines

### For AI Coding Assistants
These documents provide project-specific context to help AI systems understand:
- Current implementation state and patterns
- Component relationships and communication flows
- Testing approaches and infrastructure
- Build processes and deployment considerations

### For Developers
These documents serve as:
- Implementation reference for current codebase
- Architecture documentation for understanding system design
- API documentation for integration patterns
- Testing guidelines for maintaining quality

## Document Maintenance

These documents reflect the current state of the codebase and are updated to match actual implementation. They focus on:
- **Current State Only**: No planned features or deprecated functionality
- **Project-Specific Details**: Implementation patterns unique to this project
- **Source Code Truth**: Documentation verified against actual source code
- **AI Assistant Context**: Information needed for effective code assistance

## Quick Reference

| Component | Implementation Doc | Key Features |
|-----------|-------------------|--------------|
| Chrome Extension | [bodhi-browser-ext.md](./bodhi-browser-ext.md) | Message passing, streaming, configuration UI |
| NPM Library | [bodhi-js.md](./bodhi-js.md) | Extension detection, API abstraction, error handling |
| Message Format | [extension-message-format.md](./extension-message-format.md) | Unified request/response format |
| System Architecture | [architecture.md](./architecture.md) | Component structure, communication flows |
| Problem Domain | [domain.md](./domain.md) | Use cases, deployment model, limitations | 