# Bodhi Browser Extension Messaging Format

## Overview

This document provides a comprehensive specification for the request and response messaging format used in the Bodhi browser extension. It covers all communication between the inject script, content script, and background script, including both standard and streaming API calls. The format described here is the result of several design decisions and refinements, and is now the only supported format in the extension. All legacy formats are deprecated and unsupported.

---

## Table of Contents

1. [Message Structure](#message-structure)
2. [Request Format](#request-format)
3. [Response Format](#response-format)
4. [Streaming Responses](#streaming-responses)
5. [Component Responsibilities](#component-responsibilities)
    - [inject.js](#injectjs)
    - [content.js](#contentjs)
    - [background.js](#backgroundjs)
6. [Message Routing and Meta Information](#message-routing-and-meta-information)
7. [Examples](#examples)
8. [Error Handling](#error-handling)
9. [Versioning and Compatibility](#versioning-and-compatibility)

---

## Message Structure

Every message exchanged between the extension components follows a unified structure, with clear separation of meta information (such as `type` and `requestId`) and the actual payload (contained in a `request` or `response` object).

### Meta Fields
- `type` (string): The message type (e.g., `BODHI_API_REQUEST`, `BODHI_API_RESPONSE`, `BODHI_STREAM_REQUEST`, `BODHI_STREAM_CHUNK`, `BODHI_STREAM_ERROR`).
- `requestId` (string): A unique identifier for each request/response pair. Used for correlating responses to requests.

### Payload Fields
- `request` (object): Present in all request messages. Contains the API call details.
- `response` (object): Present in all response messages. Contains the result, status, and error info.

---

## Request Format

All API and streaming requests use the following format:

```json
{
  "type": "BODHI_API_REQUEST" | "BODHI_STREAM_REQUEST",
  "requestId": "<unique-id>",
  "request": {
    "method": "GET" | "POST" | ...,
    "endpoint": "/v1/chat/completions",
    "body": { ... },
    "headers": { ... }
  }
}
```

- `type`: Indicates if this is a regular API request or a streaming request.
- `requestId`: Generated by the inject script for each outgoing request.
- `request`: Contains all the details needed for the HTTP call.

---

## Response Format

All API and streaming responses use the following unified format:

```json
{
  "type": "BODHI_API_RESPONSE" | "BODHI_STREAM_CHUNK" | "BODHI_STREAM_ERROR",
  "requestId": "<unique-id>",
  "response": {
    "body": { ... } | null,         // The response payload or streamed chunk (or error body)
    "status": 200,                  // HTTP status code (or 0 for network errors)
    "headers": { ... }              // HTTP response headers as key-value pairs
  }
}
```

- `type`: Indicates the type of response (regular, streaming chunk, or streaming error).
- `requestId`: Matches the original request.
- `response`: Contains the result, status, and error information.

---

## Streaming Responses

For streaming API calls (e.g., OpenAI chat completions with streaming), the following message types are used:

- `BODHI_STREAM_CHUNK`: Sent for each chunk of data received from the backend.
- `BODHI_STREAM_ERROR`: Sent if an error occurs during streaming.

Each streaming message follows the unified response format, with the chunk or error in the `response` object.

### Example Streaming Chunk
```json
{
  "type": "BODHI_STREAM_CHUNK",
  "requestId": "1234-5678",
  "response": {
    "body": { "choices": [ ... ] },
    "status": 200,
    "headers": { ... }
  }
}
```

### Example Streaming Error
```json
{
  "type": "BODHI_STREAM_ERROR",
  "requestId": "1234-5678",
  "response": {
    "body": { "error" : "..." },
    "status": 422,
    "headers": { ... }
  }
}
```

---

## Component Responsibilities

### inject.ts
- **Generates** unique `requestId` for each outgoing request.
- **Sends** requests to the content script using the unified request format.
- **Receives** responses (including streaming) from the content script in the unified response format.
- **Matches** responses to requests using `requestId` and resolves/rejects Promises or streams accordingly.
- Injected into the page DOM by the content script.
- Exposes the `window.bodhiext` API for web apps to interact with the extension.
- Forwards requests and responses via `window.postMessage`.

### content.ts
- **Acts as a bridge** between the web page (inject.ts) and the extension background script.
- **Forwards** requests from inject.ts to background.ts, preserving `type`, `requestId`, and `request`.
- **Forwards** responses from background.ts back to inject.ts, preserving `type`, `requestId`, and `response`.
- **Does not alter** the payload or meta fields.
- Injected as a content script into every web page.
- Listens for messages from the injected script (via `window.postMessage`).
- Relays messages between the page and the extension background script using `chrome.runtime.sendMessage` and `chrome.runtime.onMessage`.
- Handles streaming and standard message routing.

### background.ts
- **Receives** requests from content.ts and performs the actual HTTP call to the backend.
- **Generates** all responses (including streaming) in the unified format.
- **For streaming**, emits a sequence of `BODHI_STREAM_CHUNK` messages, followed by a final chunk (with `done: true` in `data`) or a `BODHI_STREAM_ERROR` if an error occurs.
- Runs as the extension's background service worker.
- Receives API and streaming requests from content scripts.
- Handles HTTP requests to the Bodhi App server, manages streaming, and responds to the content script.
- Implements error handling, chunking for streaming, and connection management.

---

## Message Routing and Meta Information

- **requestId**: Every request/response pair is matched using this field. The inject script generates it; all components must forward it unchanged.
- **type**: Used to determine the message handler logic in each component:
    - `BODHI_API_REQUEST` / `BODHI_STREAM_REQUEST`: Triggers an outgoing API or streaming HTTP call.
    - `BODHI_API_RESPONSE`: Indicates a completed API call (success or error).
    - `BODHI_STREAM_CHUNK`: Indicates a chunk of streaming data.
    - `BODHI_STREAM_ERROR`: Indicates a streaming error.

---

## Examples

### 1. API Request and Response

**Request:**
```json
{
  "type": "BODHI_API_REQUEST",
  "requestId": "abcd-efgh",
  "request": {
    "method": "POST",
    "endpoint": "/v1/chat/completions",
    "body": { "messages": [ ... ] },
    "headers": { "Authorization": "Bearer ..." }
  }
}
```

**Response:**
```json
{
  "type": "BODHI_API_RESPONSE",
  "requestId": "abcd-efgh",
  "response": {
    "body": { "id": "cmpl-123", ... },
    "status": 200,
    "headers": {...}
  }
}
```

### 2. Streaming Request and Chunks

**Request:**
```json
{
  "type": "BODHI_STREAM_REQUEST",
  "requestId": "xyz-7890",
  "request": {
    "method": "POST",
    "endpoint": "/v1/chat/completions",
    "body": { ... },
    "headers": { ... }
  }
}
```

**Streaming Chunk Response:**
```json
{
  "type": "BODHI_STREAM_CHUNK",
  "requestId": "xyz-7890",
  "response": {
    "body": { "choices": [ ... ] },
    "status": 200,
    "headers": {...}
  }
}
```

**Streaming Error Response:**
```json
{
  "type": "BODHI_STREAM_ERROR",
  "requestId": "xyz-7890",
  "response": {
    "body": { "error": "..." },
    "status": 422,
    "headers": {...}
  }
}
```

---

## Error Handling

- All errors, including network and backend errors, are returned in the `response` object, with http error status as returned from backend, and the body containing the error message was received from backend.
- Streaming errors are sent as `BODHI_STREAM_ERROR` messages with the same unified response structure.
- No legacy or alternate error formats are supported.

---

## Versioning and Compatibility

- **Only the unified format described here is supported.**
- All components (inject.js, content.js, background.js) must send and receive messages in this format.
- Legacy formats are not supported and will result in errors or ignored messages.

---

## Streaming Responses

- When a stream is complete, the response body will be `{ done: true }`.
- Downstream consumers should check for this (`body.done === true`) to know when to close the stream and perform any necessary cleanup.

---

## Summary

This unified messaging format ensures:
- Consistent and predictable communication between all extension components.
- Easy correlation of requests and responses using `requestId`.
- Clear separation of meta information and payload.
- Robust support for both regular and streaming API calls.

For implementation details, refer to the source code of `inject.js`, `content.js`, and `background.js` in the extension codebase.
