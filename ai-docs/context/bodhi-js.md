# Bodhi JS Library - Current Implementation State

The `@bodhiapp/bodhijs` library (v0.0.5-dev) is a TypeScript library that provides a simplified interface for web applications to interact with the Bodhi Browser Extension. The library implements a platform-centric design pattern with comprehensive onboarding support and uses UAParser.js for browser/OS detection.

## Project Structure

### Directory Organization

```
bodhi-js/
├── src/
│   ├── index.ts            # Library entry point (13 exports)
│   ├── core.ts             # BodhiPlatform and BodhiExtClient classes
│   ├── types.ts            # Core API and state types
│   ├── global.d.ts         # Global type declarations
│   └── onboarding/         # Onboarding system implementation
│       ├── index.ts        # Internal exports for onboarding
│       ├── modal.ts        # BodhiOnboardingModal class
│       ├── modal.html      # Embedded React modal (108k tokens)
│       ├── types.ts        # Setup-modal type definitions  
│       ├── state-generator.ts  # Platform state conversion
│       ├── detection.ts    # UAParser.js-based browser/OS detection
│       └── constants.ts    # Platform configuration and URLs
├── tests/bodhijs-test-app-react/  # React testing application
├── dist/                   # Built library artifacts
└── package.json
```

### Build Configuration

**Package Details:**
- Name: `@bodhiapp/bodhijs`
- Version: `0.0.5-dev`
- Type: ES Module ("type": "module")

**Build System:**
- Rollup with TypeScript compilation
- Multiple output formats: CJS, ESM, UMD, and TypeScript definitions
- Custom modal copying script via `copy-modal` command

**Dependencies:**
- Runtime: `@bodhiapp/ts-client@^0.1.5`, `ua-parser-js@^1.0.40`
- No other runtime dependencies (self-contained)

**Testing Infrastructure:**
- Vitest with Playwright for real browser testing
- Happy-DOM for unit testing
- Comprehensive test application integration

## Public API Interface

### Library Exports (`src/index.ts`)

The library exports **14 essential symbols** with a clean, focused API surface:

#### Core Classes (3 classes)

```typescript
// Primary platform detection and management
export { BodhiPlatform };

// Extension API client (accessed via BodhiPlatform.getClient())
export { BodhiExtClient };

// Iframe-based onboarding modal
export { BodhiOnboardingModal };
```

#### Type Definitions (11 types)

```typescript
// Core API communication types
export type { ApiResponse, StreamChunk };

// OpenAI-compatible chat types
export type { ChatRequest, ChatResponse, ChatChunk, ChatMessage };

// Platform configuration and state management
export type { BodhiPlatformState, BodhiConfig };

// Onboarding modal configuration
export type { OnboardingModalConfig, OnboardingModalCallbacks, CloseAction };
```

#### Internal Implementation (Not Exported)

**Detection System:**
- Browser/OS detection using UAParser.js (`detectBrowser`, `detectOS`)
- Platform configuration constants (`EXTENSION_DOWNLOAD_URLS`, `SERVER_DOWNLOAD_URLS`)
- Support matrices (`BROWSER_SUPPORT`, `OS_SUPPORT`)

**State Management:**
- Interface-based state design with factory function for creation
- Complex discriminated union types (`ExtensionState`, `ServerState`) 
- Setup-modal integration types (18+ type definitions)
- Error code constants and state conversion utilities
- No object duplication - single objects implement multiple interfaces

**Extension Interface:**
- Low-level window.bodhiext interface (`WindowBodhiExt`)
- Server state information types (`ServerStateInfo`)

## Core Implementation

### BodhiPlatform Class (`src/core.ts`)

**Primary Purpose:** Main entry point providing unified platform detection, state management, and onboarding integration.

#### Class Structure

```typescript
export class BodhiPlatform {
  private config: BodhiConfig;
  private currentState: BodhiPlatformState | null = null;
  private client: BodhiExtClient | null = null;
  private onboardingModal: BodhiOnboardingModal | null = null;

  constructor(config: BodhiConfig = {}); // Merges with defaults
  async initialize(): Promise<BodhiPlatformState>;
  getClient(): BodhiExtClient; // Throws if platform not ready
  showOnboarding(config: OnboardingModalConfig = {}): void;
}
```

#### Platform Configuration

```typescript
interface BodhiConfig {
  timeout?: number; // Default: 10000ms
}
```

#### Detection Logic (Internal)

**Sequential Detection Process:**
1. **Extension Detection:** Uses `loadExtensionState()` to poll for `window.bodhiext` (configurable timeout)
2. **Extension ID Retrieval:** Calls `window.bodhiext.getExtensionId()` with Promise.race timeout
3. **Server State Check:** If extension ready, calls `getServerStateInternal()` via `/bodhi/v1/info`
4. **State Construction:** Creates `BodhiPlatformState` wrapper with `isReady()` method

**Error Handling:**
- Never throws exceptions - returns appropriate error states
- Fallback error state construction for unexpected failures
- Comprehensive timeout and connection error mapping

### BodhiExtClient Class (`src/core.ts`)

**Primary Purpose:** Provides typed API interface for communicating with the Bodhi browser extension.

#### Class Structure

```typescript
export class BodhiExtClient {
  private extensionId: string;
  
  constructor(extensionId: string); // Typically called by BodhiPlatform
  getExtensionId(): string;
  
  // Core API communication methods
  async sendApiRequest(method: string, endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse>;
  async sendStreamRequest(method: string, endpoint: string, body?: any, headers?: Record<string, string>): Promise<AsyncIterable<StreamChunk>>;
  async ping(): Promise<{ message: string }>;
}
```

#### Implementation Details

**Extension Availability Checking:**
- `ensureExtensionAvailable()` validates `window.bodhiext` presence before API calls
- Throws descriptive errors if extension not available

**API Method Implementation:**
- Direct delegation to `window.bodhiext` methods
- No additional processing or error handling beyond availability checks
- Streaming responses return `AsyncIterable<StreamChunk>` for real-time processing

#### Usage Pattern

```typescript
const platform = new BodhiPlatform({ timeout: 3000 });
const state = await platform.initialize();

if (state.isReady()) {
  const client = platform.getClient();
  
  // Standard API request
  const response = await client.sendApiRequest('POST', '/v1/chat/completions', {
    model: 'llama3',
    messages: [{ role: 'user', content: 'Hello!' }]
  });
  
  // Streaming request with async iteration
  const stream = await client.sendStreamRequest('POST', '/v1/chat/completions', {
    model: 'llama3',
    messages: [{ role: 'user', content: 'Write a poem' }],
    stream: true
  });
  
  for await (const chunk of stream) {
    if (chunk.body?.choices?.[0]?.delta?.content) {
      process.stdout.write(chunk.body.choices[0].delta.content);
    }
  }
}
```

## State Management System

### BodhiPlatformState Interface (`src/types.ts`)

**Primary Purpose:** Clean public interface defining the contract for platform state objects.

#### Interface Definition

```typescript
export interface BodhiPlatformState {
  /**
   * Checks if the platform is ready for API communication.
   * @returns True if both extension and server are ready
   */
  isReady(): boolean;
}
```

#### Internal Implementation

```typescript
// Internal interface extending the public one
interface InternalPlatformState extends BodhiPlatformState {
  extension: ExtensionState;
  server: ServerState;
  _getInternalState(): InternalPlatformState;
}

// Factory function creates objects implementing both interfaces
export function createPlatformState(extension: ExtensionState, server: ServerState): InternalPlatformState {
  return {
    extension,
    server,
    isReady(): boolean {
      return this.extension.status === 'ready' && this.server.status === 'ready';
    },
    _getInternalState(): InternalPlatformState {
      return this;
    }
  };
}
```

### Internal State Architecture

**Complex discriminated unions are encapsulated as internal implementation:**

```typescript
// Internal platform state structure (exported for internal use only)
export interface InternalPlatformState extends BodhiPlatformState {
  extension: ExtensionState;  // From onboarding/types.ts
  server: ServerState;        // From onboarding/types.ts
  _getInternalState(): InternalPlatformState;
}
```

#### ExtensionState (Internal)

```typescript
// Ready state with extension details
interface ExtensionStateReady {
  status: 'ready';
  version: string;
  id: string;
}

// Error states with detailed error information
interface ExtensionStateNotReady {
  status: 'unreachable' | 'not-installed' | 'unsupported';
  error: {
    message: string;
    code: ExtensionErrorCode; // 'ext-not-installed' | 'ext-connection-failed' | 'ext-unsupported-version'
  };
}

type ExtensionState = ExtensionStateReady | ExtensionStateNotReady;
```

#### ServerState (Internal)

```typescript
// Ready server state
interface ServerStateReady {
  status: 'ready';
  version: string;
}

// Reachable but not ready (setup/admin mode)
interface ServerStateReachable {
  status: 'setup' | 'resource-admin';
  version: string;
  error: { message: string; code: ServerErrorCode; };
}

// Pending/unreachable/error states
interface ServerStatePending { status: 'pending-extension-ready'; error: {...} }
interface ServerStateUnreachable { status: 'unreachable'; error: {...} }
interface ServerStateError { status: 'error'; error: {...} }

type ServerState = ServerStateReady | ServerStateReachable | ServerStatePending | ServerStateUnreachable | ServerStateError;
```

### Server State Mapping

**Server state detection via `/bodhi/v1/info` endpoint:**

```typescript
interface ServerStateInfo {
  status: 'setup' | 'ready' | 'resource-admin' | 'error' | 'unreachable';
  version?: string;
  url?: string;
  error?: { message: string; type?: string; code?: string; param?: string; };
}
```

**Mapping Logic:**
- Maps `ServerStateInfo` responses to internal `ServerState` discriminated unions
- Handles all status variants with appropriate error codes
- Fallback error handling for unexpected responses

## Onboarding System

### BodhiOnboardingModal Class (`src/onboarding/modal.ts`)

**Primary Purpose:** Iframe-based modal hosting the setup-modal React component for user onboarding.

**Enhanced State Management (Recent Improvements):**
- **Null Initial State**: Modal now starts with `null` state to prevent race conditions and incorrect default value flashes
- **Fixed Step Navigation**: Corrected logic that was bypassing Platform Check for unsupported browsers, ensuring proper setup flow
- **Reactive State Sync**: Fixed Safari browser detection issue by implementing proper state synchronization in dropdown components
- **Loading Indicators**: Added proper loading states throughout the modal while waiting for platform state data

#### Class Structure

```typescript
export class BodhiOnboardingModal {
  private modalElement: HTMLElement | null = null;
  private iframeElement: HTMLIFrameElement | null = null;
  private platformState: InternalPlatformState;
  private config: OnboardingModalConfig;
  private callbacks: OnboardingModalCallbacks;
  private isVisible = false;
  private messageHandler: ((event: MessageEvent) => void) | null = null;
  private keydownHandler: ((event: KeyboardEvent) => void) | null = null;

  constructor(platformState: InternalPlatformState, config: OnboardingModalConfig = {});
  
  // Modal lifecycle management
  show(): void;
  hide(): void;
  updateState(platformState: InternalPlatformState): void;
  isModalVisible(): boolean;
}
```

#### Modal Configuration

```typescript
interface OnboardingModalConfig {
  dismissible?: boolean;
  callbacks?: OnboardingModalCallbacks;
}

type CloseAction = 'complete' | 'dismiss';

interface OnboardingModalCallbacks {
  onClose?: (state: BodhiPlatformState, action: CloseAction) => void;
}
```

#### Implementation Details

**Modal Construction:**
- Creates fixed overlay with backdrop blur and modal container
- Iframe with `srcdoc` attribute containing embedded React modal (108k tokens)
- Secure iframe sandbox with `allow-scripts` and `allow-same-origin`
- CSS animations and styling applied via JavaScript

**Message Communication:**
- Host → iframe: `'modal:state'` with `SetupState` data
- iframe → Host: `'modal_out:ready|refresh|complete|close'` for user actions
- Message validation by source and type prefix
- Real-time state updates via `sendStateToModal()`
- `modal_out:refresh` triggers internal state update and re-sends state to modal

**Event Handling:**
- Backdrop click dismissal (if `dismissible` configured)
- Escape key dismissal (if `dismissible` configured)
- Window message listener for iframe communication
- Proper cleanup of event listeners and DOM elements

#### Platform Integration

**Typical Usage via BodhiPlatform:**

```typescript
const platform = new BodhiPlatform();
const state = await platform.initialize();

if (!state.isReady()) {
  platform.showOnboarding({
    dismissible: true,
    callbacks: {
      onClose: (state, action) => {
        if (action === 'complete') {
          console.log('Setup completed', state);
          // Re-initialize platform after successful setup
          platform.initialize();
        } else {
          console.log('Modal dismissed', state);
        }
      }
    }
  });
}
```

### Setup-Modal Integration

#### State Conversion (`src/onboarding/state-generator.ts`)

**SetupStateGenerator Class:**
- Converts internal `BodhiPlatformState` to setup-modal compatible `SetupState`
- Uses `detectBrowser()` and `detectOS()` for environment detection
- Maps extension and server states to setup-modal discriminated unions
- **Dynamic Browser Injection:** Automatically injects actual browser info for unknown browsers
- Includes platform configuration from `PLATFORM_CONFIG`

```typescript
export class SetupStateGenerator {
  static generateSetupState(bodhiState: InternalPlatformState): SetupState {
    const browser = detectBrowser();
    const os = detectOS();

    // Prepare browsers list - inject actual browser info if unknown
    let browsersConfig = [...PLATFORM_CONFIG.browsers];
    
    // If detected browser is unknown, replace the static unknown entry with dynamic one
    if (browser.type === 'unknown') {
      // Remove static unknown browser entry if present
      browsersConfig = browsersConfig.filter(b => b.id !== 'unknown');
      // Add dynamic unknown browser configuration with actual browser name
      browsersConfig.push(createUnknownBrowserConfig(browser.name));
    }

    return {
      extension: this.convertExtensionState(bodhiState.extension),
      server: this.convertServerState(bodhiState.server),
      env: { browser: browser.type, os: os.type },
      browsers: browsersConfig,
      os: [...PLATFORM_CONFIG.os],
    };
  }
}
```

#### Browser/OS Detection (`src/onboarding/detection.ts`)

**UAParser.js Integration:**
- Uses UAParser.js v1.0.40 for reliable user-agent parsing
- Maps browser names to supported types: `chrome`, `edge`, `firefox`, `safari`, `unknown`
- Maps OS names to supported types: `macos`, `windows`, `linux`, `unknown`
- **Enhanced Unknown Browser Handling**: Preserves actual browser names (e.g., "Brave", "Opera") while setting type as `unknown`
- Simplified architecture assumptions (macOS as Silicon, Windows/Linux as x64)

```typescript
export function detectBrowser(): BrowserInfo {
  const parser = new UAParser();
  const browser = parser.getBrowser();
  const browserName = browser.name?.toLowerCase() || '';
  const actualBrowserName = browser.name || 'Unknown Browser';

  if (browserName.includes('chrome')) return { name: 'Google Chrome', type: 'chrome' };
  else if (browserName.includes('edge')) return { name: 'Microsoft Edge', type: 'edge' };
  // ... other mappings
  else {
    // Clean up browser name (remove common suffixes like "Browser")
    const cleanName = actualBrowserName
      .replace(/\s+Browser$/i, '')
      .replace(/\s+browser$/i, '')
      .trim() || 'Unknown Browser';
    
    return { name: cleanName, type: 'unknown' };
  }
}
```

#### Platform Configuration (`src/onboarding/constants.ts`)

**Current Support Matrix:**
- **Browsers:** Chrome (supported), Edge (supported), Firefox (not supported), Safari (not supported)
- **Operating Systems:** macOS (supported), Windows (not supported), Linux (not supported)
- **Download URLs:** Placeholder URLs for Chrome Web Store and Microsoft Edge Add-ons
- **GitHub Issue URLs:** Links for requesting support for unsupported platforms
- **Dynamic Unknown Browser Support:** `createUnknownBrowserConfig()` function generates browser-specific configurations with actual browser names and targeted GitHub issue URLs

**Enhanced Unknown Browser Configuration:**
```typescript
export function createUnknownBrowserConfig(browserName: string): Browser {
  // Generate GitHub issue slug from browser name
  const githubSlug = browserName.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

  return {
    id: 'unknown' as const,
    status: 'not-supported' as const,
    name: browserName,
    github_issue_url: `https://github.com/BodhiSearch/bodhi-browser/issues/${githubSlug}-support`,
  };
}
```

## Usage Patterns

### Basic Platform Integration

**Standard initialization and usage pattern:**

```typescript
import { BodhiPlatform } from '@bodhiapp/bodhijs';

// Initialize platform with timeout
const platform = new BodhiPlatform({ timeout: 3000 });
const state = await platform.initialize();

if (state.isReady()) {
  // Both extension and server are ready
  const client = platform.getClient();
  
  // API communication
  const response = await client.sendApiRequest('POST', '/v1/chat/completions', {
    model: 'llama3',
    messages: [{ role: 'user', content: 'Hello!' }]
  });
  
  // Streaming communication
  const stream = await client.sendStreamRequest('POST', '/v1/chat/completions', {
    model: 'llama3',
    messages: [{ role: 'user', content: 'Write a story' }],
    stream: true
  });
  
  for await (const chunk of stream) {
    if (chunk.body?.choices?.[0]?.delta?.content) {
      console.log(chunk.body.choices[0].delta.content);
    }
  }
} else {
  // Platform needs setup - show onboarding modal
  platform.showOnboarding({
    dismissible: true,
    callbacks: {
      onClose: (state, action) => {
        if (action === 'complete') {
          console.log('Setup completed');
          // Re-initialize platform after successful setup
          platform.initialize();
        } else {
          console.log('Modal dismissed');
        }
      }
    }
  });
}
```

### Test Application Integration

**Real-world usage from `tests/bodhijs-test-app-react/`:**

```typescript
// Platform detection hook pattern
const usePlatformDetection = () => {
  const [state, setState] = useState({
    status: 'detecting',
    platform: null,
    client: null,
    platformState: null,
    error: null
  });

  useEffect(() => {
    const detectPlatform = async () => {
      const platform = new BodhiPlatform({ timeout: 3000 });
      const platformState = await platform.initialize();
      
      if (platformState.isReady()) {
        setState({
          status: 'detected',
          platform,
          client: platform.getClient(),
          platformState,
          error: null
        });
      } else {
        setState({
          status: 'setup',
          platform,
          client: null,
          platformState,
          error: null
        });
      }
    };
    
    detectPlatform();
  }, []);

  return state;
};
```

### Error Handling Pattern

**Comprehensive error handling without exceptions:**

```typescript
try {
  const platform = new BodhiPlatform();
  const state = await platform.initialize();
  
  if (state.isReady()) {
    // Success path - no exception handling needed
    const client = platform.getClient();
    const response = await client.sendApiRequest('GET', '/ping');
  } else {
    // Platform not ready - handle via UI (modal)
    // Internal state contains detailed error information
    platform.showOnboarding();
  }
} catch (error) {
  // Only BodhiPlatform.initialize() never throws
  // Errors only occur with getClient() when platform not ready
  console.error('Platform error:', error.message);
}
```

## Type System

### Core API Types (`src/types.ts`)

**OpenAI-Compatible Types:**

```typescript
interface ChatRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: { role: string; content: string; };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface ChatChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: { role?: string; content?: string; };
    finish_reason?: string;
  }>;
}
```

**Communication Types:**

```typescript
interface ApiResponse {
  body: any;
  headers: Record<string, string>;
  status: number;
}

interface StreamChunk {
  body: any;
  headers?: Record<string, string>;
  status?: number;
}
```

### Browser Extension Interface

**Window Extension (`src/types.ts`):**

```typescript
// Internal interface for extension communication
interface WindowBodhiExt {
  extension_id: string | null; // Deprecated - use getExtensionId()
  getExtensionId(): Promise<string>;
  sendApiRequest(method: string, endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse>;
  sendStreamRequest(method: string, endpoint: string, body?: any, headers?: Record<string, string>): Promise<AsyncIterable<StreamChunk>>;
  ping(): Promise<{ message: string }>;
  serverState(): Promise<ServerStateInfo>;
}

// Global type declaration
declare global {
  interface Window {
    bodhiext?: WindowBodhiExt;
  }
}
```

## Build System

### Build Configuration

**Output Formats:**
- **CommonJS**: `dist/bodhi.cjs.js` (main entry point)
- **ES Modules**: `dist/bodhi.esm.js` (module entry point)
- **TypeScript Declarations**: `dist/index.d.ts` (types entry point)

**Package.json Configuration:**
```json
{
  "name": "@bodhiapp/bodhijs",
  "version": "0.0.5-dev",
  "type": "module",
  "main": "dist/bodhi.cjs.js",
  "module": "dist/bodhi.esm.js", 
  "types": "dist/index.d.ts",
  "files": ["dist"]
}
```

**Build Pipeline:**
1. **Prebuild**: `copy-modal` script copies setup-modal HTML content
2. **Rollup Build**: TypeScript compilation with multiple output formats
3. **Type Generation**: Separate TypeScript declaration generation

### Testing Infrastructure

**Framework Stack:**
- **Vitest**: Unit testing with Happy-DOM
- **Playwright**: Real browser automation testing
- **Test Application**: React-based integration testing at `tests/bodhijs-test-app-react/`

**Test Coverage:**
- Platform detection and initialization
- API communication (standard and streaming)
- Onboarding modal functionality
- OAuth authentication flows
- Error handling and edge cases

## Current Implementation Status

### ✅ Completed Features

**Core Library:**
1. **Platform Management**: Simplified `BodhiPlatform` class with `isReady()` pattern
2. **Extension Communication**: Full `BodhiExtClient` API with streaming support
3. **State Management**: Interface-based design with factory pattern - complex discriminated unions hidden behind simple boolean interface
4. **Onboarding System**: Complete iframe-based modal with React component integration and unified callback interface
5. **Browser/OS Detection**: UAParser.js integration with platform configuration

**Developer Experience:**
1. **Clean Public API**: 14 essential exports with simplified callback interface
2. **TypeScript Support**: Comprehensive type definitions with OpenAI compatibility
3. **Error Handling**: No-throw initialization with structured error states
4. **Testing Infrastructure**: Real browser testing with extension integration
5. **Unified Callbacks**: Single `onClose` callback with action parameter replaces multiple callback methods

### 🔧 Technical Architecture

**Design Patterns:**
- **Platform-Centric**: Single entry point (`BodhiPlatform`) managing all complexity
- **Interface-Based State**: Clean separation between public interface and internal implementation
- **Factory Pattern**: `createPlatformState()` function creates objects implementing both interfaces
- **No Object Duplication**: Single object implements both public and internal interfaces
- **Sequential Detection**: Extension detection followed by server state detection
- **Iframe Integration**: Secure modal hosting via srcdoc with postMessage communication

**Dependencies:**
- **Runtime**: `ua-parser-js@^1.0.40` for browser/OS detection, `@bodhiapp/ts-client@^0.1.5`
- **Build**: Rollup, TypeScript, and various development tools
- **Testing**: Comprehensive testing stack with real browser automation

### 🎯 Key Strengths

1. **Simplicity**: Single `isReady()` method replaces complex state checking
2. **Reliability**: Never-throw initialization with comprehensive error handling  
3. **Completeness**: Full OpenAI-compatible API with streaming support
4. **Self-Contained**: Embedded modal content removes external dependencies
5. **Type Safety**: Comprehensive TypeScript integration throughout
6. **Unified Interface**: Single callback pattern with action parameter simplifies modal integration
7. **Memory Efficient**: No wrapper objects - single object implements both interfaces
8. **Clean Architecture**: Interface-based design with factory pattern for object creation

## Usage Summary

The library provides a clean, platform-centric interface for web applications to integrate with the Bodhi Browser Extension. Users follow a simple three-step pattern:

1. **Initialize**: `const platform = new BodhiPlatform({ timeout: 3000 })`
2. **Check State**: `const state = await platform.initialize(); if (state.isReady())`
3. **Use or Onboard**: Get client for API calls or show onboarding modal

All complexity around browser detection, server state management, error handling, and onboarding integration is encapsulated within the library, providing a developer-friendly interface while maintaining full functionality. The interface-based architecture ensures memory efficiency and clean separation of concerns.