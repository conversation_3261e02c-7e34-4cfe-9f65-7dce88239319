# Linting Guide for Bodhi Browser Project

This guide outlines the key linting rules and configurations used across the Bodhi Browser project components. All code contributions should follow these guidelines.

## Common Rules Across All Projects

### Code Style
```javascript
{
  semi: true,
  singleQuote: true,
  trailingComma: 'es5',
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  bracketSpacing: true,
  arrowParens: 'avoid'
}
```

### Key Rules

1. **Indentation**: Use 2 spaces for indentation (no tabs)
2. **Line Length**: Maximum line length is 100 characters
3. **Quotes**: Use single quotes for strings
4. **Semicolons**: Required at the end of statements
5. **Arrow Functions**: Avoid parentheses around single parameters

### Variable Usage

- Unused variables are not allowed
- To mark a parameter as intentionally unused, prefix it with underscore (_):
```javascript
function example(_unusedParam) {
  // This is valid
}
```

## Project-Specific Configurations

### bodhi-browser-ext
- Has access to Chrome extension globals
- Stricter unused variable checking (error level)

### bodhi-js
- <PERSON>rows<PERSON> and Node.js globals only
- Warning level for unused variables

### integration-tests
- Additional ignore patterns for test distributions
- More permissive console usage
- Warning level for unused variables

## File Extensions

The linting configuration covers:
- `.js` and `.jsx` files (JavaScript)
- `.ts` and `.tsx` files (TypeScript)

## TypeScript-Specific Rules

For TypeScript files:
- Explicit return types are optional
- `any` type is allowed
- Unused variables trigger warnings

## Ignored Paths

Common ignored paths across all projects:
```javascript
['dist/**', 'node_modules/**']
```

## Best Practices

1. Always use meaningful variable names
2. Prefix unused parameters with underscore (_)
3. Minimize console usage according to project rules
4. Keep line lengths under 100 characters for readability
5. Use consistent spacing around operators and blocks

## Running Linting

To run linting:
```bash
make lint        # Lint all projects
make lint-fix    # Fix auto-fixable lint issues
```

Each project can also be linted individually:
```bash
make ext-lint
make bodhi-js-lint
make integration-tests-lint
``` 