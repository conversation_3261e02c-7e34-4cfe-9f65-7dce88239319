# Mock LLM Server (`mock-llm-server`)

The `mock-llm-server` is a test-time dependency designed to facilitate robust testing of Bodhi browser components and integrations. It provides a mock implementation of an OpenAI-compatible LLM server, enabling end-to-end and integration tests without requiring a live LLM backend.

## Overview

- **Purpose:** Simulate LLM API endpoints for automated and manual testing.
- **Usage Context:** Used in development and CI pipelines to ensure browser extension and related components interact correctly with LLM APIs.
- **Key Features:**
  - OpenAI-style `/v1/chat/completions` endpoint with support for both standard and streaming responses.
  - CORS enabled for all routes to simplify local and cross-origin testing.
  - Lightweight and fast to start/stop.

## Installation

Install dependencies and build the server:

```bash
cd mock-llm-server
npm install
npm run build
```

## Usage

### Start the Mock Server

You can start the mock LLM server via Node.js:

```js
import { startMockLLMServer } from '@bodhiapp/mock-llm-server';

const server = startMockLLMServer({ port: 11135 });
// ... run your tests ...
server.close();
```

Or using a test runner script (e.g., via Vitest):

```bash
npm run test
```

### API Endpoints

- `GET /ping` – Health check endpoint, returns `{ message: 'pong' }`.
- `POST /v1/chat/completions` – Accepts OpenAI-style chat completion requests. Supports both standard and streaming (SSE) responses.

#### Example Request

```json
{
  "model": "test-model",
  "messages": [{ "role": "user", "content": "Hello!" }],
  "stream": true
}
```

#### Example Streaming Response

The server will emit chunks using Server-Sent Events (SSE) to simulate streaming token responses, mimicking real LLM APIs.

## Development

- Source code is in the `src/` directory.
- TypeScript is used for type safety.
- Run `npm run lint` and `npm run test` before submitting changes.

## Typical Use Case

- Used in CI and local development to test browser extension flows that require LLM responses, without depending on a real LLM provider.
- Ensures that integration points (e.g., streaming, error handling) are robustly tested.

---

For more details, see the test cases under `mock-llm-server/tests/` or review the exported API in `src/index.ts`.