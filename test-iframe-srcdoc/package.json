{"name": "test-iframe-srcdoc", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "npm run copy-modal && vite", "build": "npm run copy-modal && vite build", "build:fast": "npm run copy-modal && node ./scripts/build-fast.mjs . 'npm run build' src package.json vite.config.ts", "serve": "npm run build:fast && npm run preview", "preview": "npx serve -s dist", "copy-modal": "cp ../setup-modal/dist/index.html src/modal.html", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest watch"}, "devDependencies": {"@playwright/test": "^1.54.2", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "playwright": "^1.54.2", "typescript": "^5.5.4", "vite": "^5.2.0", "vitest": "^3.2.4"}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1"}}