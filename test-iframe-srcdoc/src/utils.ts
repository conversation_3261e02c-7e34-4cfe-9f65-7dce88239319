import { 
  SetupState, 
  EXT_NOT_INSTALLED, 
  SERVER_PENDING_EXT_READY 
} from '@/types';

export const createCompleteSetupState = (): SetupState => ({
  extension: {
    status: 'not-installed',
    error: {
      message: 'Extension is not installed/or not detected',
      code: EXT_NOT_INSTALLED
    }
  },
  server: {
    status: 'pending-extension-ready',
    error: {
      message: 'Server waiting for extension',
      code: SERVER_PENDING_EXT_READY
    }
  },
  env: {
    browser: 'chrome',
    os: 'macos'
  },
  browsers: [
    {
      id: 'chrome',
      status: 'supported',
      name: 'Google Chrome',
      extension_url: 'https://chrome.google.com/webstore/detail/bodhi-extension/placeholder-extension-id'
    },
    {
      id: 'edge', 
      status: 'supported',
      name: 'Microsoft Edge',
      extension_url: 'https://microsoftedge.microsoft.com/addons/detail/bodhi-extension/placeholder-extension-id'
    },
    {
      id: 'firefox',
      status: 'not-supported',
      name: 'Firefox',
      github_issue_url: 'https://github.com/bodhiapps/browser-extension/issues/1'
    },
    {
      id: 'safari',
      status: 'not-supported', 
      name: 'Safari',
      github_issue_url: 'https://github.com/bodhiapps/browser-extension/issues/2'
    },
    {
      id: 'unknown',
      status: 'not-supported',
      name: 'Unknown Browser'
    }
  ],
  os: [
    {
      id: 'macos',
      status: 'supported',
      name: 'macOS',
      download_url: 'https://github.com/bodhiapps/app-server/releases/download/v1.0.0/bodhi-server-macos.dmg'
    },
    {
      id: 'windows',
      status: 'supported',
      name: 'Windows',
      download_url: 'https://github.com/bodhiapps/app-server/releases/download/v1.0.0/bodhi-server-windows.exe'
    },
    {
      id: 'linux',
      status: 'not-supported',
      name: 'Linux',
      github_issue_url: 'https://github.com/bodhiapps/app-server/issues/1'  
    },
    {
      id: 'unknown',
      status: 'not-supported',
      name: 'Unknown OS'
    }
  ]
});