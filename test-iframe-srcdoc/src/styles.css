body {
  font-family: Arial, sans-serif;
  margin: 20px;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

.iframe-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

iframe {
  width: 100%;
  height: 600px;
  border: none;
  display: block;
}

.info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.info h2, .info h3 {
  margin-top: 0;
  color: #333;
}

.info p {
  color: #666;
  line-height: 1.6;
}

/* Status Display */
.status-section {
  margin-bottom: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 15px 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.status-label {
  font-weight: 500;
  color: #333;
}

.status-value {
  font-weight: bold;
  text-transform: capitalize;
}

/* Control Panel */
.controls-section {
  margin-bottom: 20px;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.control-group {
  display: flex;
  flex-direction: column;
}

.control-group label {
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
}

.control-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
}

/* Sync status indicators */
select[data-sync-status="syncing"] {
  border-color: #f59e0b;
  background-color: #fef3c7;
}

select[data-sync-status="synced"] {
  border-color: #22c55e;
}

select[data-sync-status="error"] {
  border-color: #ef4444;
  background-color: #fecaca;
}

/* Control Buttons */
.control-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  background: #007bff;
  color: white;
}

.control-btn:hover {
  background: #0056b3;
}

.control-btn[data-operation-status="loading"] {
  background: #f59e0b;
  cursor: not-allowed;
}

.control-btn[data-operation-status="success"] {
  background: #22c55e;
}

.control-btn[data-operation-status="error"] {
  background: #ef4444;
}

/* Debug Panel */
.debug-section {
  margin-bottom: 20px;
}

.debug-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin: 15px 0;
}

.debug-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background: #f1f3f4;
  border-radius: 3px;
  font-size: 14px;
}

.debug-item span {
  font-weight: 500;
}

.debug-item code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

/* Message Log */
.message-log-section {
  margin-bottom: 20px;
}

.message-log-container {
  margin-top: 10px;
}

.message-log {
  background: #1a1a1a;
  color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  border: 1px solid #333;
}

.message-log:empty::before {
  content: "Waiting for messages...";
  color: #6c757d;
  font-style: italic;
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h2 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 5px;
  border-radius: 3px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

/* URL Testing */
.url-testing {
  border-top: 1px solid #dee2e6;
  padding-top: 20px;
}

.url-list ul {
  list-style-type: none;
  padding: 0;
  margin: 10px 0;
}

.url-list li {
  margin-bottom: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
}

.url-list a {
  color: #007bff;
  text-decoration: none;
  word-break: break-all;
}

.url-list a:hover {
  text-decoration: underline;
}