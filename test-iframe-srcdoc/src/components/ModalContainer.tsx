import React, { useEffect, useRef } from 'react';
import { SetupState, MODAL_MESSAGE_STATE, MODAL_OUT_COMPLETE, MODAL_OUT_CLOSE } from '@/types';
import modalHtml from '@/modal.html?raw';

interface ModalContainerProps {
  setupState: SetupState;
  onClose: () => void;
  onMessage: (message: any) => void;
}

export function ModalContainer({ setupState, onClose, onMessage }: ModalContainerProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const sendStateToModal = () => {
    if (iframeRef.current?.contentWindow) {
      iframeRef.current.contentWindow.postMessage({
        type: MODAL_MESSAGE_STATE,
        data: setupState
      }, '*');
      console.log('Sent state to modal:', setupState);
    }
  };

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (!event.data?.type?.startsWith('modal_out:')) return;
      
      console.log('Received message from modal:', event.data);
      onMessage(event.data);
      
      // Auto-close on complete/close messages
      if (event.data.type === MODAL_OUT_COMPLETE || event.data.type === MODAL_OUT_CLOSE) {
        onClose();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [onMessage, onClose]);

  // Send state whenever it changes
  useEffect(() => {
    if (iframeRef.current?.contentWindow) {
      sendStateToModal();
    }
  }, [setupState]);

  const handleIframeLoad = () => {
    // Give iframe a moment to initialize before sending state
    setTimeout(() => {
      sendStateToModal();
    }, 100);
  };

  return (
    <div className="iframe-container">
      <div className="modal-header">
        <h2>Bodhi Modal Test</h2>
      </div>
      <iframe
        ref={iframeRef}
        id="wizard-iframe"
        srcDoc={modalHtml}
        title="Setup Wizard"
        onLoad={handleIframeLoad}
        style={{
          width: '100%',
          height: '600px',
          border: 'none',
          display: 'block'
        }}
      />
    </div>
  );
}