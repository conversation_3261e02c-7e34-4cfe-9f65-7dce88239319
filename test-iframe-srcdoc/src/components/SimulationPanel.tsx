import React from 'react';
import { SetupState } from '@/types';
import { ControlPanel } from './ControlPanel';

interface SimulationPanelProps {
  state: SetupState;
  onChange: (state: SetupState) => void;
  onLaunchModal: () => void;
  disabled: boolean;
}

export function SimulationPanel({ state, onChange, onLaunchModal, disabled }: SimulationPanelProps) {
  const handleSimulateRefresh = () => {
    // Simulate a refresh by updating extension and server to ready state
    const refreshedState: SetupState = {
      ...state,
      extension: {
        status: 'ready',
        id: 'extension-id-123',
        version: '1.0.0'
      },
      server: {
        status: 'ready',
        version: '2.0.0',
        url: 'http://localhost:3000'
      }
    };
    onChange(refreshedState);
  };

  return (
    <div className="info">
      <div className="panel-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2 style={{ margin: 0 }}>Modal Simulation Controls</h2>
        <button 
          onClick={onLaunchModal}
          disabled={disabled}
          className="control-btn"
          style={{ 
            backgroundColor: disabled ? '#6b7280' : '#007bff',
            cursor: disabled ? 'not-allowed' : 'pointer'
          }}
        >
          {disabled ? 'Modal Active' : 'Launch New Modal'}
        </button>
      </div>
      
      <ControlPanel
        state={state}
        onStateChange={onChange}
        onSendState={() => {}} // Not needed in new architecture
        onSimulateRefresh={handleSimulateRefresh}
      />
      
      <div className="quick-actions" style={{ marginTop: '20px' }}>
        <h3>Quick Actions</h3>
        <div className="control-buttons">
          <button 
            onClick={handleSimulateRefresh}
            className="control-btn"
          >
            Simulate Successful Setup
          </button>
          <button 
            onClick={() => onChange({
              ...state,
              env: { browser: 'chrome', os: 'macos' },
              extension: { 
                status: 'not-installed',
                error: { message: 'Extension is not installed/or not detected', code: 'ext-not-installed' }
              },
              server: { 
                status: 'pending-extension-ready',
                error: { message: 'Server waiting for extension', code: 'server-pending-ext-ready' }
              }
            })}
            className="control-btn"
          >
            Reset to Initial State
          </button>
        </div>
      </div>

      <div className="state-preview" style={{ marginTop: '20px' }}>
        <h3>Current State Preview</h3>
        <div className="debug-grid">
          <div className="debug-item">
            <span>Browser:</span>
            <code>{state.env.browser}</code>
          </div>
          <div className="debug-item">
            <span>OS:</span>
            <code>{state.env.os}</code>
          </div>
          <div className="debug-item">
            <span>Extension:</span>
            <code>{state.extension.status}</code>
          </div>
          <div className="debug-item">
            <span>Server:</span>
            <code>{state.server.status}</code>
          </div>
          <div className="debug-item">
            <span>Browsers Available:</span>
            <code>{state.browsers.length}</code>
          </div>
          <div className="debug-item">
            <span>OS Available:</span>
            <code>{state.os.length}</code>
          </div>
        </div>
      </div>

      <div className="url-testing" style={{ marginTop: '20px' }}>
        <h3>URL Testing</h3>
        <div className="url-list">
          <h4>Extension URLs (Supported Browsers):</h4>
          <ul>
            {state.browsers.filter(b => b.status === 'supported').map(browser => (
              <li key={browser.id}>
                <strong>{browser.name}:</strong>{' '}
                <a href={browser.extension_url} target="_blank" rel="noopener noreferrer">
                  {browser.extension_url}
                </a>
              </li>
            ))}
          </ul>
          
          <h4>Server Download URLs (Supported OS):</h4>
          <ul>
            {state.os.filter(o => o.status === 'supported').map(os => (
              <li key={os.id}>
                <strong>{os.name}:</strong>{' '}
                <a href={os.download_url} target="_blank" rel="noopener noreferrer">
                  {os.download_url}
                </a>
              </li>
            ))}
          </ul>

          <h4>GitHub Issues (Unsupported Platforms):</h4>
          <ul>
            {[
              ...state.browsers.filter(b => b.status === 'not-supported' && b.github_issue_url),
              ...state.os.filter(o => o.status === 'not-supported' && o.github_issue_url)
            ].map((platform, index) => (
              <li key={index}>
                <strong>{platform.name}:</strong>{' '}
                <a href={platform.github_issue_url} target="_blank" rel="noopener noreferrer">
                  {platform.github_issue_url}
                </a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}