import React from 'react';
import { SetupState, BrowserType, OSType, EXT_NOT_INSTALLED, EXT_CONNECTION_FAILED, EXT_UNSUPPORTED_VERSION, SERVER_PENDING_EXT_READY, SERVER_IN_SETUP_STATUS, SERVER_IN_ADMIN_STATUS, SERVER_CONN_REFUSED, SERVER_UNEXPECTED_ERROR } from '@/types';

interface ControlPanelProps {
  state: SetupState;
  onStateChange: (newState: SetupState) => void;
  onSendState: () => void;
  onSimulateRefresh: () => void;
}

export function ControlPanel({ state, onStateChange, onSendState, onSimulateRefresh }: ControlPanelProps) {
  const handleControlChange = (field: string, value: string) => {
    let updatedState = { ...state };

    switch (field) {
      case 'browser':
        updatedState.env.browser = value as BrowserType;
        break;
      case 'os':
        updatedState.env.os = value as OSType;
        break;
      case 'extension':
        if (value === 'ready') {
          updatedState.extension = {
            status: 'ready',
            id: 'extension-id-123',
            version: '1.0.0'
          };
        } else {
          let errorCode = EXT_NOT_INSTALLED;
          let errorMessage = 'Extension is not installed/or not detected';
          
          switch (value) {
            case 'unreachable':
              errorCode = EXT_CONNECTION_FAILED;
              errorMessage = 'Could not connect to extension';
              break;
            case 'unsupported':
              errorCode = EXT_UNSUPPORTED_VERSION;
              errorMessage = 'Extension version is unsupported';
              break;
          }
          
          updatedState.extension = {
            status: value as any,
            error: {
              message: errorMessage,
              code: errorCode
            }
          };
        }
        break;
      case 'server':
        let serverState: any = { status: value };

        switch (value) {
          case 'ready':
            serverState = {
              status: 'ready',
              version: '2.0.0',
              url: 'http://localhost:3000'
            };
            break;
          case 'setup':
            serverState = {
              status: 'setup',
              version: '2.0.0',
              url: 'http://localhost:3000',
              error: {
                message: 'Server requires initial setup',
                code: SERVER_IN_SETUP_STATUS
              }
            };
            break;
          case 'resource-admin':
            serverState = {
              status: 'resource-admin',
              version: '2.0.0',
              url: 'http://localhost:3000',
              error: {
                message: 'Server requires admin approval',
                code: SERVER_IN_ADMIN_STATUS
              }
            };
            break;
          case 'pending-extension-ready':
            serverState = {
              status: 'pending-extension-ready',
              error: {
                message: 'Server waiting for extension',
                code: SERVER_PENDING_EXT_READY
              }
            };
            break;
          case 'unreachable':
            serverState = {
              status: 'unreachable',
              error: {
                message: 'Server connection refused',
                code: SERVER_CONN_REFUSED
              }
            };
            break;
          case 'error':
            serverState = {
              status: 'error',
              error: {
                message: 'Unexpected server error',
                code: SERVER_UNEXPECTED_ERROR
              }
            };
            break;
        }

        updatedState.server = serverState;
        break;
    }

    onStateChange(updatedState);
  };

  return (
    <div className="controls-section">
      <h2>Simulation Controls</h2>
      <div className="controls-grid">
        <div className="control-group">
          <label htmlFor="browser-select">Browser:</label>
          <select
            id="browser-select"
            data-testid="browser-select"
            value={state.env.browser}
            onChange={(e) => handleControlChange('browser', e.target.value)}
            data-sync-status="synced"
          >
            <option value="unknown">Unknown</option>
            <option value="chrome">Chrome</option>
            <option value="edge">Edge</option>
            <option value="firefox">Firefox</option>
            <option value="safari">Safari</option>
          </select>
        </div>

        <div className="control-group">
          <label htmlFor="os-select">Operating System:</label>
          <select
            id="os-select"
            data-testid="os-select"
            value={state.env.os}
            onChange={(e) => handleControlChange('os', e.target.value)}
            data-sync-status="synced"
          >
            <option value="unknown">Unknown</option>
            <option value="macos">macOS</option>
            <option value="windows">Windows</option>
            <option value="linux">Linux</option>
          </select>
        </div>

        <div className="control-group">
          <label htmlFor="extension-select">Extension Status:</label>
          <select
            id="extension-select"
            data-testid="extension-status"
            value={state.extension.status}
            onChange={(e) => handleControlChange('extension', e.target.value)}
            data-sync-status="synced"
          >
            <option value="not-installed">Not Installed</option>
            <option value="ready">Ready</option>
            <option value="unreachable">Unreachable</option>
            <option value="unsupported">Unsupported</option>
          </select>
        </div>

        <div className="control-group">
          <label htmlFor="server-select">Server Status:</label>
          <select
            id="server-select"
            data-testid="server-status"
            value={state.server.status}
            onChange={(e) => handleControlChange('server', e.target.value)}
            data-sync-status="synced"
          >
            <option value="pending-extension-ready">Pending Extension</option>
            <option value="setup">Setup</option>
            <option value="ready">Ready</option>
            <option value="resource-admin">Resource Admin</option>
            <option value="unreachable">Unreachable</option>
            <option value="error">Error</option>
          </select>
        </div>
      </div>

      <div className="control-buttons">
        <button
          id="send-state-btn"
          onClick={onSendState}
          className="control-btn"
          data-operation-status="ready"
        >
          Send State to Iframe
        </button>
        <button
          id="simulate-refresh-btn"
          onClick={onSimulateRefresh}
          className="control-btn"
          data-operation-status="ready"
        >
          Simulate Refresh
        </button>
      </div>
    </div>
  );
}