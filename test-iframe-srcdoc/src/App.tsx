import React, { useState } from 'react';
import { 
  SetupState,
  MODAL_OUT_READY, 
  MODAL_OUT_REFRESH,
  MODAL_OUT_COMPLETE,
  MODAL_OUT_CLOSE
} from '@/types';
import { createCompleteSetupState } from '@/utils';
import { ModalContainer } from '@/components/ModalContainer';
import { SimulationPanel } from '@/components/SimulationPanel';
import { MessageLog } from '@/components/MessageLog';

export default function App() {
  const [setupState, setSetupState] = useState<SetupState>(createCompleteSetupState());
  const [showModal, setShowModal] = useState(false);
  const [messages, setMessages] = useState<string[]>([]);

  const logMessage = (type: 'sent' | 'received' | 'info', message: string, data?: any) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}${data ? '\n' + JSON.stringify(data, null, 2) : ''}\n\n`;
    setMessages(prev => [...prev, logEntry]);
    console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`, data || '');
  };

  const handleModalMessage = (message: any) => {
    const action = message.type.replace('modal_out:', '');
    logMessage('received', `Action from modal: ${action}`, message);

    switch (message.type) {
      case MODAL_OUT_READY:
        logMessage('info', '🚀 Modal is ready - initial state will be sent automatically');
        break;

      case MODAL_OUT_REFRESH:
        logMessage('info', '🔄 Modal requested refresh - sending current simulation state');
        // The ModalContainer will automatically send the current setupState
        break;

      case MODAL_OUT_COMPLETE:
        logMessage('info', '✅ Setup completed successfully! Modal will close automatically.');
        closeModal();
        break;

      case MODAL_OUT_CLOSE:
        logMessage('info', '❌ Modal closed by user');
        closeModal();
        break;

      default:
        logMessage('info', `🔧 Unhandled action: ${message.type}`);
        break;
    }
  };

  const openModal = () => {
    setShowModal(true);
    logMessage('info', '🔄 Launching new modal instance');
  };

  const closeModal = () => {
    setShowModal(false);
    logMessage('info', '🔒 Modal closed');
  };

  return (
    <div className="container">
      <h1>Bodhi Platform Setup Wizard Modal - Test App</h1>
      
      {showModal ? (
        <>
          <ModalContainer
            setupState={setupState}
            onClose={closeModal}
            onMessage={handleModalMessage}
          />
          
          <SimulationPanel
            state={setupState}
            onChange={setSetupState}
            onLaunchModal={openModal}
            disabled={true} // Modal is active
          />
        </>
      ) : (
        <>
          <div className="info">
            <h2>Modal Closed</h2>
            <p>The modal has been closed. You can launch a new modal to test again.</p>
            <button onClick={openModal} className="control-btn" style={{ fontSize: '16px', padding: '12px 24px' }}>
              Launch Modal
            </button>
          </div>
          
          <SimulationPanel
            state={setupState}
            onChange={setSetupState}
            onLaunchModal={openModal}
            disabled={false} // Modal not active
          />
        </>
      )}
      
      <MessageLog messages={messages} />
    </div>
  );
}