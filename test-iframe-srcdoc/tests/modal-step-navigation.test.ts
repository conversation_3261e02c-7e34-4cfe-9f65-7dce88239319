import { Browser, chromium, Page } from 'playwright';
import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, test } from 'vitest';
import { SetupStep } from '../src/types';

describe('Modal Step Navigation - Parameterized Testing', () => {
  let browser: Browser;
  let testUrl: string;
  let page: Page;

  beforeAll(async () => {
    testUrl = globalThis.TEST_URL;
    browser = await chromium.launch({ headless: process.env.CI ? true : false });
  });

  afterAll(async () => {
    await browser?.close();
  });

  beforeEach(async () => {
    page = await browser.newPage();
  });

  afterEach(async () => {
    await page?.close();
  });

  // Test scenarios based on state combinations
  const testScenarios = [
    // Scenario 1: Supported OS/Browser, Extension not-installed, Server pending -> Step 2 (Extension Setup)
    {
      name: 'supported platform, extension not-installed, server pending -> Extension Setup',
      os: 'macos',
      browser: 'chrome',
      extensionStatus: 'not-installed',
      serverStatus: 'pending-extension-ready',
      expectedStep: SetupStep.EXTENSION_SETUP,
      expectedText: 'Extension is not installed'
    },

    // Scenario 2: Unsupported OS, Supported Browser -> Platform Check Error
    {
      name: 'unsupported OS, supported browser -> Platform Check',
      os: 'linux',
      browser: 'chrome',
      extensionStatus: 'ready',
      serverStatus: 'ready',
      expectedStep: SetupStep.PLATFORM_CHECK,
      expectedText: 'Currently Supported Platforms'
    },

    // Scenario 3: Supported OS, Unsupported Browser -> Platform Check Error
    {
      name: 'supported OS, unsupported browser -> Platform Check',
      os: 'macos',
      browser: 'firefox',
      extensionStatus: 'ready',
      serverStatus: 'ready',
      expectedStep: SetupStep.PLATFORM_CHECK,
      expectedText: 'Currently Supported Platforms'
    },

    // Scenario 4: Unsupported OS + Browser -> Platform Check
    {
      name: 'unsupported OS and browser -> Platform Check',
      os: 'linux',
      browser: 'firefox',
      extensionStatus: 'not-installed',
      serverStatus: 'unreachable',
      expectedStep: SetupStep.PLATFORM_CHECK,
      expectedText: 'Currently Supported Platforms'
    },

    // Scenario 5: Supported OS/Browser, Extension ready, Server not-ready -> Server Setup
    {
      name: 'supported platform, extension ready, server not-ready -> Server Setup',
      os: 'macos',
      browser: 'chrome',
      extensionStatus: 'ready',
      serverStatus: 'unreachable',
      expectedStep: SetupStep.SERVER_SETUP,
      expectedText: 'Server connection refused'
    },

    // Scenario 6: Supported OS/Browser, Extension ready, Server ready -> Complete
    {
      name: 'supported platform, extension ready, server ready -> Complete',
      os: 'macos',
      browser: 'chrome',
      extensionStatus: 'ready',
      serverStatus: 'ready',
      expectedStep: SetupStep.COMPLETE,
      expectedText: 'All Systems Ready!'
    },

    // Additional edge cases
    {
      name: 'supported platform, extension unreachable, server ready -> Extension Setup',
      os: 'windows',
      browser: 'edge',
      extensionStatus: 'unreachable',
      serverStatus: 'ready',
      expectedStep: SetupStep.EXTENSION_SETUP,
      expectedText: 'Could not connect to extension'
    },

    {
      name: 'supported platform, extension ready, server setup -> Server Setup',
      os: 'windows',
      browser: 'edge',
      extensionStatus: 'ready',
      serverStatus: 'setup',
      expectedStep: SetupStep.SERVER_SETUP,
      expectedText: 'Server requires initial setup'
    }
  ];

  test.each(testScenarios)('should navigate to step $expectedStep: when $name', async (scenario) => {
    await page.goto(testUrl);

    // Wait for page to load
    await page.waitForSelector('h1');

    // Set the state using simulation controls
    await page.locator('select[data-testid="os-select"]').selectOption(scenario.os);
    await page.locator('select[data-testid="browser-select"]').selectOption(scenario.browser);
    await page.locator('select[data-testid="extension-status"]').selectOption(scenario.extensionStatus);
    await page.locator('select[data-testid="server-status"]').selectOption(scenario.serverStatus);

    // Launch modal
    await page.locator('button').filter({ hasText: 'Launch Modal' }).click();
    await page.waitForSelector('iframe#wizard-iframe');

    // Get iframe and wait for loading indicator to disappear (parent data loaded)
    const iframe = page.frameLocator('iframe#wizard-iframe');

    // Wait for loading indicator to disappear (meaning parent data has been received)
    await iframe.locator('[data-testid="loading-indicator"]').waitFor({ state: 'hidden' });

    // Wait for step indicators to be present
    await iframe.locator('[data-testid^="step-"]').first().waitFor();

    // Give a moment for step navigation to complete after parent data is processed
    await page.waitForTimeout(100);

    // Map expected step enum to testid
    const expectedStepId = `step-${scenario.expectedStep}`;

    // Get the actual text content from the step content area to see what step is displayed
    const stepContentArea = iframe.locator('.mt-6.overflow-y-auto.h-\\[400px\\]');
    const actualStepText = await stepContentArea.textContent();

    // Also check that the correct step indicator is active by looking at ring classes
    const currentStepElement = await iframe.locator('[data-testid^="step-"]').evaluateAll(elements => {
      for (const element of elements) {
        // Look for the ring classes in the child div
        const circleDiv = element.querySelector('div[class*="ring-2"][class*="ring-blue-300"]');
        if (circleDiv) {
          return element.getAttribute('data-testid');
        }
      }

      // If no current step found, check for fallback cases:
      // 1. Complete step is marked as complete (green checkmark)
      // 2. Extension step has error styling (red background)
      // 3. Server step has error styling (red background)
      for (const element of elements) {
        const testId = element.getAttribute('data-testid');
        if (testId === 'step-complete') {
          const completeDiv = element.querySelector('div[class*="bg-green-100"]');
          if (completeDiv) {
            return 'step-complete';
          }
        }
        if (testId === 'step-extension-setup') {
          const errorDiv = element.querySelector('div[class*="bg-red-100"]');
          if (errorDiv) {
            return 'step-extension-setup';
          }
        }
        if (testId === 'step-server-setup') {
          const errorDiv = element.querySelector('div[class*="bg-red-100"]');
          if (errorDiv) {
            return 'step-server-setup';
          }
        }
      }

      return null;
    });

    // Assert both content and step indicator match
    expect(actualStepText).toContain(scenario.expectedText);
    expect(currentStepElement).toBe(expectedStepId);

    // Verify step indicators count (should always be 4)
    const stepCount = await iframe.locator('[data-testid^="step-"]').count();
    expect(stepCount).toBe(4);
  });

  test('should handle modal close correctly', async () => {
    await page.goto(testUrl);

    await page.waitForSelector('h1');

    // Launch modal
    await page.locator('button').filter({ hasText: 'Launch Modal' }).click();
    await page.waitForSelector('iframe#wizard-iframe');

    // Get iframe and wait for loading indicator to disappear
    const iframe = page.frameLocator('iframe#wizard-iframe');
    await iframe.locator('[data-testid="loading-indicator"]').waitFor({ state: 'hidden' });

    // Close modal using the close button inside the iframe
    await iframe.locator('[data-testid="close-button"]').click();

    // Wait for modal to disappear
    await page.waitForFunction(() => !document.querySelector('iframe#wizard-iframe'));

    // Should show "Modal Closed" message
    const modalClosedText = await page.locator('.info h2').first().textContent();
    expect(modalClosedText).toContain('Modal Closed');
  });

  test('should close modal when clicking Continue to Webpage button', async () => {
    await page.goto(testUrl);

    await page.waitForSelector('h1');

    // Set up complete state (extension ready, server ready)
    await page.locator('select[data-testid="extension-status"]').selectOption('ready');
    await page.locator('select[data-testid="server-status"]').selectOption('ready');

    // Launch modal
    await page.locator('button').filter({ hasText: 'Launch Modal' }).click();
    await page.waitForSelector('iframe#wizard-iframe');

    // Get iframe and wait for loading indicator to disappear
    const iframe = page.frameLocator('iframe#wizard-iframe');
    await iframe.locator('[data-testid="loading-indicator"]').waitFor({ state: 'hidden' });

    // Verify modal is in complete state
    await iframe.getByText('All Systems Ready!').waitFor();

    // Click Continue to Webpage button
    await iframe.getByText('Continue to Webpage').click();

    // Wait for modal to disappear
    await page.waitForFunction(() => !document.querySelector('iframe#wizard-iframe'));

    // Should show "Modal Closed" message
    const modalClosedText = await page.locator('.info h2').first().textContent();
    expect(modalClosedText).toContain('Modal Closed');
  });
});