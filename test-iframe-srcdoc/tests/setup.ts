import { ChildProcess, execSync, spawn } from 'child_process';
import { join } from 'path';
import { chromium } from 'playwright';
import { afterAll, beforeAll } from 'vitest';

// Declare global variables for test access
declare global {
  var TEST_URL: string;
}

let serverProcess: ChildProcess | null = null;

// Find a random available port
function getRandomPort(): number {
  return Math.floor(Math.random() * (9999 - 9000) + 9000)
}

// Wait for server to be ready
async function waitForServer(url: string): Promise<void> {
  const maxAttempts = 50
  let attempts = 0
  while (attempts < maxAttempts) {
    try {
      const response = await fetch(url)
      if (response.ok) {
        return
      }
    } catch (error) {
    }
    await new Promise(resolve => setTimeout(resolve, 100))
    attempts++
  }
  throw new Error(`Server did not become ready after ${maxAttempts} attempts`)
}

// Start the preview server
async function startServer(): Promise<string> {
  return new Promise((resolve, reject) => {
    const testPort = getRandomPort()
    const testUrl = `http://localhost:${testPort}`
    const testAppDir = join(__dirname, '..');
    const appDir = join(testAppDir, '../setup-modal');
    execSync('npm run build:fast', { stdio: 'inherit', cwd: appDir });
    execSync('npm run build', { stdio: 'inherit', cwd: testAppDir });
    const args = ['run', 'preview', '--', '-l', testPort.toString()];
    serverProcess = spawn('npm', args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: testAppDir,
      env: {
        ...process.env,
      }
    })
    serverProcess.stdout?.on('data', (data) => {
      const output = data.toString()
      console.log(`[server] ${output}`)
    });
    serverProcess.stderr?.on('data', (data) => {
      console.error(`[server-error] ${data}`)
    });
    serverProcess.on('error', (error) => {
      reject(new Error(`Failed to start server: ${error.message}`))
    });
    serverProcess.on('exit', (code) => {
      if (code !== 0 && code !== null) {
        reject(new Error(`Server process exited with code ${code}`))
      }
    });
    waitForServer(testUrl).then(() => {
      resolve(testUrl)
    }).catch((error) => {
      reject(error)
    });
  })
}

// Global setup function for vitest
beforeAll(async () => {
  globalThis.TEST_URL = await startServer();
});

afterAll(async () => {
  if (serverProcess) {
    serverProcess.kill('SIGTERM')
  }
  await new Promise<void>((resolve) => {
    const timeout = setTimeout(() => {
      if (serverProcess && !serverProcess.killed) {
        serverProcess.kill('SIGKILL')
      }
      resolve()
    }, 3000)

    serverProcess?.on('exit', () => {
      clearTimeout(timeout)
      resolve()
    })
  });
});