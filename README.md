# Bodhi Browser

A secure browser extension and JavaScript library for interacting with local LLM services. This project provides a bridge between web applications and locally running LLM services with an OpenAI-compatible API.

## Project Components

The project consists of three main components:

1. **bodhi-browser-ext**: Chrome extension that provides secure communication with local LLM services
2. **bodhijs**: JavaScript library (@bodhiapp/bodhijs) that provides an easy-to-use interface for web applications
3. **integration-tests**: End-to-end tests that verify the extension and library work together correctly

## Quick Start

### Installation

1. Clone this repository:
```bash
git clone https://github.com/BodhiSearch/bodhi-browser.git
cd bodhi-browser
```

2. Install dependencies and build all components:
```bash
make setup
make build
```

3. Load the extension in Chrome:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer Mode" in the top right
   - Click "Load unpacked" and select the `bodhi-browser-ext/dist` directory

4. Use the library in your web application:
```html
<!-- Via script tag -->
<script src="dist/bodhi.js"></script>

<!-- Or via npm -->
npm install @bodhiapp/bodhijs
```

## Usage

### Basic Example
```javascript
// Check if extension is installed
const isInstalled = bodhijs.isInstalled();
console.log('Extension installed:', isInstalled);

// Use chat completions
const response = await bodhijs.chat.completions.create({
  model: 'bartowski/Meta-Llama-3.1-8B-Instruct-GGUF:Q4_K_M',
  messages: [
    { role: 'user', content: 'Hello, how can you help me?' }
  ]
});
```

## Development

### Project Structure
```
bodhi-browser/
├── bodhi-browser-ext/  # Chrome extension
├── bodhi-js/          # JavaScript library
└── integration-tests/  # End-to-end tests
```

### Commands

```bash
# Install all dependencies
make setup

# Build all components
make build

# Run all tests
make test

# Run lint checks
make lint

# Clean build artifacts
make clean

# Build and test specific components
make bodhi-js
make bodhi-browser-ext
```

### Component-Specific Development

#### Chrome Extension
```bash
cd bodhi-browser-ext
npm run validate  # Run format and lint checks
npm run build    # Build the extension
npm test         # Run extension tests
```

#### JavaScript Library
```bash
cd bodhi-js
npm run validate  # Run format and lint checks
npm run build    # Build the library
npm test         # Run library tests
```

## Testing

The project uses a comprehensive testing strategy:

1. **Unit Tests**: Individual component testing
2. **Integration Tests**: Testing components working together
3. **End-to-End Tests**: Full system testing with browser interaction

Run all tests:
```bash
make test
```

## Release Process

### Chrome Extension
```bash
make release-bodhi-browser-ext
```

### JavaScript Library
```bash
make release-bodhi-js
```

Both releases are managed through GitHub Actions, which will:
- Run tests
- Build the components
- Create GitHub releases
- Publish packages (npm for bodhijs)

## Security

- Content Security Policy (CSP) implementation
- Secure message passing between components
- Minimal permissions model
- Zero-dependency core functionality
- Regular security audits

## Contributing

1. Fork the repository
2. Create your feature branch: `git checkout -b feature/my-new-feature`
3. Run validation: `make lint`
4. Commit your changes: `git commit -am 'Add some feature'`
5. Push to the branch: `git push origin feature/my-new-feature`
6. Submit a pull request

## Documentation

- [Chrome Extension Documentation](./bodhi-browser-ext/README.md)
- [JavaScript Library Documentation](./bodhi-js/README.md)
- [Integration Tests](./integration-tests/README.md)

## License

MIT
