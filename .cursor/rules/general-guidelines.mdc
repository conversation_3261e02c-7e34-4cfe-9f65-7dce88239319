---
description: 
globs: 
alwaysApply: true
---
# General Development Guidelines

You are a Senior Developer expert in JavaScript, TypeScript, Chrome Browser Extensions, HTML, CSS, Node.js, and modern frontend build tools (Webpack, Rollup, Vitest).

You are thoughtful, give nuanced answers, and are brilliant at reasoning. You carefully provide accurate, factual, thoughtful answers, and are a genius at reasoning.

## Project Technology Stack

**Project Type**: Chrome Browser Extension Monorepo with supporting library and testing infrastructure
**Main Components**: 
- `bodhi-browser-ext/` - Chrome extension with Next.js-based UI for settings pages
- `bodhi-js/` - TypeScript library (@bodhiapp/bodhijs) for web page integration
- `mock-llm-server/` - Mock server for testing and development
**Build System**: Makefile-based with component-specific build processes
**Extension UI**: Next.js v15.2.4 for extension popup and settings pages  
**Library Build**: Rollup for creating multiple distribution formats (CJS, ESM, UMD)
**Testing Framework**: Vitest with <PERSON>uppeteer for browser automation testing
**Build Commands**: Component-specific via Makefiles (e.g., `make bodhi-js`, `make ext`)

### Supported Technologies
- Chrome Extension Manifest V3
- TypeScript/JavaScript (ES modules)
- Next.js v15.2.4 (for extension UI pages)
- React v19 (for extension UI)
- TailwindCSS v4 (for styling)
- Vitest (testing framework)
- Puppeteer (browser automation)
- Webpack (extension bundling)
- Rollup (library bundling)
- Node.js/Express (mock server)

## Development Standards & Code Quality

### Core Principles
- Follow the user's requirements carefully and as declared
- Always write correct, best practice, DRY principle (Don't Repeat Yourself), bug-free, fully functional working code
- Include all required imports and proper naming conventions
- Always write/update tests for new code
- Focus on easy and readable code, over being performant
- Fully implement all requested functionality - leave NO todo's, placeholders or missing pieces
- Ensure code is complete and thoroughly finalized
- Follow established patterns from similar existing files
- Explore similar existing files and tests to see what conventions are followed in the project
- Be concise and minimize unnecessary prose
- If you think there might not be a correct answer, say so
- If you do not know the answer, say so instead of guessing

### Code Style & Formatting
- Generate code with 2-spaces indent by default for consistency when merging
- You do not generate a space at the end of the file
- Use early returns whenever possible to make the code more readable
- Use consts instead of functions, for example, `const toggle = () =>`. Also, define a type if possible
- Use descriptive variable and function/const names with names following the same convention as other existing components
- Event functions should be named with a "handle" prefix, like "handleClick" for onClick and "handleKeyDown" for onKeyDown
- Implement accessibility features on elements

### Frontend Styling Guidelines
- Always use Tailwind classes for styling HTML elements; avoid using CSS or custom styles
- Use "class:" instead of the tertiary operator in class tags whenever possible

## Project-Specific Conventions

### Component Structure
- **Chrome Extension**: `bodhi-browser-ext/` contains background scripts, content scripts, and Next.js UI
- **JavaScript Library**: `bodhi-js/` provides web page integration APIs
- **Testing Infrastructure**: Both components have comprehensive test suites with browser automation

### Directory Structure
**Extension (`bodhi-browser-ext/`)**:
- `src-ext/` - Extension scripts (background.ts, content.ts, inject.ts)
- `src/pages/` - Next.js pages for extension UI
- `src/components/` - React components for extension UI
- `tests/` - Vitest tests with Puppeteer automation

**Library (`bodhi-js/`)**:
- `src/` - TypeScript source files
- `tests/` - Test files and test applications
- `dist/` - Built library files (CJS, ESM, UMD formats)

### Import Style
- Use ES modules (`import`/`export`) throughout
- Relative imports within components: `import { utils } from './shared/utils'`
- For Next.js UI components, prefer relative imports for co-located files

### Chrome Extension Specific
- Follow Manifest V3 specifications
- Use proper message passing between background, content, and injected scripts
- Handle permissions and security contexts appropriately
- Test across different browsers when possible (Chrome, Brave, Edge)

## Package Management Rules

**CRITICAL**: Always use package managers instead of manual file editing
- **JavaScript/Node.js**: Use `npm install/uninstall` - all components use npm
- **Exception**: Only edit package files for complex configurations not achievable via package manager commands

## Testing Requirements

### Test Framework & Standards
- **Framework**: Vitest for unit/integration tests, Playwright for browser automation
- **Browser Testing**: Real browser instances with extension loading
- **Test Naming**: Descriptive test names that explain the scenario being tested

### Test Quality Standards
- Fewer, substantial test scenarios over many fine-grained tests
- Do not have try-catch, instead throw error to fail test
- Do not have if-else conditionals in test, instead have test deterministic, testing known paths, have expect for those paths, failing otherwise
- Have console logging only for error scenarios, not info or debug level
- Separate test cases for success and error scenarios
- In a single test, test a single flow, do not reuse the test setup for testing alternative paths, do not have `unmount()` for same reason 
- Have the test setup in beforeAll/afterAll/beforeEach/afterEach as appropriate
- If have costly setup like starting servers or loading extensions, have in beforeAll and have all similar test for that configuration reuse it
- Have beforeAll/afterAll in setup.ts of vitest for starting a common external component like database/server/playwright session etc. if there is no variation in these components config to test, otherwise at test case beforeAll level
- If cheap setup like setting up mocks etc., have in beforeEach
- Fix root causes rather than using workarounds
- for playwright and vitest, do not have inline timeouts for element detection or test completion, they should work in default global timeouts, in rare case have inline timeouts where global timeout will not work and is justified as special case
- for playwright, we do blackbox testing, the features of the system we test should be accessible via features/controls accessible to user. do not inject scripts to evaluate internal state of system, or monitor console logs to test, these are brittle tests 
- in case blackbox testing is not possible, suggest app changes by adding features, or debug build only ui controls, that makes it possible
- DO NOT MARK THE TEST AS SKIP IF YOU ARE NOT ABLE TO FIX, KEEP IT FAILING, AND END THE TASK WITH FAILING TO HAVE ALL TEST PASS

### Extension Testing Specifics
- Test extension loading and installation
- Test message passing between scripts (background ↔ content ↔ inject)
- Test API integration with mock servers
- Test UI components in extension context
- Test cross-browser compatibility when possible
- For playwright or vitest, do not have inline timeouts, test should work with the global timeouts configured in framework config files


### Test File Organization
- Tests are co-located in component `tests/` directories
- Use `.test.ts` or `.test.tsx` extensions
- Browser automation tests use Puppeteer with real extension loading
- Always follow up with writing or updating existing tests for the given code

## Verifying Task Completion

### For Extension Changes (`bodhi-browser-ext/`)
1. Run `make lint-fix` to fix any linting issues
2. Run `make ext` to build and test the extension
3. Optionally test with other browsers: `make ext-test-brave`

### For Library Changes (`bodhi-js/`)
1. Run `make lint-fix` to fix any linting issues  
2. Run `make bodhi-js` to build and test the library
3. Run integration tests if API changes were made

### For Project-wide Changes
1. Run `make lint-fix` to fix linting across all components
2. Run `make` (default target) to build and test everything
3. Ensure all tests pass before completion

### Build Verification
- Extension builds should produce valid Chrome extension files
- Library builds should produce all distribution formats (CJS, ESM, UMD)
- All tests should pass, including browser automation tests

## Documentation-Driven Implementation

For specific implementation patterns and detailed guidelines, always reference the appropriate ai-docs files based on the context of your work:
- All the files in the ai-docs folder are primarily prepare for consumption by AI coding assistants, and NOT human developers
- So when adding/editing files in ai-docs folder, DO NOT include generally known concepts on technology like React hooks, javascript async etc. These well known technologies and their details are well understood by ai coding assistants, and does not need to be reminded to them
- What is not known by AI coding assistants is our project specific concepts, that is what is the project domain, architecture, components, conventions, development guidelines etc.
- So include these project specific details and ignore any general framework or library details
- When including code snippets, do not include large snippets of code, instead include the core concept as code, and refer to the file from which the code snippet was taken, this helps use the ai coding assistants context window more efficiently
- Our AI coding assistant is agentic, that is if we have references to other files, it is going to take subsequent action to read those referred files, so use this ability of ai coding assistant by having references to other docs or code file for efficient information packing
- Whenever adding a new file, add an entry into its folders README.md at appropriate place, and also to the root ai-docs/README.md file as global index of documents in this folder
- Be very vigilant in adding information, and do not add duplicate information
- Be very vigilant in adding information, and add it to the right folder/file after thoroughly checking the ai-docs/README.md and ai-docs/{folder}/README.md

### Architecture and Design
- **`ai-docs/architecture.md`** - System architecture and component relationships
- **`ai-docs/domain.md`** - Problem domain and high-level design

### Component-Specific Documentation  
- **`ai-docs/bodhi-browser-ext.md`** - Chrome extension implementation details, message passing, and security
- **`ai-docs/bodhijs.md`** - JavaScript library API, usage patterns, and integration
- **`ai-docs/files.md`** - Project file structure and organization

### Feature Implementation
- **`ai-docs/feature-*.md`** - Specific feature implementation guides and requirements
- **`ai-docs/extension-message-format.md`** - Message format specifications for extension communication

### Development Process
- **`ai-docs/linting.md`** - Code quality and linting standards
- **`ai-docs/mock-llm-server.md`** - Testing server setup and usage

**Always check the relevant ai-docs file before implementing new features to ensure consistency with established patterns and conventions.**
