---
description: 
globs: 
alwaysApply: true
---
# Bodhi Browser Project Rules

## Project Structure and Components

- This project consists of three main components:
  1. **bodhi-browser-ext**: Chrome extension that bridges web pages and the local Bodhi App server
  2. **bodhi-js**: JavaScript/TypeScript library (@bodhiapp/bodhijs) that provides an interface for web pages to communicate with the extension
  3. **integration-tests**: Tests that validate the extension and library work correctly together
- The project uses TypeScript for type safety in the library and JavaScript for the extension
- The ai-docs folder contains documentation about each component and architecture

## Documentation Resources

- Reference ai-docs/domain.md for understanding the problem domain and component relationships
- Reference ai-docs/architecture.md for system design and communication flows
- Reference ai-docs/bodhi-browser-ext.md for Chrome extension implementation details
- Reference ai-docs/bodhijs.md for library API and usage patterns
- Reference ai-docs/files.md for project file structure information

## Code Modification Guidelines

- Do not start a task, till the condition of task as success and complete is not specified
- The condition of success and complete is specified as the makefile targets that you have to execute, or any other build target you have to execute
- Generally, the run the lint, build, test and package the project
- In general, there is a tasks corresponding to the project name that does all of the above for that project. for e.g. for bodhi-js, `make bodhi-js` is going to lint, build, test and package the project bodhi-js
- Also at repo level, there is a task that builds all the projects in repo, this is usually the default task. for e.g. `make` is going to build all the projects in this repo
- the tasks `make lint-fix` fixes any lint errors in the project. if you get a lint related failure, run this task, and run the original task to move ahead.
- If not specified, suggest the recommended task from the above convention. for e.g. if user asks to make changes in bodhi-js project, then ask if you have to run `make lint-fix`, `make bodhi-js`, `make` in that order and move to next target unless the previous is finished
- Only modify files that are explicitly specified by the user
- Do not suggest changes to other files unless specifically requested
- When adding or modifying features, maintain consistent patterns with existing code
- Respect 2-space indentation throughout the codebase
- There is no production release for this project, so do not worry about legacy code or backwards compatability


## Testing Philosophy

- Prefer integration testing over heavily mocked unit tests
- If mocking becomes tedious or complex, suggest an integration test approach instead
- Tests should provide actual value; avoid tests that are purely for coverage
- Each component should have tests appropriate to its functionality:
  - bodhi-browser-ext: Test communication between scripts
  - bodhijs: Test API functionality and error handling
  - integration-tests: End-to-end tests with real browser interactions

## Communication with User

- Stay focused on the specific goal provided by the user
- Ask clarifying questions rather than making assumptions
- After suggesting modifications to 5 or more files, return to the user for guidance
- Perform thorough analysis before suggesting changes
- If the solution spans multiple files, explain the approach holistically first
- if not specified, ask use for the test or command to run to verify the task is completed succesfully, generally a test that passes

## Code Style

- Use clear, descriptive variable and function names
- Add meaningful comments for complex logic
- Maintain consistency with existing patterns in each module
- Follow TypeScript best practices in the library code
- Avoid unnecessary abstractions or premature optimizations

## Development Workflow

- The extension is loaded unpacked in Chrome during development
- bodhijs library is built with Rollup and published to npm
- Integration tests use Puppeteer to test cross-component functionality
- Follow each component's own build process when making changes

## Definition of Complete
- After every task,