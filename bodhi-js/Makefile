# bodhi-js Makefile
# This Makefile provides commands to build and test the bodhi-js library

.PHONY: all clean build test setup install lint format build-all validate release help

# Default target - builds and tests everything
all: setup lint-fix build test ## Default target, builds and tests everything

# Clean all build artifacts
clean: ## Clean all build artifacts
	npm run clean
	@echo "bodhi-js build artifacts cleaned"

# Setup - install dependencies
install: ## Install dependencies (using npm install)
	@echo "Installing dependencies for bodhi-js..."
	npm install
	@echo "Installing dependencies for test-app..."
	cd tests/test-app-react && npm install
	@echo "dependencies installed successfully"

# Setup - install dependencies with exact versions
setup: ## Install dependencies with exact versions (using npm ci)
	@echo "Installing dependencies for bodhi-js..."
	npm ci
	@echo "Installing dependencies for test-app..."
	cd tests/test-app-react && npm ci || npm install
	@echo "dependencies installed successfully"

# Build setup-modal as prerequisite
build-modal: ## Build setup-modal dependency
	@echo "Building setup-modal dependency..."
	cd ../setup-modal && npm run build:fast
	@echo "setup-modal built successfully"

# Build the library
build: build-modal ## Build bodhi-js library
	npm run build
	@echo "bodhi-js built successfully"

# Run tests
test: build ## Test bodhi-js library
	npm test
	@echo "bodhi-js tests completed successfully"

# Build and serve test app with all dependencies
bodhijs-test-app-serve: ## Build setup-modal, bodhi-js, and serve test app
	@echo "Building setup-modal in dev mode..."
	cd ../setup-modal && NODE_ENV=development npm run build:fast
	@echo "Building bodhi-js..."
	npm run clean && npm run build
	@echo "Starting test app server..."
	cd tests/bodhijs-test-app-react	&& npm run clean && npm run serve

# Start test server with configurable options
test-app-server: ## Start Bodhi test server (use ARGS="...": make test-app-server ARGS="--app-status resource-admin --port 51000")
	npx tsx scripts/start-test-server.ts $(ARGS)

# Run ESLint checks
lint: ## Run ESLint checks
	@echo "Running ESLint checks..."
	npm run lint
	@echo "ESLint checks completed"

# Fix ESLint issues automatically where possible
lint-fix: ## Fix ESLint and formatting issues automatically
	@echo "Fixing ESLint and formatting issues..."
	npm run lint:fix
	@echo "ESLint and formatting fixes completed"

# Validate code (format and lint)
validate: ## Run validation (format and lint)
	@echo "Validating bodhi-js..."
	npm run lint
	@echo "bodhi-js validation completed successfully"

# Function to check git branch status
define check_git_branch
	@CURRENT_BRANCH=$$(git branch --show-current) && \
	if [ "$$CURRENT_BRANCH" != "main" ]; then \
		read -p "Warning: You are not on main branch (current: $$CURRENT_BRANCH). Continue? [y/N] " confirm && \
		if [ "$$confirm" != "y" ]; then \
			echo "Aborting release." && exit 1; \
		fi \
	fi && \
	echo "Fetching latest changes from remote..." && \
	git fetch origin main && \
	LOCAL_HEAD=$$(git rev-parse HEAD) && \
	REMOTE_HEAD=$$(git rev-parse origin/main) && \
	if [ "$$LOCAL_HEAD" != "$$REMOTE_HEAD" ]; then \
		echo "Warning: Your local main branch is different from origin/main" && \
		echo "Local:  $$LOCAL_HEAD" && \
		echo "Remote: $$REMOTE_HEAD" && \
		read -p "Continue anyway? [y/N] " confirm && \
		if [ "$$confirm" != "y" ]; then \
			echo "Aborting release." && exit 1; \
		fi \
	fi
endef

# Function to safely delete existing tag
define delete_tag_if_exists
	echo "Checking for existing tag $(1)..." && \
	if git rev-parse "$(1)" >/dev/null 2>&1; then \
		read -p "Tag $(1) already exists. Delete and recreate? [y/N] " confirm && \
		if [ "$$confirm" = "y" ]; then \
			echo "Deleting existing tag $(1)..." && \
			git tag -d "$(1)" 2>/dev/null || true && \
			git push --delete origin "$(1)" 2>/dev/null || true; \
		else \
			echo "Aborting release." && exit 1; \
		fi \
	fi
endef

# Release bodhi-js package
release: ## Create and push tag for bodhi-js package release
	@echo "Preparing to release bodhi-js package..."
	$(call check_git_branch)
	@CURRENT_VERSION=$$(npm view @bodhiapp/bodhijs version 2>/dev/null || echo "0.0.0") && \
	IFS='.' read -r MAJOR MINOR PATCH <<< "$$CURRENT_VERSION" && \
	NEXT_VERSION="$$MAJOR.$$MINOR.$$((PATCH + 1))" && \
	echo "Current version on npmjs: $$CURRENT_VERSION" && \
	echo "Next version to release: $$NEXT_VERSION" && \
	TAG_NAME="bodhi-js/v$$NEXT_VERSION" && \
	$(call delete_tag_if_exists,$$TAG_NAME) && \
	echo "Creating tag $$TAG_NAME..." && \
	git tag "$$TAG_NAME" && \
	git push origin "$$TAG_NAME" && \
	echo "Tag $$TAG_NAME pushed. GitHub workflow will handle the release process."

.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z0-9._-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) 