{"name": "@bodhiapp/bodhijs", "version": "0.0.5-dev", "description": "A JavaScript library for interacting with local LLM capabilities via Bodhi Browser Extension", "type": "module", "main": "dist/bodhi.cjs.js", "module": "dist/bodhi.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"clean": "rimraf dist && rimraf src/onboarding/modal.html && rimraf src/onboarding/types.ts", "copy-modal": "node scripts/copy-modal.js", "build": "npm run copy-modal && rollup -c", "dev": "rollup -c -w", "test": "vitest run", "prepublishOnly": "npm run build", "pretest": "npm run build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "validate": "npm run lint && npm run test"}, "keywords": ["llm", "ai", "browser-extension", "b<PERSON><PERSON><PERSON><PERSON>"], "author": "Bodhi Search", "homepage": "https://github.com/BodhiSearch/bodhi-browser", "dependencies": {"@bodhiapp/ts-client": "^0.1.5", "ua-parser-js": "^1.0.40"}, "devDependencies": {"@bodhiapp/app-bindings": "^0.0.15", "@eslint/js": "^9.23.0", "@playwright/test": "^1.54.2", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@types/fs-extra": "^11.0.4", "@types/node": "^20.19.9", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "@vitest/browser": "^3.0.8", "dotenv": "^17.1.0", "eslint": "^9.22.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-html": "^8.1.2", "eslint-plugin-prettier": "^5.1.3", "fs-extra": "^11.2.0", "globals": "^16.0.0", "happy-dom": "^17.4.4", "playwright": "^1.54.0", "prettier": "^3.1.1", "rimraf": "^6.0.1", "rollup": "^3.29.4", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-string": "^3.0.0", "serve": "^14.2.1", "ts-node": "^10.9.2", "tslib": "^2.6.2", "typescript": "^5.8.3", "vitest": "^3.0.8"}}