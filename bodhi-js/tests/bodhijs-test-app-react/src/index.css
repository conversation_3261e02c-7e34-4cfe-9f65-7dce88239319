:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: white;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
}

.extension-status {
  margin: 2rem 0;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #333;
}

.extension-status.detecting {
  background-color: #1a1a2e;
  border-color: #646cff;
}

.extension-status.detected {
  background-color: #1a2e1a;
  border-color: #4caf50;
}

.extension-status.error {
  background-color: #2e1a1a;
  border-color: #f44336;
}

.extension-status.timeout {
  background-color: #2e2e1a;
  border-color: #ff9800;
}

.status-title {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.status-message {
  font-size: 0.9em;
  opacity: 0.8;
}

.extension-id {
  font-family: monospace;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  margin: 0.5rem 0;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
    color: #213547;
  }

  .extension-status {
    border-color: #ccc;
  }

  .extension-status.detecting {
    background-color: #e8f4fd;
    border-color: #2196f3;
  }

  .extension-status.detected {
    background-color: #e8f5e8;
    border-color: #4caf50;
  }

  .extension-status.error {
    background-color: #fde8e8;
    border-color: #f44336;
  }

  .extension-status.timeout {
    background-color: #fff8e1;
    border-color: #ff9800;
  }

  .extension-id {
    background-color: rgba(0, 0, 0, 0.1);
  }
}
