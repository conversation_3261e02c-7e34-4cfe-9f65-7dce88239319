import { BodhiPlatform, BodhiPlatformState } from '@bodhiapp/bodhijs';

interface PlatformStatusSectionProps {
  platform: BodhiPlatform | null;
  platformState: BodhiPlatformState | null;
  isInitializing: boolean;
  onRetryInitialization: () => void;
  onShowOnboarding: () => void;
}

export function PlatformStatusSection({
  platform,
  platformState,
  isInitializing,
  onRetryInitialization,
  onShowOnboarding,
}: PlatformStatusSectionProps) {
  const isReady = platformState?.isReady() || false;

  return (
    <div
      style={{
        margin: '20px 0',
        padding: '20px',
        border: '1px solid #ddd',
        borderRadius: '8px',
        backgroundColor: '#ffffff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      }}
    >
      <h3 style={{ color: '#333333', marginBottom: '16px' }}>
        BodhiPlatform Status
      </h3>

      <div style={{ marginBottom: '16px' }}>
        {isInitializing ? (
          <p style={{ color: '#007bff', margin: '8px 0' }}>
            <strong>Status:</strong> 🔍 Initializing platform...
          </p>
        ) : !platform ? (
          <p style={{ color: '#dc3545', margin: '8px 0' }}>
            <strong>Status:</strong> ❌ Platform not initialized
          </p>
        ) : isReady ? (
          <p style={{ color: '#28a745', margin: '8px 0' }}>
            <strong>Status:</strong> ✅ BodhiPlatform is ready
          </p>
        ) : (
          <div>
            <p style={{ color: '#ffc107', margin: '8px 0' }}>
              <strong>Status:</strong> ⚠️ BodhiPlatform is not setup
            </p>
            <p style={{ color: '#6c757d', fontSize: '14px', margin: '8px 0' }}>
              The Bodhi browser extension or server needs to be installed and configured.
            </p>
          </div>
        )}
      </div>

      <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
        <button
          onClick={onRetryInitialization}
          disabled={isInitializing}
          style={{
            padding: '10px 16px',
            backgroundColor: isInitializing ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isInitializing ? 'not-allowed' : 'pointer',
            fontSize: '14px',
          }}
        >
          {isInitializing ? 'Initializing...' : 'Retry Initialization'}
        </button>

        {platform && !isReady && (
          <button
            data-testid="show-onboarding-modal"
            onClick={onShowOnboarding}
            style={{
              padding: '10px 16px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
            }}
          >
            Open Setup Modal
          </button>
        )}
      </div>

      {!platform && !isInitializing && (
        <p style={{ color: '#6c757d', fontSize: '14px', marginTop: '12px' }}>
          Failed to initialize BodhiPlatform. Please ensure the browser extension is installed.
        </p>
      )}
    </div>
  );
}