import { PlatformDetectionState } from '@/hooks/usePlatformDetection';
import { AuthenticationState } from '@/hooks/useAuthentication';

interface AuthenticationSectionProps {
  platformState: PlatformDetectionState;
  authState: AuthenticationState;
  onNavigateToApiTest: () => void;
}

export function AuthenticationSection({
  platformState,
  authState,
  onNavigateToApiTest,
}: AuthenticationSectionProps) {
  // Only show authentication section when extension is available
  const showAuthSection = platformState.status === 'detected' || platformState.status === 'setup';

  if (!showAuthSection) {
    return null;
  }

  const getAuthStatusMessage = () => {
    switch (authState.status) {
      case 'unauthenticated':
        return 'Not authenticated';
      case 'authenticating':
        return 'Authenticating...';
      case 'authenticated':
        return 'Authenticated';
      case 'error':
        return 'Authentication error';
      default:
        return 'Unknown status';
    }
  };

  const getAuthStatusClassName = () => `auth-status ${authState.status}`;

  return (
    <>
      {/* Authentication Status */}
      <div className={getAuthStatusClassName()} style={{ marginTop: '1rem' }}>
        <div className="status-title">Authentication Status</div>
        <div className="status-message">{getAuthStatusMessage()}</div>
      </div>

      {/* Authentication Error Display */}
      {authState.error && (
        <div
          className="error-message"
          style={{
            marginTop: '1rem',
            padding: '1rem',
            backgroundColor: '#ffe6e6',
            border: '1px solid #ff6b6b',
            borderRadius: '4px',
            color: '#d63031',
          }}
        >
          <strong>Error:</strong> {authState.error}
        </div>
      )}

      {/* OAuth Authentication Controls */}
      <div style={{ marginTop: '2rem' }}>
        {authState.status === 'unauthenticated' || authState.status === 'error' ? (
          <LoginSection onLogin={authState.login} />
        ) : authState.status === 'authenticated' && authState.userInfo ? (
          <AuthenticatedSection
            userInfo={authState.userInfo}
            onLogout={authState.logout}
            onNavigateToApiTest={onNavigateToApiTest}
          />
        ) : authState.status === 'authenticating' ? (
          <AuthenticatingSection />
        ) : (
          <ExtensionReadySection />
        )}
      </div>

      {/* OAuth Instructions */}
      {authState.status === 'unauthenticated' && <OAuthInstructions />}
    </>
  );
}

function LoginSection({ onLogin }: { onLogin: () => Promise<void> }) {
  return (
    <div>
      <h2>Login Required</h2>
      <p>Please log in to access the API testing features.</p>
      <button
        onClick={onLogin}
        style={{
          padding: '0.75rem 1.5rem',
          fontSize: '1rem',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
        }}
      >
        Login with OAuth
      </button>
    </div>
  );
}

function AuthenticatedSection({
  userInfo,
  onLogout,
  onNavigateToApiTest,
}: {
  userInfo: any;
  onLogout: () => void;
  onNavigateToApiTest: () => void;
}) {
  return (
    <div>
      <h2>Welcome, {userInfo.email}!</h2>
      <div
        style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#e6ffe6',
          border: '1px solid #28a745',
          borderRadius: '4px',
        }}
      >
        <h3>User Information</h3>
        <p>
          <strong>Email:</strong> {userInfo.email}
        </p>
        <p>
          <strong>Role:</strong> {userInfo.role}
        </p>
        <p>
          <strong>Token Type:</strong> {userInfo.tokenType}
        </p>
        <p>
          <strong>Status:</strong> {userInfo.loggedIn ? 'Logged In' : 'Logged Out'}
        </p>
      </div>

      <div style={{ marginTop: '1.5rem' }}>
        <button
          onClick={onNavigateToApiTest}
          style={{
            padding: '0.75rem 1.5rem',
            fontSize: '1rem',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '1rem',
          }}
        >
          Go to API Test
        </button>

        <button
          onClick={onLogout}
          style={{
            padding: '0.75rem 1.5rem',
            fontSize: '1rem',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Logout
        </button>
      </div>
    </div>
  );
}

function AuthenticatingSection() {
  return (
    <div>
      <h2>Authenticating...</h2>
      <p>Please wait while we process your authentication.</p>
      <div
        style={{
          padding: '1rem',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffc107',
          borderRadius: '4px',
        }}
      >
        Processing OAuth flow...
      </div>
    </div>
  );
}

function ExtensionReadySection() {
  return (
    <div>
      <h2>Extension Ready</h2>
      <p>The Bodhi browser extension has been successfully detected and is ready for use.</p>
      <p>Available features:</p>
      <ul style={{ textAlign: 'left', display: 'inline-block' }}>
        <li>OAuth authentication flow</li>
        <li>API testing interface</li>
        <li>Streaming chat completions</li>
      </ul>
    </div>
  );
}

function OAuthInstructions() {
  return (
    <div style={{ marginTop: '2rem', fontSize: '0.9rem', color: '#6c757d' }}>
      <h3>How OAuth Flow Works</h3>
      <ol style={{ textAlign: 'left', display: 'inline-block' }}>
        <li>Click "Login with OAuth" to start the authentication process</li>
        <li>You'll be redirected to the Bodhi authentication server</li>
        <li>Enter your credentials and authorize the application</li>
        <li>You'll be redirected back to this application</li>
        <li>The application will exchange the authorization code for an access token</li>
        <li>You'll be able to access authenticated API features</li>
      </ol>
    </div>
  );
}
