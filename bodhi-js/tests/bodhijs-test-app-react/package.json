{"name": "bodhijs-test-app-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "dev": "vite", "build": "tsc && vite build", "build:fast": "node ../../../scripts/build-fast.mjs . 'npm run build' src index.html package.json tsconfig.json vite.config.ts", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "serve": "NODE_ENV=development npm run build:fast && npx serve -d dist -l 12345"}, "dependencies": {"@bodhiapp/bodhijs": "file:../../", "react": "19.1.1", "react-dom": "19.1.1", "react-router-dom": "7.7.1"}, "devDependencies": {"@types/react": "19.1.1", "@types/react-dom": "19.1.1", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "@vitejs/plugin-react": "^4.7.0", "eslint": "9.32.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "^0.4.17", "rimraf": "^6.0.1", "typescript": "5.8.3", "vite": "7.0.6"}}