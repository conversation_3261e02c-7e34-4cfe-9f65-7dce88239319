import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BodhiAppServerManager, TestAppManager } from './test-utils/index.js';

declare global {
  var TEST_APP_URL: string;
}

describe('Test Infrastructure', () => {
  let browserManager: BrowserManager;

  beforeAll(async () => {
    browserManager = BrowserManager.default(null);
    await browserManager.start();
  });

  afterAll(async () => {
    if (browserManager?.isRunning()) {
      await browserManager.stop();
    }
  });

  it('should validate global test environment is properly configured', () => {
    // Verify global test app URL is available
    expect(globalThis.TEST_APP_URL).toBeDefined();
    expect(globalThis.TEST_APP_URL).toMatch(/^http:\/\/localhost:12345$/);

    // Verify required environment variables are available
    expect(globalThis.INTEG_TEST_AUTH_URL).toBeDefined();
    expect(globalThis.INTEG_TEST_AUTH_REALM).toBeDefined();
    expect(globalThis.INTEG_TEST_CLIENT_ID).toBeDefined();
    expect(globalThis.INTEG_TEST_CLIENT_SECRET).toBeDefined();
    expect(globalThis.INTEG_TEST_USERNAME).toBeDefined();
    expect(globalThis.INTEG_TEST_PASSWORD).toBeDefined();
    expect(globalThis.INTEG_TEST_APP_CLIENT_ID).toBeDefined();

    // Verify they're not empty strings
    expect(globalThis.INTEG_TEST_AUTH_URL).not.toBe('');
    expect(globalThis.INTEG_TEST_AUTH_REALM).not.toBe('');
    expect(globalThis.INTEG_TEST_CLIENT_ID).not.toBe('');
    expect(globalThis.INTEG_TEST_CLIENT_SECRET).not.toBe('');
    expect(globalThis.INTEG_TEST_USERNAME).not.toBe('');
    expect(globalThis.INTEG_TEST_PASSWORD).not.toBe('');
    expect(globalThis.INTEG_TEST_APP_CLIENT_ID).not.toBe('');
  });

  it('should verify test app is running and accessible', async () => {
    // Test that the global test app is accessible and serves the React app
    const response = await fetch(globalThis.TEST_APP_URL);
    expect(response.ok).toBe(true);
    expect(response.status).toBe(200);

    const html = await response.text();
    expect(html).toContain('<title>Bodhi JS Test App</title>');
    expect(html).toContain('<div id="root">');
  });

  it('should verify browser manager is running with extension loaded', async () => {
    expect(browserManager.isRunning()).toBe(true);

    // Create a test page to verify browser is working
    const page = await browserManager.createPage();
    expect(page).toBeDefined();

    await page.close();
  });

  it('should verify test utility managers can be instantiated with proper configuration', () => {
    // Test that TestAppManager enforces port 12345
    const testAppManager = new TestAppManager({
      appPath: '/test/path',
    });
    expect(testAppManager.getPort()).toBe(12345);
    expect(testAppManager.getAppUrl()).toBe('http://localhost:12345');

    // Test that BodhiAppServerManager can be created with global test constants
    const serverManager = new BodhiAppServerManager({
      authUrl: globalThis.INTEG_TEST_AUTH_URL,
      authRealm: globalThis.INTEG_TEST_AUTH_REALM,
      clientId: globalThis.INTEG_TEST_CLIENT_ID,
      clientSecret: globalThis.INTEG_TEST_CLIENT_SECRET,
      appClientId: globalThis.INTEG_TEST_APP_CLIENT_ID,
      appClientSecret: globalThis.INTEG_TEST_APP_CLIENT_SECRET,
    });
    expect(serverManager).toBeDefined();
  });
});
