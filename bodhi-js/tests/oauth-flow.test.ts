import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { expect as playwrightExpect } from '@playwright/test';
import { Page } from 'playwright';
import { BrowserManager, BodhiAppServerManager } from './test-utils';
import type { BodhiAppServerConfig } from './test-utils';

declare global {
  var INTEG_TEST_AUTH_URL: string;
  var INTEG_TEST_AUTH_REALM: string;
  var INTEG_TEST_CLIENT_ID: string;
  var INTEG_TEST_CLIENT_SECRET: string;
  var INTEG_TEST_USERNAME: string;
  var INTEG_TEST_PASSWORD: string;
  var INTEG_TEST_APP_CLIENT_ID: string;
  var TEST_APP_URL: string;
}

describe('OAuth Flow Integration Tests', () => {
  let browserManager: BrowserManager;
  let serverManager: BodhiAppServerManager;
  let serverUrl: string;

  beforeAll(async () => {
    const serverConfig: BodhiAppServerConfig = {
      authUrl: globalThis.INTEG_TEST_AUTH_URL,
      authRealm: globalThis.INTEG_TEST_AUTH_REALM,
      clientId: globalThis.INTEG_TEST_CLIENT_ID,
      clientSecret: globalThis.INTEG_TEST_CLIENT_SECRET,
      appClientId: globalThis.INTEG_TEST_APP_CLIENT_ID,
    };

    serverManager = new BodhiAppServerManager(serverConfig);
    serverUrl = await serverManager.start();

    // Start browser with extension
    browserManager = BrowserManager.default(serverUrl);
    await browserManager.start();
  });

  afterAll(async () => {
    if (browserManager) {
      await browserManager.stop();
    }
    if (serverManager) {
      await serverManager.stop();
    }
  });

  describe('OAuth Authentication Flow', () => {
    let page: Page;

    beforeAll(async () => {
      page = await browserManager.createPage();
    });

    afterAll(async () => {
      if (page) {
        await page.close();
      }
    });

    it('should complete full OAuth login flow and authenticate user', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Verify extension is detected
      await page.waitForSelector('.extension-status.detected');
      const statusTitle = await page.textContent('.status-title');
      expect(statusTitle).toBe('Platform Ready');

      // Verify we start in unauthenticated state
      await page.waitForSelector('.auth-status.unauthenticated');
      const authStatusMessage = await page.textContent('.auth-status .status-message');
      expect(authStatusMessage).toBe('Not authenticated');

      // Click login button to start OAuth flow
      const loginButton = page.locator('button:has-text("Login with OAuth")');
      await playwrightExpect(loginButton).toBeVisible();
      await loginButton.click();

      // Wait for redirect to OAuth server (Keycloak)
      await page.waitForURL(url => url.toString().includes(globalThis.INTEG_TEST_AUTH_URL));

      // Fill in credentials on OAuth server
      const usernameField = page.locator('input[name="username"], input[type="email"], #username');
      const passwordField = page.locator(
        'input[name="password"], input[type="password"], #password'
      );
      const submitButton = page.locator(
        'button[type="submit"], input[type="submit"], button:has-text("Sign In")'
      );

      await playwrightExpect(usernameField).toBeVisible();
      await playwrightExpect(passwordField).toBeVisible();

      await usernameField.fill(globalThis.INTEG_TEST_USERNAME);
      await passwordField.fill(globalThis.INTEG_TEST_PASSWORD);
      await submitButton.click();

      await page.waitForURL(url => url.pathname === '/');

      // Verify we're now in authenticated state
      await page.waitForSelector('.auth-status.authenticated');
      const authenticatedStatusMessage = await page.textContent('.auth-status .status-message');
      expect(authenticatedStatusMessage).toBe('Authenticated');

      // Verify user information is displayed
      const welcomeMessage = page.locator('h2:has-text("Welcome,")');
      await playwrightExpect(welcomeMessage).toBeVisible();

      // Verify user info section is present
      const userInfoSection = page.locator('h3:has-text("User Information")');
      await playwrightExpect(userInfoSection).toBeVisible();

      // Verify email is displayed
      const emailInfo = page.locator('p:has-text("Email:")');
      await playwrightExpect(emailInfo).toBeVisible();
      const emailText = await emailInfo.textContent();
      expect(emailText).toContain(globalThis.INTEG_TEST_USERNAME);

      // Verify token type is displayed
      const tokenTypeInfo = page.locator('p:has-text("Token Type:")');
      await playwrightExpect(tokenTypeInfo).toBeVisible();
      const tokenTypeText = await tokenTypeInfo.textContent();
      expect(tokenTypeText).toContain('Bearer');

      // Token Management: Check that tokens are stored in localStorage
      const hasAccessToken = await page.evaluate(() => {
        return !!localStorage.getItem('bodhi_access_token');
      });
      expect(hasAccessToken).toBe(true);

      // Check that user info is stored
      const hasUserInfo = await page.evaluate(() => {
        return !!localStorage.getItem('bodhi_user_info');
      });
      expect(hasUserInfo).toBe(true);

      // Verify user info structure
      const userInfo = await page.evaluate(() => {
        const userInfoStr = localStorage.getItem('bodhi_user_info');
        return userInfoStr ? JSON.parse(userInfoStr) : null;
      });

      expect(userInfo).toBeTruthy();
      expect(userInfo.email).toBe(globalThis.INTEG_TEST_USERNAME);
      expect(userInfo.tokenType).toBe('Bearer');
      expect(userInfo.loggedIn).toBe(true);
    });

    it('should handle logout functionality correctly', async () => {
      // Navigate to test app
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Verify we're in authenticated state (from previous test)
      await page.waitForSelector('.auth-status.authenticated');

      // Click logout button
      const logoutButton = page.locator('button:has-text("Logout")');
      await playwrightExpect(logoutButton).toBeVisible();
      await logoutButton.click();

      // Verify we're back to unauthenticated state
      await page.waitForSelector('.auth-status.unauthenticated');
      const authStatusMessage = await page.textContent('.auth-status .status-message');
      expect(authStatusMessage).toBe('Not authenticated');

      // Verify login button is available again
      const loginButton = page.locator('button:has-text("Login with OAuth")');
      await playwrightExpect(loginButton).toBeVisible();

      // Verify user info section is no longer visible
      const userInfoSection = page.locator('h3:has-text("User Information")');
      await playwrightExpect(userInfoSection).not.toBeVisible();

      // Token Management: Verify tokens are cleared after logout
      const hasAccessTokenAfterLogout = await page.evaluate(() => {
        return !!localStorage.getItem('bodhi_access_token');
      });
      expect(hasAccessTokenAfterLogout).toBe(false);

      const hasUserInfoAfterLogout = await page.evaluate(() => {
        return !!localStorage.getItem('bodhi_user_info');
      });
      expect(hasUserInfoAfterLogout).toBe(false);
    });
  });

  describe('OAuth Error Handling', () => {
    let page: Page;

    beforeAll(async () => {
      page = await browserManager.createPage();
    });

    afterAll(async () => {
      if (page) {
        await page.close();
      }
    });

    it('should handle callback with missing authorization code', async () => {
      // Navigate directly to callback page without proper OAuth flow
      await page.goto(`${globalThis.TEST_APP_URL}/callback`);
      await page.waitForLoadState('networkidle');

      // Wait for error state in callback processing
      await page.waitForSelector('.status-message:has-text("Authorization code not found")');

      // Verify error is displayed
      const errorSection = page.locator('div:has-text("✗ Authentication Failed")').first();
      await playwrightExpect(errorSection).toBeVisible();

      // Click return to home button
      const returnHomeButton = page.locator('button:has-text("Return to Home")');
      await playwrightExpect(returnHomeButton).toBeVisible();
      await returnHomeButton.click();

      // Verify we're back on landing page in unauthenticated state
      await page.waitForURL(url => url.pathname === '/');
      await page.waitForSelector('.auth-status.unauthenticated');
    });

    it('should handle OAuth error parameters in callback', async () => {
      // Navigate to callback with OAuth error parameters
      const errorParams = new URLSearchParams({
        error: 'access_denied',
        error_description: 'User denied access to the application',
        state: 'test_state',
      });
      await page.goto(`${globalThis.TEST_APP_URL}/callback?${errorParams}`);
      await page.waitForLoadState('networkidle');
      await page.waitForSelector('.status-message');
      const errorMessage = await page.locator('.status-message').textContent();
      expect(errorMessage).toContain('User denied access to the application');
      const returnHomeButton = page.locator('button:has-text("Return to Home")');
      await returnHomeButton.click();
      await page.waitForURL(url => url.pathname === '/');
      await page.waitForSelector('.auth-status.unauthenticated');
    });
  });
});
