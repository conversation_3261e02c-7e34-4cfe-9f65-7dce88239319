import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { expect as playwrightExpect } from '@playwright/test';
import { Page } from 'playwright';
import { BrowserManager } from './test-utils';
import type { BrowserManagerConfig } from './test-utils';

declare global {
  var TEST_APP_URL: string;
}

describe('Extension Detection without Extension Installed', () => {
  let browserManager: BrowserManager;

  beforeAll(async () => {
    const browserConfig: BrowserManagerConfig = {
      extensionPath: null,
    };

    browserManager = new BrowserManager(browserConfig);
    await browserManager.start();
  });

  afterAll(async () => {
    if (browserManager) {
      await browserManager.stop();
    }
  });

  describe('Extension Detection Error Scenarios', () => {
    let page: Page;

    beforeAll(async () => {
      page = await browserManager.createPage();
      page.setDefaultTimeout(15000); // Increase timeout for error scenarios
    });

    afterAll(async () => {
      if (page) {
        await page.close();
      }
    });

    it('should handle extension not found error, show proper messages, and allow retry', async () => {
      // Ensure we have the global test app URL
      if (!globalThis.TEST_APP_URL) {
        throw new Error('Global test app URL is not available. Global setup may have failed.');
      }

      // Navigate to test app landing page
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Verify page loaded correctly
      const pageTitle = await page.title();
      expect(pageTitle).toBe('Bodhi JS Test App');

      // Check that window.bodhiext is not available (no extension)
      const bodhiextAvailable = await page.evaluate(() => {
        return typeof (window as any).bodhiext !== 'undefined';
      });
      expect(bodhiextAvailable).toBe(false);

      // Wait for extension detection to fail (extension not installed)
      await page.waitForSelector('.extension-status.error');

      // Verify error status (extension not installed shows as error in UI)
      const statusTitle = await page.textContent('.status-title');
      expect(statusTitle).toBe('Platform Not Available');

      const statusMessage = await page.textContent('.status-message');
      expect(statusMessage).toBe('Platform not available');

      // Verify error message contains proper information
      const errorMessages = await page.locator('.status-message').allTextContents();
      const hasExtensionNotInstalledError = errorMessages.some(msg =>
        msg.includes('Extension not installed')
      );
      expect(hasExtensionNotInstalledError).toBe(true);

      // Verify proper error message for extension not installed
      const hasProperErrorMessage = errorMessages.some(
        msg =>
          msg.includes('Extension not installed') &&
          msg.includes('Please ensure the Bodhi browser extension is installed and enabled')
      );
      expect(hasProperErrorMessage).toBe(true);

      // Verify installation required section is shown
      const installSection = page.locator('h2:has-text("Installation Required")');
      await playwrightExpect(installSection).toBeVisible();

      // Verify retry button is available
      const retryButton = page.locator('button:has-text("Retry Detection")');
      await playwrightExpect(retryButton).toBeVisible();

      // Test retry functionality
      await retryButton.click();

      // Verify detection status changes to detecting
      await page.waitForSelector('.extension-status.detecting');
      const retryStatusTitle = await page.textContent('.status-title');
      expect(retryStatusTitle).toBe('Detecting Platform...');

      // Should eventually go back to error state
      await page.waitForSelector('.extension-status.error');

      // Verify we're back to error state
      const finalStatusTitle = await page.textContent('.status-title');
      expect(finalStatusTitle).toBe('Platform Not Available');

      // Test modal functionality for extension not installed scenario
      const modalButton = page.locator('[data-testid="show-onboarding-modal"]');
      expect(await modalButton.isVisible()).toBe(true);
      expect(await modalButton.isEnabled()).toBe(true);
      await modalButton.click();

      // Verify modal overlay appears with correct content
      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });
      expect(await modalOverlay.isVisible()).toBe(true);

      // Verify iframe content shows extension required scenario
      const iframe = page.locator('iframe');
      expect(await iframe.isVisible()).toBe(true);

      const iframeFrame = iframe.contentFrame();
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Extension Required');

      // Test modal dismissal methods
      expect(await iframeFrame.locator('[data-testid="modal-close"]').isVisible()).toBe(true);
      expect(await iframeFrame.locator('[data-testid="modal-check-again"]').isVisible()).toBe(true);

      // Test Close button
      await iframeFrame.locator('[data-testid="modal-close"]').click();
      await modalOverlay.waitFor({ state: 'hidden' });
      expect(await modalOverlay.isVisible()).toBe(false);

      // Test Escape key dismissal
      await modalButton.click();
      await modalOverlay.waitFor({ state: 'visible' });
      expect(await modalOverlay.isVisible()).toBe(true);
      await page.keyboard.press('Escape');
      await modalOverlay.waitFor({ state: 'hidden' });
      expect(await modalOverlay.isVisible()).toBe(false);
    });

    it('should display enhanced system information and detection results for extension not installed', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.error');

      // Test system information display
      const showSystemInfoButton = page.locator('button:has-text("Show System Details")');
      await showSystemInfoButton.click();

      // Verify browser information is displayed
      const browserInfo = page.locator('h4:has-text("Browser Information")');
      await playwrightExpect(browserInfo).toBeVisible();

      // Verify OS information is displayed
      const osInfo = page.locator('h4:has-text("Operating System Information")');
      await playwrightExpect(osInfo).toBeVisible();

      // Verify download URLs section
      const downloadUrls = page.locator('h4:has-text("Download URLs & GitHub Links")');
      await playwrightExpect(downloadUrls).toBeVisible();

      // Verify GitHub issue links are present
      const browserSupportLink = page.locator('a:has-text("Request Browser Support")');
      await playwrightExpect(browserSupportLink).toBeVisible();

      const osSupportLink = page.locator('a:has-text("Request OS Support")');
      await playwrightExpect(osSupportLink).toBeVisible();

      const bugReportLink = page.locator('a:has-text("Report Bug")');
      await playwrightExpect(bugReportLink).toBeVisible();
    });

    it('should test modal callback functionality for extension not installed', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.error');

      // Clear any existing callback logs
      const clearLogsButton = page.locator('button:has-text("Clear Logs")');
      await clearLogsButton.click();

      // Verify logs are cleared
      const callbackLogs = page.locator('[data-testid="callback-logs"]');
      const initialLogText = await callbackLogs.textContent();
      expect(initialLogText).toContain('No callback events yet...');

      // Show modal and verify callback logging
      const showModalButton = page.locator('[data-testid="show-onboarding-modal"]');
      await showModalButton.click();

      // Wait for modal to appear and check logs
      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      // Verify onboarding show callback was logged
      await page.waitForTimeout(500); // Give time for callback to be logged
      const showLogText = await callbackLogs.textContent();
      expect(showLogText).toContain('Showing onboarding modal');

      // Test dismiss callback
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const closeButton = iframeFrame.locator('[data-testid="modal-close"]');
      await closeButton.click();

      await modalOverlay.waitFor({ state: 'hidden' });

      // Verify dismiss callback was logged
      await page.waitForTimeout(500);
      const dismissLogText = await callbackLogs.textContent();
      expect(dismissLogText).toContain('Onboarding dismissed');
    });

    it('should test modal with different theme configurations for extension not installed', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.error');

      // Test Auto Theme Modal
      const autoThemeButton = page.locator('[data-testid="show-onboarding-modal"]');
      await autoThemeButton.click();

      let modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      let iframe = page.locator('iframe');
      let iframeFrame = iframe.contentFrame();

      // Verify modal content is displayed
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Extension Required');

      // Close modal
      const closeButton = iframeFrame.locator('[data-testid="modal-close"]');
      await closeButton.click();
      await modalOverlay.waitFor({ state: 'hidden' });

      // Test Light Theme Modal
      const lightThemeButton = page.locator('[data-testid="show-onboarding-modal-light"]');
      await lightThemeButton.click();

      modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      iframe = page.locator('iframe');
      iframeFrame = iframe.contentFrame();

      // Verify modal content is displayed
      const lightTitle = await iframeFrame.locator('h1').textContent();
      expect(lightTitle).toBe('Extension Required');

      await iframeFrame.locator('[data-testid="modal-close"]').click();
      await modalOverlay.waitFor({ state: 'hidden' });

      // Test Dark Theme Modal
      const darkThemeButton = page.locator('[data-testid="show-onboarding-modal-dark"]');
      await darkThemeButton.click();

      modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      iframe = page.locator('iframe');
      iframeFrame = iframe.contentFrame();

      // Verify modal content is displayed
      const darkTitle = await iframeFrame.locator('h1').textContent();
      expect(darkTitle).toBe('Extension Required');

      await iframeFrame.locator('[data-testid="modal-close"]').click();
      await modalOverlay.waitFor({ state: 'hidden' });
    });

    it('should test non-dismissible modal configuration for extension not installed', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.error');

      // Show non-dismissible modal
      const nonDismissibleButton = page.locator(
        '[data-testid="show-onboarding-modal-non-dismissible"]'
      );
      await nonDismissibleButton.click();

      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      // Try clicking backdrop (should not close modal)
      await modalOverlay.click({ position: { x: 10, y: 10 } });

      // Modal should still be visible
      await playwrightExpect(modalOverlay).toBeVisible();

      // Try pressing escape key (should not close modal)
      await page.keyboard.press('Escape');

      // Modal should still be visible
      await playwrightExpect(modalOverlay).toBeVisible();

      // Close modal using the close button
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const closeButton = iframeFrame.locator('[data-testid="modal-close"]');
      await closeButton.click();

      await modalOverlay.waitFor({ state: 'hidden' });
    });
  });
});
