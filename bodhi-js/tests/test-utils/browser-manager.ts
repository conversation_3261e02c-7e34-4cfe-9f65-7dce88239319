import { <PERSON><PERSON><PERSON>, Browser<PERSON>ontext, chromium, Page } from 'playwright';
import { expect } from 'vitest';
import path from 'path';
import { fileURLToPath } from 'url';

declare global {
  var TEST_APP_URL: string;
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Playwright configuration embedded directly in code
const PLAYWRIGHT_CONFIG = {
  // Global timeout configuration
  timeout: 3000, // 3 seconds for element finding
  expect: {
    timeout: 3000, // 3 seconds for assertions
  },
  workers: process.env.CI ? 1 : undefined,
  reporter: [['html'], ['list'], process.env.CI ? ['github'] : ['line']],
  // Use headless mode in CI
  use: {
    headless: process.env.CI ? true : false,
    actionTimeout: 3 * 1000,
    screenshot: 'only-on-failure',
    video: 'on-first-retry',
  },
};

export interface BrowserManagerConfig {
  extensionPath?: string | null;
  serverUrl?: string | null;
}

export class BrowserManager {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private config: BrowserManagerConfig;

  constructor(config: BrowserManagerConfig = {}) {
    this.config = {
      ...config,
    };
  }

  static default(serverUrl: string | null): BrowserManager {
    return new BrowserManager({
      extensionPath: path.resolve(__dirname, '../../../bodhi-browser-ext/dist'),
      serverUrl,
    });
  }

  async start(): Promise<BrowserContext> {
    if (this.context) {
      return this.context;
    }
    const args = [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--disable-gpu',
    ];
    if (this.config.extensionPath) {
      args.push(
        `--disable-extensions-except=${this.config.extensionPath}`,
        `--load-extension=${this.config.extensionPath}`
      );
    }

    this.context = await chromium.launchPersistentContext('', {
      args,
      headless: PLAYWRIGHT_CONFIG.use.headless,
      ...(PLAYWRIGHT_CONFIG.use.video && {
        recordVideo: {
          dir: path.join(__dirname, '../videos'),
        },
      }),
    });
    this.browser = this.context.browser()!;

    if (this.config.serverUrl) {
      await this.configureExtensionSettings(this.config.serverUrl);
    }
    return this.context;
  }

  async createPage(): Promise<Page> {
    if (!this.context) {
      throw new Error('Browser context not initialized. Call start() first.');
    }
    const page = await this.context.newPage();
    page.setDefaultTimeout(10000); // 10 second default timeout
    return page;
  }

  public async configureExtensionSettings(serverUrl: string): Promise<void> {
    if (!this.context) {
      throw new Error('Browser context not initialized. Call start() first.');
    }
    const page = await this.context.newPage();
    await page.goto(globalThis.TEST_APP_URL);
    await this.waitForExtensionReady(page);
    const extensionId = await page.evaluate(async () => {
      return (await (window as any).bodhiext?.getExtensionId()) || null;
    });
    if (!extensionId) {
      throw new Error('Could not get extension ID for configuration');
    }
    const extensionUrl = `chrome-extension://${extensionId}/index.html`;
    await page.goto(extensionUrl);

    await page.waitForSelector('[id="backendUrl"]');
    await page.fill('[id="backendUrl"]', serverUrl);
    await page.click('[id="submit"]');
    await page.waitForSelector('[id="message-container"]');
    const message = await page.textContent('[id="message-container"]');
    expect(message).toContain('Settings saved');
    await page.close();
  }

  async stop(): Promise<void> {
    if (this.context) {
      await this.context.close();
      this.context = null;
    }
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  async waitForExtensionReady(page: Page): Promise<void> {
    await page.waitForFunction(
      () =>
        typeof (window as any).bodhiext !== 'undefined' &&
        typeof (window as any).bodhiext.getExtensionId === 'function'
    );
  }

  isRunning(): boolean {
    return this.browser !== null && this.context !== null;
  }
}
