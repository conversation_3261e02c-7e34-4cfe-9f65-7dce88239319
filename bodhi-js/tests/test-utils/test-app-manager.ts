import { spawn, ChildProcess, exec } from 'child_process';
import { createServer } from 'http';
import path from 'path';

export interface TestAppManagerConfig {
  appPath: string;
  port?: number; // Defaults to 12345, configurable but enforced
  timeout?: number;
}

export class TestAppManager {
  private process: ChildProcess | null = null;
  private config: TestAppManagerConfig;
  private readonly requiredPort: number;

  constructor(config: TestAppManagerConfig) {
    this.requiredPort = config.port || 12345;
    this.config = {
      timeout: 10000,
      ...config,
      port: this.requiredPort,
    };
  }

  async start(): Promise<string> {
    if (this.process) {
      throw new Error('Test app is already running');
    }

    // Clear port before starting
    await this.clearPort(this.requiredPort);

    const appUrl = `http://localhost:${this.requiredPort}`;

    // Start the test app using npx serve
    this.process = spawn(
      'npx',
      [
        'serve',
        '-s', // Single page application mode
        '-l',
        this.requiredPort.toString(),
        path.resolve(this.config.appPath, 'dist'), // Serve from dist directory
      ],
      {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: this.config.appPath,
        env: {
          ...process.env,
          NODE_ENV: 'test',
        },
      }
    );

    // Pipe stdout/stderr to parent process
    this.process.stdout?.on('data', data => {
      process.stdout.write(`[test-app] ${data}`);
    });

    this.process.stderr?.on('data', data => {
      process.stderr.write(`[test-app] ${data}`);
    });

    // Handle process exit
    this.process.on('exit', () => {
      this.process = null;
    });

    this.process.on('error', error => {
      console.error('[test-app] Process error:', error);
      this.process = null;
    });

    // Wait for app to be ready
    await this.waitForHealthCheck(appUrl);

    return appUrl;
  }

  async buildApp(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Use shell: true to ensure npm is found in PATH on all platforms
      const isWindows = process.platform === 'win32';
      const npmCommand = isWindows ? 'npm.cmd' : 'npm';

      const buildProcess = spawn(npmCommand, ['run', 'build:fast'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: this.config.appPath,
        shell: true,
        env: {
          ...process.env,
        },
      });

      buildProcess.stdout?.on('data', data => {
        process.stdout.write(`[test-app-build] ${data}`);
      });

      buildProcess.stderr?.on('data', data => {
        process.stderr.write(`[test-app-build] ${data}`);
      });

      buildProcess.on('exit', code => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Build failed with exit code ${code}`));
        }
      });

      buildProcess.on('error', error => {
        reject(new Error(`Build process error: ${error.message}`));
      });
    });
  }

  async clearPort(port: number): Promise<void> {
    const isPortAvailable = await this.checkPortAvailable(port);
    if (isPortAvailable) {
      return;
    }

    try {
      const pids = await this.findProcessUsingPort(port);
      if (pids.length > 0) {
        await this.killProcesses(pids);

        // Wait a moment for the port to be released
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Verify port is now available
        const isNowAvailable = await this.checkPortAvailable(port);
        if (!isNowAvailable) {
          throw new Error(`Port ${port} is still not available after killing processes`);
        }
      }
    } catch (error) {
      console.error(`[test-app] Error clearing port ${port}:`, error);
      throw new Error(`Failed to clear port ${port}: ${(error as Error).message}`);
    }
  }

  private async findProcessUsingPort(port: number): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const isWindows = process.platform === 'win32';

      let command: string;
      if (isWindows) {
        // Windows: netstat -ano | findstr :12345
        command = `netstat -ano | findstr :${port}`;
      } else {
        // Unix/Linux/macOS: lsof -ti:12345
        command = `lsof -ti:${port}`;
      }

      exec(command, (error, stdout, stderr) => {
        if (error) {
          // If no processes found, that's okay
          if (error.code === 1 && !stderr) {
            resolve([]);
            return;
          }
          reject(new Error(`Failed to find processes using port ${port}: ${error.message}`));
          return;
        }

        const pids: string[] = [];

        if (isWindows) {
          // Parse Windows netstat output
          const lines = stdout.split('\n').filter(line => line.trim());
          for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 5) {
              const pid = parts[parts.length - 1];
              if (pid && pid !== '0' && !pids.includes(pid)) {
                pids.push(pid);
              }
            }
          }
        } else {
          // Parse Unix lsof output (one PID per line)
          const lines = stdout.split('\n').filter(line => line.trim());
          for (const line of lines) {
            const pid = line.trim();
            if (pid && !pids.includes(pid)) {
              pids.push(pid);
            }
          }
        }

        resolve(pids);
      });
    });
  }

  private async killProcesses(pids: string[]): Promise<void> {
    const isWindows = process.platform === 'win32';

    for (const pid of pids) {
      await new Promise<void>(resolve => {
        const command = isWindows ? `taskkill /F /PID ${pid}` : `kill ${pid}`;

        exec(command, error => {
          if (error) {
            console.warn(`[test-app] Warning: Failed to kill process ${pid}: ${error.message}`);
            // Don't reject, continue with other processes
          }
          resolve();
        });
      });
    }
  }

  private async checkPortAvailable(port: number): Promise<boolean> {
    return new Promise(resolve => {
      const server = createServer();

      server.listen(port, () => {
        server.close(() => resolve(true));
      });

      server.on('error', () => resolve(false));
    });
  }

  private async waitForHealthCheck(appUrl: string): Promise<void> {
    const startTime = Date.now();
    const timeout = this.config.timeout!;

    while (Date.now() - startTime < timeout) {
      try {
        const response = await fetch(`${appUrl}/index.html`);
        if (response.ok) {
          return;
        }
      } catch {
        // App not ready yet, continue waiting
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    throw new Error(`Test app failed to start within ${timeout}ms at ${appUrl}`);
  }

  async stop(): Promise<void> {
    if (this.process) {
      // Send SIGTERM to gracefully shutdown
      this.process.kill('SIGTERM');

      // Wait for process to exit
      await new Promise<void>(resolve => {
        if (!this.process) {
          resolve();
          return;
        }

        const timeout = setTimeout(() => {
          // Force kill if graceful shutdown takes too long
          if (this.process) {
            this.process.kill('SIGKILL');
          }
        }, 5000);

        this.process.on('exit', () => {
          clearTimeout(timeout);
          resolve();
        });
      });

      this.process = null;
    }

    // Clear port after stopping to ensure no hanging processes
    try {
      await this.clearPort(this.requiredPort);
    } catch (error) {
      console.error('[test-app] Error clearing port after stop:', error);
    }
  }

  getAppUrl(): string {
    return `http://localhost:${this.requiredPort}`;
  }

  getPort(): number {
    return this.requiredPort;
  }

  isRunning(): boolean {
    return this.process !== null && !this.process.killed;
  }
}
