import { createServer } from 'http';
import { AddressInfo } from 'net';
import { mkdtempSync } from 'fs';
import { tmpdir } from 'os';
import { join, resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export interface BodhiAppServerConfig {
  authUrl: string;
  authRealm: string;
  clientId: string;
  clientSecret: string;
  appClientId: string;
  appClientSecret?: string;
  appStatus?: string; // App status: ready, setup, resource-admin, etc.
  port?: number; // If not provided, will use random available port
  timeout?: number;
}

export class BodhiAppServerManager {
  private server: any = null;
  private config: BodhiAppServerConfig;
  private actualPort: number | null = null;
  private tempHome: string | null = null;

  constructor(config: BodhiAppServerConfig) {
    this.config = {
      timeout: 10000,
      ...config,
    };
  }

  async start(): Promise<string> {
    if (this.server) {
      throw new Error('Server is already running');
    }

    // Find available port if not specified
    const port = this.config.port || (await this.findAvailablePort());
    this.actualPort = port;

    const serverUrl = `http://localhost:${port}`;

    // Create temporary home directory
    this.tempHome = mkdtempSync(join(tmpdir(), 'bodhi-test-'));

    // Create Bodhi server using app-bindings
    const { server } = await this.createBodhiServer(this.config);
    this.server = server;

    // Start the server
    await this.server.start();

    // Verify server is running
    const isRunning = await this.server.isRunning();
    if (!isRunning) {
      throw new Error('Bodhi server failed to start');
    }

    // Wait for server to be ready
    await this.waitForHealthCheck(serverUrl);

    console.log(`[bodhi-server] Server started at ${serverUrl}`);
    return serverUrl;
  }

  private async createBodhiServer(config: BodhiAppServerConfig) {
    // Load app-bindings
    const appBindings = await import('@bodhiapp/app-bindings');
    const {
      createNapiAppOptions,
      setEnvVar,
      setSystemSetting,
      setAppSetting,
      setAppStatus,
      setClientCredentials,
      BodhiServer,
      BODHI_HOST,
      BODHI_PORT,
      BODHI_ENV_TYPE,
      BODHI_APP_TYPE,
      BODHI_VERSION,
      BODHI_LOG_LEVEL,
      BODHI_LOG_STDOUT,
      BODHI_AUTH_URL,
      BODHI_AUTH_REALM,
      BODHI_EXEC_LOOKUP_PATH,
    } = appBindings;

    // Build configuration for the Bodhi server
    let napiConfig = createNapiAppOptions();

    // Environment variables
    napiConfig = setEnvVar(napiConfig, 'HOME', this.tempHome!);
    napiConfig = setEnvVar(napiConfig, BODHI_HOST, '127.0.0.1');
    napiConfig = setEnvVar(napiConfig, BODHI_PORT, this.actualPort!.toString());

    // Set HF_HOME for model caching
    const { homedir } = await import('os');
    const hfHome = resolve(homedir(), '.cache', 'huggingface');
    napiConfig = setEnvVar(napiConfig, 'HF_HOME', hfHome);

    // System settings
    napiConfig = setSystemSetting(napiConfig, BODHI_ENV_TYPE, 'development');
    napiConfig = setSystemSetting(napiConfig, BODHI_APP_TYPE, 'container');
    napiConfig = setSystemSetting(napiConfig, BODHI_VERSION, '1.0.0-test');
    napiConfig = setSystemSetting(napiConfig, BODHI_AUTH_URL, config.authUrl);
    napiConfig = setSystemSetting(napiConfig, BODHI_AUTH_REALM, config.authRealm);

    // App settings
    napiConfig = setAppSetting(napiConfig, BODHI_LOG_LEVEL, 'debug');
    napiConfig = setAppSetting(napiConfig, BODHI_LOG_STDOUT, 'true');

    // Set bin path for executable lookup
    const binPath = resolve(__dirname, '../../../bin');
    napiConfig = setAppSetting(napiConfig, BODHI_EXEC_LOOKUP_PATH, binPath);

    // Set app status and client credentials
    const appStatus = config.appStatus || 'ready';
    napiConfig = setAppStatus(napiConfig, appStatus);
    napiConfig = setClientCredentials(napiConfig, config.clientId, config.clientSecret);

    const server = new BodhiServer(napiConfig);

    return { server };
  }

  private async findAvailablePort(): Promise<number> {
    return new Promise((resolve, reject) => {
      const server = createServer();
      server.listen(0, () => {
        const port = (server.address() as AddressInfo).port;
        server.close(() => resolve(port));
      });
      server.on('error', reject);
    });
  }

  private async waitForHealthCheck(serverUrl: string): Promise<void> {
    const startTime = Date.now();
    const timeout = this.config.timeout!;

    while (Date.now() - startTime < timeout) {
      try {
        const response = await fetch(`${serverUrl}/ping`);
        if (response.ok) {
          console.log(`[bodhi-server] Health check passed at ${serverUrl}`);
          return;
        }
      } catch {
        // Server not ready yet, continue waiting
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    throw new Error(`Bodhi server failed to start within ${timeout}ms`);
  }

  async stop(): Promise<void> {
    if (this.server) {
      console.log('[bodhi-server] Stopping server...');

      try {
        await this.server.stop();
      } catch (error) {
        console.error('[bodhi-server] Error stopping server:', error);
      }

      this.server = null;
      this.actualPort = null;
    }

    // Clean up temporary home directory
    if (this.tempHome) {
      try {
        const { rmSync } = await import('fs');
        rmSync(this.tempHome, { recursive: true, force: true });
      } catch (error) {
        console.warn('[bodhi-server] Could not clean up temp directory:', error);
      }
      this.tempHome = null;
    }
  }

  getServerUrl(): string | null {
    if (this.actualPort) {
      return `http://localhost:${this.actualPort}`;
    }
    return null;
  }

  getPort(): number | null {
    return this.actualPort;
  }

  async isRunning(): Promise<boolean> {
    if (!this.server) {
      return false;
    }

    try {
      return await this.server.isRunning();
    } catch {
      return false;
    }
  }
}
