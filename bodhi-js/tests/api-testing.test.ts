import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { expect as playwrightExpect } from '@playwright/test';
import { Page } from 'playwright';
import { BrowserManager, BodhiAppServerManager } from './test-utils';
import type { BodhiAppServerConfig } from './test-utils';

declare global {
  var INTEG_TEST_AUTH_URL: string;
  var INTEG_TEST_AUTH_REALM: string;
  var INTEG_TEST_CLIENT_ID: string;
  var INTEG_TEST_CLIENT_SECRET: string;
  var INTEG_TEST_USERNAME: string;
  var INTEG_TEST_PASSWORD: string;
  var INTEG_TEST_APP_CLIENT_ID: string;
  var TEST_APP_URL: string;
}

describe('API Testing Interface Integration Tests (Authenticated)', () => {
  let browserManager: BrowserManager;
  let serverManager: BodhiAppServerManager;
  let serverUrl: string;
  let page: Page;

  beforeAll(async () => {
    const serverConfig: BodhiAppServerConfig = {
      authUrl: globalThis.INTEG_TEST_AUTH_URL,
      authRealm: globalThis.INTEG_TEST_AUTH_REALM,
      clientId: globalThis.INTEG_TEST_CLIENT_ID,
      clientSecret: globalThis.INTEG_TEST_CLIENT_SECRET,
      appClientId: globalThis.INTEG_TEST_APP_CLIENT_ID,
    };

    serverManager = new BodhiAppServerManager(serverConfig);
    serverUrl = await serverManager.start();

    // Start browser with extension
    browserManager = BrowserManager.default(serverUrl);
    await browserManager.start();

    // Create page and complete OAuth flow once for all tests
    page = await browserManager.createPage();
    await completeOAuthFlow(page);
  });

  afterAll(async () => {
    if (page) {
      await page.close();
    }
    if (browserManager) {
      await browserManager.stop();
    }
    if (serverManager) {
      await serverManager.stop();
    }
  });

  async function completeOAuthFlow(page: Page): Promise<void> {
    // Navigate to landing page
    await page.goto(globalThis.TEST_APP_URL);
    await page.waitForLoadState('networkidle');

    // Wait for extension detection
    await page.waitForSelector('.extension-status.detected');

    // Click login button
    const loginButton = page.locator('button:has-text("Login with OAuth")');
    await playwrightExpect(loginButton).toBeVisible();
    await loginButton.click();

    // Handle OAuth flow
    await page.waitForURL(url => url.toString().includes(globalThis.INTEG_TEST_AUTH_URL));

    // Fill in credentials
    const usernameField = page.locator('input[name="username"], input[type="email"], #username');
    const passwordField = page.locator('input[name="password"], input[type="password"], #password');
    const submitButton = page.locator(
      'button[type="submit"], input[type="submit"], button:has-text("Sign In")'
    );

    await playwrightExpect(usernameField).toBeVisible();
    await playwrightExpect(passwordField).toBeVisible();

    await usernameField.fill(globalThis.INTEG_TEST_USERNAME);
    await passwordField.fill(globalThis.INTEG_TEST_PASSWORD);
    await submitButton.click();

    // Wait for redirect back to app
    await page.waitForURL(url => url.pathname === '/');

    // Wait for authentication to complete
    await page.waitForSelector('.auth-status.authenticated');
  }

  async function navigateToApiTestPage(): Promise<void> {
    // First navigate to home page to ensure we're in the right state
    await page.goto(globalThis.TEST_APP_URL);
    await page.waitForLoadState('networkidle');

    // Wait for authentication state to be visible
    await page.waitForSelector('.auth-status.authenticated');

    // Navigate to API test page
    const goToApiTestButton = page.locator('button:has-text("Go to API Test")');
    await playwrightExpect(goToApiTestButton).toBeVisible();
    await goToApiTestButton.click();
    await page.waitForURL(url => url.pathname === '/api-test');
  }

  describe('API Test Page Interface', () => {
    it('should display API test page with proper form elements', async () => {
      // Navigate to API test page
      await navigateToApiTestPage();

      // Verify page elements are present
      await playwrightExpect(page.locator('h1:has-text("API Test Interface")')).toBeVisible();

      // Verify form elements
      await playwrightExpect(page.locator('[data-testid="api-method"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="api-endpoint"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="api-body"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="api-headers"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="streaming-mode"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="include-auth"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="submit-api-request"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="clear-output"]')).toBeVisible();

      // Verify status indicators
      await playwrightExpect(page.locator('[data-testid="extension-status"]')).toContainText(
        'Ready'
      );
      await playwrightExpect(page.locator('[data-testid="api-status"]')).toContainText('Ready');

      // Verify response sections
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText(
        '(no response yet)'
      );
      await playwrightExpect(page.locator('[data-testid="response-headers"]')).toContainText(
        '(no response yet)'
      );
      await playwrightExpect(page.locator('[data-testid="response-body"]')).toContainText(
        '(no response yet)'
      );
    });

    it('should make successful non-authenticated request to /ping endpoint', async () => {
      // Navigate to API test page
      await navigateToApiTestPage();

      // Configure request for /ping endpoint
      await page.selectOption('[data-testid="api-method"]', 'GET');
      await page.fill('[data-testid="api-endpoint"]', '/ping');

      // Ensure authentication is not included
      await page.uncheck('[data-testid="include-auth"]');

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Wait for API status to change to calling then completed
      await page.waitForSelector('[data-testid="api-status"]:has-text("Completed")');

      // Verify response
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('200');
      await playwrightExpect(page.locator('[data-testid="response-body"]')).toContainText(
        'message'
      );
      const content = await page.locator('[data-testid="response-body"]').textContent();
      const contentJson = await JSON.parse(content!);
      expect(contentJson).toStrictEqual({ message: 'pong' });
    });

    it('should make successful authenticated request to /v1/chat/completions endpoint', async () => {
      // Navigate to API test page
      await navigateToApiTestPage();

      // Configure request for chat completions
      await page.selectOption('[data-testid="api-method"]', 'POST');
      await page.fill('[data-testid="api-endpoint"]', '/v1/chat/completions');

      // Set request body
      const requestBody = JSON.stringify({
        model: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M',
        messages: [{ role: 'user', content: 'Answer in one word: what day comes after Monday?' }],
        max_tokens: 50,
        stream: false,
      });
      await page.fill('[data-testid="api-body"]', requestBody);

      // Include authentication
      await page.check('[data-testid="include-auth"]');

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Wait for API status to change to calling then completed
      await page.waitForSelector('[data-testid="api-status"]:has-text("Calling API")');
      await page.waitForSelector('[data-testid="api-status"]:has-text("Completed")');

      // Verify response
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('200');
      await playwrightExpect(page.locator('[data-testid="response-body"]')).toContainText(
        'choices'
      );
      const content = await page.locator('[data-testid="response-body"]').textContent();
      const contentJson = await JSON.parse(content!);
      expect(contentJson.choices[0].message.content).toContain('Tuesday');
    });

    it('should handle streaming chat completions with real-time updates', async () => {
      await navigateToApiTestPage();
      await page.selectOption('[data-testid="api-method"]', 'POST');
      await page.fill('[data-testid="api-endpoint"]', '/v1/chat/completions');
      const requestBody = JSON.stringify({
        model: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M',
        messages: [{ role: 'user', content: 'Write 50 words information on India' }],
        max_tokens: 100,
        stream: true,
      });
      await page.fill('[data-testid="api-body"]', requestBody);
      await page.check('[data-testid="streaming-mode"]');
      await page.check('[data-testid="include-auth"]');
      await page.click('[data-testid="submit-api-request"]');
      await page.waitForSelector('[data-testid="api-status"]:has-text("Completed")');

      // Verify final response
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('200');
      await playwrightExpect(page.locator('[data-testid="stream-content"]')).toContainText('India');
      await playwrightExpect(page.locator('[data-testid="response-body"]')).toContainText(
        'choices'
      );
    });

    it('should clear output and reset form state', async () => {
      // Navigate to API test page
      await navigateToApiTestPage();

      // Make a request first to have some output
      await page.selectOption('[data-testid="api-method"]', 'GET');
      await page.fill('[data-testid="api-endpoint"]', '/ping');
      await page.click('[data-testid="submit-api-request"]');

      // Wait for response
      await page.waitForSelector('[data-testid="api-status"]:has-text("Completed")');
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('200');

      // Clear output
      await page.click('[data-testid="clear-output"]');

      // Verify output is cleared
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText(
        '(no response yet)'
      );
      await playwrightExpect(page.locator('[data-testid="response-headers"]')).toContainText(
        '(no response yet)'
      );
      await playwrightExpect(page.locator('[data-testid="response-body"]')).toContainText(
        '(no response yet)'
      );

      // Verify API status is reset
      await playwrightExpect(page.locator('[data-testid="api-status"]')).toContainText('Ready');
    });

    it('should display proper error handling for invalid endpoints', async () => {
      // Navigate to API test page
      await navigateToApiTestPage();

      // Configure request for invalid endpoint
      await page.selectOption('[data-testid="api-method"]', 'GET');
      await page.fill('[data-testid="api-endpoint"]', '/invalid-endpoint');

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Verify error response
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('404');
    });

    it('should handle custom headers correctly', async () => {
      // Navigate to API test page
      await navigateToApiTestPage();

      // Configure request with custom headers
      await page.selectOption('[data-testid="api-method"]', 'GET');
      await page.fill('[data-testid="api-endpoint"]', '/ping');

      // Add custom headers
      await page.fill(
        '[data-testid="api-headers"]',
        'X-Custom-Header: test-value\nX-Another-Header: another-value'
      );

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Wait for response
      await page.waitForSelector('[data-testid="api-status"]:has-text("Completed")');

      // Verify successful response (headers are sent to server)
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('200');
    });
  });
});
