import { describe, it, beforeAll, afterAll } from 'vitest';
import { expect as playwrightExpect } from '@playwright/test';
import { Page } from 'playwright';
import { BrowserManager, BodhiAppServerManager } from './test-utils';
import type { BodhiAppServerConfig } from './test-utils';

declare global {
  var INTEG_TEST_AUTH_URL: string;
  var INTEG_TEST_AUTH_REALM: string;
  var INTEG_TEST_CLIENT_ID: string;
  var INTEG_TEST_CLIENT_SECRET: string;
  var INTEG_TEST_USERNAME: string;
  var INTEG_TEST_PASSWORD: string;
  var INTEG_TEST_APP_CLIENT_ID: string;
  var TEST_APP_URL: string;
}

describe('API Testing Interface Integration Tests (Unauthenticated)', () => {
  let browserManager: BrowserManager;
  let serverManager: BodhiAppServerManager;
  let serverUrl: string;
  let page: Page;

  beforeAll(async () => {
    const serverConfig: BodhiAppServerConfig = {
      authUrl: globalThis.INTEG_TEST_AUTH_URL,
      authRealm: globalThis.INTEG_TEST_AUTH_REALM,
      clientId: globalThis.INTEG_TEST_CLIENT_ID,
      clientSecret: globalThis.INTEG_TEST_CLIENT_SECRET,
      appClientId: globalThis.INTEG_TEST_APP_CLIENT_ID,
    };

    serverManager = new BodhiAppServerManager(serverConfig);
    serverUrl = await serverManager.start();

    // Start browser with extension
    browserManager = BrowserManager.default(serverUrl);
    await browserManager.start();

    // Create page but do NOT authenticate
    page = await browserManager.createPage();
  });

  afterAll(async () => {
    if (page) {
      await page.close();
    }
    if (browserManager) {
      await browserManager.stop();
    }
    if (serverManager) {
      await serverManager.stop();
    }
  });

  async function navigateDirectlyToApiTestPage(): Promise<void> {
    // Navigate directly to API test page without authentication
    await page.goto(`${globalThis.TEST_APP_URL}/api-test`);
    await page.waitForLoadState('networkidle');
  }

  describe('Unauthenticated API Test Page Interface', () => {
    it('should display API test page even when not authenticated', async () => {
      // Navigate directly to API test page
      await navigateDirectlyToApiTestPage();

      // Verify page elements are present
      await playwrightExpect(page.locator('h1:has-text("API Test Interface")')).toBeVisible();

      // Verify form elements
      await playwrightExpect(page.locator('[data-testid="api-method"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="api-endpoint"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="api-body"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="api-headers"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="streaming-mode"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="include-auth"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="submit-api-request"]')).toBeVisible();
      await playwrightExpect(page.locator('[data-testid="clear-output"]')).toBeVisible();

      // Verify status indicators
      await playwrightExpect(page.locator('[data-testid="extension-status"]')).toContainText(
        'Ready'
      );
      await playwrightExpect(page.locator('[data-testid="api-status"]')).toContainText('Ready');

      // Verify response sections
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText(
        '(no response yet)'
      );
      await playwrightExpect(page.locator('[data-testid="response-headers"]')).toContainText(
        '(no response yet)'
      );
      await playwrightExpect(page.locator('[data-testid="response-body"]')).toContainText(
        '(no response yet)'
      );
    });

    it('should make successful non-authenticated request to /ping endpoint', async () => {
      // Navigate directly to API test page
      await navigateDirectlyToApiTestPage();

      // Configure request for /ping endpoint
      await page.selectOption('[data-testid="api-method"]', 'GET');
      await page.fill('[data-testid="api-endpoint"]', '/ping');

      // Ensure authentication is not included
      await page.uncheck('[data-testid="include-auth"]');

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Wait for API status to change to calling then completed
      await page.waitForSelector('[data-testid="api-status"]:has-text("Calling API")');
      await page.waitForSelector('[data-testid="api-status"]:has-text("Completed")');

      // Verify response
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('200');
      await playwrightExpect(page.locator('[data-testid="response-body"]')).toContainText(
        'message'
      );
    });

    it('should show error when trying to include auth without being authenticated', async () => {
      // Navigate directly to API test page
      await navigateDirectlyToApiTestPage();

      // Configure request for chat completions
      await page.selectOption('[data-testid="api-method"]', 'POST');
      await page.fill('[data-testid="api-endpoint"]', '/v1/chat/completions');

      // Set request body
      const requestBody = JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Hello, this is a test message.' }],
        max_tokens: 50,
      });
      await page.fill('[data-testid="api-body"]', requestBody);

      // Try to include authentication when not authenticated
      await page.check('[data-testid="include-auth"]');

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Should show error about no access token
      await playwrightExpect(
        page.locator('text=Authentication requested but no access token found')
      ).toBeVisible();
    });

    it('should handle authentication error when calling chat API without auth', async () => {
      // Navigate directly to API test page
      await navigateDirectlyToApiTestPage();

      // Configure request for chat completions without auth
      await page.selectOption('[data-testid="api-method"]', 'POST');
      await page.fill('[data-testid="api-endpoint"]', '/v1/chat/completions');

      // Set request body
      const requestBody = JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Hello, this is a test message.' }],
        max_tokens: 50,
      });
      await page.fill('[data-testid="api-body"]', requestBody);

      // Do NOT include authentication
      await page.uncheck('[data-testid="include-auth"]');

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Verify error response
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('401');
    });

    it('should display proper error handling for invalid endpoints', async () => {
      // Navigate directly to API test page
      await navigateDirectlyToApiTestPage();

      // Configure request for invalid endpoint
      await page.selectOption('[data-testid="api-method"]', 'GET');
      await page.fill('[data-testid="api-endpoint"]', '/invalid-endpoint');

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Verify error response
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('404');
    });

    it('should handle custom headers correctly without authentication', async () => {
      // Navigate directly to API test page
      await navigateDirectlyToApiTestPage();

      // Configure request with custom headers
      await page.selectOption('[data-testid="api-method"]', 'GET');
      await page.fill('[data-testid="api-endpoint"]', '/ping');

      // Add custom headers
      await page.fill(
        '[data-testid="api-headers"]',
        'X-Custom-Header: test-value\nX-Another-Header: another-value'
      );

      // Submit request
      await page.click('[data-testid="submit-api-request"]');

      // Wait for response
      await page.waitForSelector('[data-testid="api-status"]:has-text("Completed")');

      // Verify successful response (headers are sent to server)
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('200');
    });

    it('should clear output and reset form state', async () => {
      // Navigate directly to API test page
      await navigateDirectlyToApiTestPage();

      // Make a request first to have some output
      await page.selectOption('[data-testid="api-method"]', 'GET');
      await page.fill('[data-testid="api-endpoint"]', '/ping');
      await page.click('[data-testid="submit-api-request"]');

      // Wait for response
      await page.waitForSelector('[data-testid="api-status"]:has-text("Completed")');
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText('200');

      // Clear output
      await page.click('[data-testid="clear-output"]');

      // Verify output is cleared
      await playwrightExpect(page.locator('[data-testid="response-status"]')).toContainText(
        '(no response yet)'
      );
      await playwrightExpect(page.locator('[data-testid="response-headers"]')).toContainText(
        '(no response yet)'
      );
      await playwrightExpect(page.locator('[data-testid="response-body"]')).toContainText(
        '(no response yet)'
      );

      // Verify API status is reset
      await playwrightExpect(page.locator('[data-testid="api-status"]')).toContainText('Ready');
    });
  });
});
