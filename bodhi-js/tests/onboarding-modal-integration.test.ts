import { expect } from '@playwright/test';
import path from 'path';
import { Page } from 'playwright';
import { afterAll, beforeAll, beforeEach, describe, it } from 'vitest';
import { BrowserManager, TestAppManager } from './test-utils';

describe('Onboarding Modal Integration', () => {
  let browserManager: BrowserManager;
  let testAppManager: TestAppManager;
  let page: Page;

  beforeAll(async () => {
    browserManager = new BrowserManager();
    const testAppPath = path.resolve(__dirname, 'bodhijs-test-app-react');
    testAppManager = new TestAppManager({
      appPath: testAppPath,
    });

    await browserManager.start();
    await testAppManager.start();

  });

  afterAll(async () => {
    await browserManager.stop();
    await testAppManager.stop();
    console.log('✅ Browser and test app stopped');
  });

  beforeEach(async () => {
    // Navigate to onboarding test page
    page = await browserManager.createPage();
    console.log('✅ Browser and test app started');
    await page.goto('http://localhost:12345/onboarding-test');
    await page.waitForLoadState('networkidle');
  });

  afterEach(async () => {
    if (page) {
      await page.close();
    }
  });

  it('should load the onboarding test page', async () => {
    await expect(page.locator('h1')).toContainText('Onboarding Modal Test');
    await expect(page.locator('[data-testid="show-modal-button"]')).toBeVisible();
  });

  it('should show modal with default state', async () => {
    // Click show modal button
    await page.locator('[data-testid="show-modal-button"]').click();

    // Wait for modal to appear
    await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).toBeVisible();

    // Check that event log shows modal shown
    await expect(page.locator('[data-testid="event-log"]')).toContainText('Modal shown');
  });

  it('should hide modal when hide button is clicked', async () => {
    // Show modal first
    await page.locator('[data-testid="show-modal-button"]').click();
    await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).toBeVisible();

    // Click hide modal button
    await page.locator('[data-testid="hide-modal-button"]').click();

    // Wait for modal to disappear
    await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).not.toBeVisible();

    // Check that event log shows modal hidden
    await expect(page.locator('[data-testid="event-log"]')).toContainText('Modal hidden');
  });

  it('should close modal when clicking backdrop', async () => {
    // Show modal first
    await page.locator('[data-testid="show-modal-button"]').click();
    await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).toBeVisible();

    // Click on modal overlay (backdrop)
    await page.locator('[data-testid="onboarding-modal-overlay"]').click();

    // Wait for modal to disappear
    await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).not.toBeVisible();

    // Check that event log shows modal dismissed
    await expect(page.locator('[data-testid="event-log"]')).toContainText('Modal dismissed');
  });

  it('should close modal when pressing Escape key', async () => {
    // Show modal first
    await page.locator('[data-testid="show-modal-button"]').click();
    await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).toBeVisible();

    // Press Escape key
    await page.keyboard.press('Escape');

    // Wait for modal to disappear
    await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).not.toBeVisible();

    // Check that event log shows modal dismissed
    await expect(page.locator('[data-testid="event-log"]')).toContainText('Modal dismissed');
  });

  it('should update state when dropdown values change', async () => {
    // Change browser dropdown
    await page.selectOption('select:has-text("Browser:")', 'firefox');

    // Check current state display
    const currentState = await page.locator('[data-testid="current-state"]').textContent();
    expect(currentState).toContain('"browser": "firefox"');

    // Change OS dropdown
    await page.selectOption('select:has-text("OS:")', 'windows');

    // Check current state display again
    const updatedState = await page.locator('[data-testid="current-state"]').textContent();
    expect(updatedState).toContain('"os": "windows"');
  });

  it('should update extension and server status', async () => {
    // Change extension status
    await page.selectOption('select:has-text("Extension Status:")', 'ready');

    // Check current state
    let currentState = await page.locator('[data-testid="current-state"]').textContent();
    expect(currentState).toContain('"status": "ready"');
    expect(currentState).toContain('"id": "test-extension-id"');

    // Change server status
    await page.selectOption('select:has-text("Server Status:")', 'ready');

    // Check current state again
    currentState = await page.locator('[data-testid="current-state"]').textContent();
    expect(currentState).toContain('"url": "http://localhost:1135"');
  });

  it('should refresh state in modal when refresh button is clicked', async () => {
    // Show modal first
    await page.locator('[data-testid="show-modal-button"]').click();
    await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).toBeVisible();

    // Change some state
    await page.selectOption('select:has-text("Extension Status:")', 'ready');

    // Click refresh state button
    await page.locator('[data-testid="refresh-state-button"]').click();

    // Check that event log shows state refreshed
    await expect(page.locator('[data-testid="event-log"]')).toContainText('State refreshed in modal');
  });

  it('should disable buttons appropriately based on modal state', async () => {
    // Initially, show button should be enabled, hide/refresh should be disabled
    await expect(page.locator('[data-testid="show-modal-button"]')).toBeEnabled();
    await expect(page.locator('[data-testid="hide-modal-button"]')).toBeDisabled();
    await expect(page.locator('[data-testid="refresh-state-button"]')).toBeDisabled();

    // Show modal
    await page.locator('[data-testid="show-modal-button"]').click();

    // Now show should be disabled, hide/refresh should be enabled
    await expect(page.locator('[data-testid="show-modal-button"]')).toBeDisabled();
    await expect(page.locator('[data-testid="hide-modal-button"]')).toBeEnabled();
    await expect(page.locator('[data-testid="refresh-state-button"]')).toBeEnabled();
  });

  it('should clear event logs when clear button is clicked', async () => {
    // Show and hide modal to generate some logs
    await page.locator('[data-testid="show-modal-button"]').click();
    await page.locator('[data-testid="hide-modal-button"]').click();

    // Verify logs exist
    await expect(page.locator('[data-testid="event-log"]')).toContainText('Modal shown');

    // Click clear logs button
    await page.locator('[data-testid="clear-logs-button"]').click();

    // Verify logs are cleared
    await expect(page.locator('[data-testid="event-log"]')).toContainText('No events logged');
  });

  it('should handle different extension status scenarios', async () => {
    const scenarios = [
      { status: 'not-installed', expectedError: 'Extension not installed' },
      { status: 'unreachable', expectedError: 'Extension installed but not responding' },
      { status: 'unsupported', expectedError: 'Browser not supported' }
    ];

    for (const scenario of scenarios) {
      // Set the extension status
      await page.selectOption('select:has-text("Extension Status:")', scenario.status);

      // Check current state contains expected error
      const currentState = await page.locator('[data-testid="current-state"]').textContent();
      expect(currentState).toContain(scenario.expectedError);

      // Show modal to test with this state
      await page.locator('[data-testid="show-modal-button"]').click();
      await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).toBeVisible();

      // Hide modal for next iteration
      await page.locator('[data-testid="hide-modal-button"]').click();
      await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).not.toBeVisible();
    }
  });

  it('should handle different server status scenarios', async () => {
    const scenarios = [
      { status: 'setup', expectedError: 'Server needs initial setup' },
      { status: 'resource-admin', expectedError: 'Server needs resource admin setup' },
      { status: 'pending-extension-ready', expectedError: 'Server detection requires extension setup first' },
      { status: 'unreachable', expectedError: 'Server not running' },
      { status: 'error', expectedError: 'Server error occurred' }
    ];

    for (const scenario of scenarios) {
      // Set the server status
      await page.selectOption('select:has-text("Server Status:")', scenario.status);

      // Check current state contains expected error
      const currentState = await page.locator('[data-testid="current-state"]').textContent();
      expect(currentState).toContain(scenario.expectedError);

      // Show modal to test with this state
      await page.locator('[data-testid="show-modal-button"]').click();
      await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).toBeVisible();

      // Hide modal for next iteration
      await page.locator('[data-testid="hide-modal-button"]').click();
      await expect(page.locator('[data-testid="onboarding-modal-overlay"]')).not.toBeVisible();
    }
  });

  it('should navigate back to landing page', async () => {
    // Click the back link
    await page.locator('a:has-text("← Back to Landing Page")').click();

    // Should be on landing page
    await expect(page.locator('h1')).toContainText('Bodhi JS Test App');
  });
});
