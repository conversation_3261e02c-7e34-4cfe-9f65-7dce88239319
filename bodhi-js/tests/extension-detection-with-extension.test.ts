import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { expect as playwrightExpect } from '@playwright/test';
import { Page } from 'playwright';
import { BrowserManager } from './test-utils';

declare global {
  var TEST_APP_URL: string;
}

describe('Extension Detection with Extension Installed', () => {
  let browserManager: BrowserManager;

  beforeAll(async () => {
    // Start browser with extension
    browserManager = BrowserManager.default('http://localhost:11135');
    await browserManager.start();
  });

  afterAll(async () => {
    if (browserManager) {
      await browserManager.stop();
    }
  });

  describe('Extension Detection Success Scenarios', () => {
    let page: Page;

    beforeAll(async () => {
      page = await browserManager.createPage();
    });

    afterAll(async () => {
      if (page) {
        await page.close();
      }
    });

    it('should detect extension, display extension ID, and handle all extension functionality', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      const pageTitle = await page.title();
      expect(pageTitle).toBe('Bodhi JS Test App');

      // Wait for platform to be initialized (could be detected or setup)
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');
      const statusTitle = await page.textContent('.status-title');
      expect(statusTitle).toMatch(/Platform Ready|Platform Setup Required/);

      let statusMessage = await page.textContent('.status-message');
      expect(statusMessage).toMatch(
        /Platform ready - extension and server connected|Extension connected but server needs configuration/
      );

      const extensionIdElement = page.locator('.extension-id');
      await playwrightExpect(extensionIdElement).toBeVisible();

      const extensionIdText = await extensionIdElement.textContent();
      expect(extensionIdText).toMatch(/^Extension ID: [a-z]{32}$/);

      const extensionId = extensionIdText?.replace('Extension ID: ', '');
      expect(extensionId).toMatch(/^[a-z]{32}$/);

      // Verify extension ID is available in window.bodhiext
      const windowExtensionId = await page.evaluate(async () => {
        return await (window as any).bodhiext?.getExtensionId();
      });
      expect(windowExtensionId).toBe(extensionId);

      // Verify platform state is displayed
      const platformStateElement = page.locator('.platform-state');
      await playwrightExpect(platformStateElement).toBeVisible();

      const platformStateText = await platformStateElement.textContent();
      expect(platformStateText).toContain('Platform Status:');
      expect(platformStateText).toContain('Extension: ready');
      expect(platformStateText).toContain('Server:');

      const serverStateSection = page.locator('.server-state');
      await playwrightExpect(serverStateSection).toBeVisible();
      statusMessage = await serverStateSection.locator('.status-message').textContent();
      expect(statusMessage).toMatch(/Status: (unreachable|setup|ready)/);

      // Test modal functionality with extension installed (server setup scenario)
      const modalButton = page.locator('[data-testid="show-onboarding-modal"]');
      expect(await modalButton.isVisible()).toBe(true);
      expect(await modalButton.isEnabled()).toBe(true);
      await modalButton.click();

      // Verify modal overlay appears
      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });
      expect(await modalOverlay.isVisible()).toBe(true);

      // Verify iframe content shows appropriate scenario (server setup, ready, or not found)
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Server Not Found');

      const checkAgainButton = iframeFrame.locator('[data-testid="modal-check-again"]');
      await checkAgainButton.click();
      await modalOverlay.waitFor({ state: 'hidden' });
      expect(await modalOverlay.isVisible()).toBe(false);
    });

    it('should display enhanced system information for extension installed but server not running', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Test system information display
      const showSystemInfoButton = page.locator('button:has-text("Show System Details")');
      await showSystemInfoButton.click();

      // Verify browser information is displayed
      const browserInfo = page.locator('h4:has-text("Browser Information")');
      await playwrightExpect(browserInfo).toBeVisible();

      // Verify OS information is displayed
      const osInfo = page.locator('h4:has-text("Operating System Information")');
      await playwrightExpect(osInfo).toBeVisible();

      // Verify download URLs section
      const downloadUrls = page.locator('h4:has-text("Download URLs & GitHub Links")');
      await playwrightExpect(downloadUrls).toBeVisible();

      // Verify GitHub issue links are present
      const browserSupportLink = page.locator('a:has-text("Request Browser Support")');
      await playwrightExpect(browserSupportLink).toBeVisible();

      const osSupportLink = page.locator('a:has-text("Request OS Support")');
      await playwrightExpect(osSupportLink).toBeVisible();

      const bugReportLink = page.locator('a:has-text("Report Bug")');
      await playwrightExpect(bugReportLink).toBeVisible();
    });

    it('should test modal callback functionality for extension installed but server not running', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Clear any existing callback logs
      const clearLogsButton = page.locator('button:has-text("Clear Logs")');
      await clearLogsButton.click();

      // Verify logs are cleared
      const callbackLogs = page.locator('[data-testid="callback-logs"]');
      const initialLogText = await callbackLogs.textContent();
      expect(initialLogText).toContain('No callback events yet...');

      // Show modal and verify callback logging
      const showModalButton = page.locator('[data-testid="show-onboarding-modal"]');
      await showModalButton.click();

      // Wait for modal to appear and check logs
      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      // Verify onboarding show callback was logged
      await page.waitForTimeout(500); // Give time for callback to be logged
      const showLogText = await callbackLogs.textContent();
      expect(showLogText).toContain('Showing onboarding modal');

      // Test dismiss callback
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const closeButton = iframeFrame.locator('[data-testid="modal-close"]');
      await closeButton.click();

      await modalOverlay.waitFor({ state: 'hidden' });

      // Verify dismiss callback was logged
      await page.waitForTimeout(500);
      const dismissLogText = await callbackLogs.textContent();
      expect(dismissLogText).toContain('Onboarding dismissed');
    });

    it('should display appropriate modal content for server not found scenario', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Show modal
      const showModalButton = page.locator('[data-testid="show-onboarding-modal"]');
      await showModalButton.click();

      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();

      // Verify modal has appropriate content sections
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Server Not Found');

      // Verify status section is present
      const statusSection = iframeFrame.locator('.status');
      await playwrightExpect(statusSection).toBeVisible();

      // Verify status items are displayed
      const statusItems = iframeFrame.locator('.status-item');
      const statusCount = await statusItems.count();
      expect(statusCount).toBeGreaterThan(0);

      // Verify action buttons are present
      const checkAgainButton = iframeFrame.locator('[data-testid="modal-check-again"]');
      await playwrightExpect(checkAgainButton).toBeVisible();

      const closeButton = iframeFrame.locator('[data-testid="modal-close"]');
      await playwrightExpect(closeButton).toBeVisible();

      // Close modal
      await closeButton.click();
      await modalOverlay.waitFor({ state: 'hidden' });
    });
  });
});
