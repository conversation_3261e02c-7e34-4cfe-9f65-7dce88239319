import { expect as playwrightExpect } from '@playwright/test';
import { Page } from 'playwright';
import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it } from 'vitest';
import { BodhiAppServerManager, BrowserManager } from './test-utils';

declare global {
  var TEST_APP_URL: string;
}

describe('Server State Detection with Running Server', () => {
  let browserManager: BrowserManager;
  let page: Page;

  beforeAll(async () => {
    // Start browser with extension
    browserManager = BrowserManager.default(null);
    await browserManager.start();
  });

  beforeEach(async () => {
    page = await browserManager.createPage();
  });

  afterEach(async () => {
    if (page) {
      await page.close();
    }
  });

  afterAll(async () => {
    if (browserManager) {
      await browserManager.stop();
    }
  });

  describe('appStatus=ready', () => {
    let serverManager: BodhiAppServerManager;
    let serverUrl: string;

    beforeEach(async () => {
      // Start Bodhi App server with ready status
      serverManager = new BodhiAppServerManager({
        authUrl: process.env.INTEG_TEST_AUTH_URL!,
        authRealm: process.env.INTEG_TEST_AUTH_REALM!,
        clientId: process.env.INTEG_TEST_CLIENT_ID!,
        clientSecret: process.env.INTEG_TEST_CLIENT_SECRET!,
        appClientId: process.env.INTEG_TEST_APP_CLIENT_ID!,
        appStatus: 'ready',
      });

      serverUrl = await serverManager.start();
      await browserManager.configureExtensionSettings(serverUrl);
    });

    afterEach(async () => {
      if (serverManager) {
        await serverManager.stop();
      }
    });

    it('should display server state as ready, allow refreshing, and show server state section', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized (could be detected or setup)
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Wait for server state section to appear
      await page.waitForSelector('.server-state');

      // Check that server state section appears
      const serverStateSection = page.locator('.server-state');
      await playwrightExpect(serverStateSection).toBeVisible();

      // Check that it has the correct title
      const statusTitle = await serverStateSection.locator('.status-title').textContent();
      expect(statusTitle).toBe('Server State');

      // Check that server state shows ready status
      const statusMessage = await serverStateSection.locator('.status-message').textContent();
      expect(statusMessage).toContain('Status: ready (v1.0.0-test)');

      // Check that server URL is displayed
      const serverUrlElement = serverStateSection.locator('.server-url');
      await playwrightExpect(serverUrlElement).toBeVisible();

      const urlText = await serverUrlElement.textContent();
      expect(urlText).toContain('URL:');

      // Check that no error details are displayed for ready state
      const errorDetails = serverStateSection.locator('.error-details');
      await playwrightExpect(errorDetails).not.toBeVisible();

      // Check that refresh button is available
      const refreshButton = serverStateSection.locator('button');
      await playwrightExpect(refreshButton).toBeVisible();
      expect(await refreshButton.textContent()).toBe('Refresh Server State');

      // Test refresh functionality
      await refreshButton.click();
      await page.waitForTimeout(1000);

      // Verify server state is still displayed correctly after refresh
      const refreshedStatusMessage = await serverStateSection
        .locator('.status-message')
        .textContent();
      expect(refreshedStatusMessage).toContain('Status: ready');

      // Test modal functionality for server ready scenario
      const modalButton = page.locator('[data-testid="show-onboarding-modal"]');
      expect(await modalButton.isVisible()).toBe(true);
      expect(await modalButton.isEnabled()).toBe(true);
      await modalButton.click();

      // Verify modal overlay appears with correct content for ready state
      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });
      expect(await modalOverlay.isVisible()).toBe(true);

      // Verify iframe content shows "Setup Complete" for ready state
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Setup Complete');

      const description = await iframeFrame.locator('p').first().textContent();
      expect(description).toContain('ready to use');

      // Test modal dismissal
      await iframeFrame.locator('[data-testid="modal-close"]').click();
      await modalOverlay.waitFor({ state: 'hidden' });
      await playwrightExpect(modalOverlay).not.toBeVisible();
    });

    it('should test enhanced modal functionality for server ready state', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Test system information display
      const showSystemInfoButton = page.locator('button:has-text("Show System Details")');
      await showSystemInfoButton.click();

      // Verify system information sections are displayed
      const browserInfo = page.locator('h4:has-text("Browser Information")');
      await playwrightExpect(browserInfo).toBeVisible();

      const osInfo = page.locator('h4:has-text("Operating System Information")');
      await playwrightExpect(osInfo).toBeVisible();

      // Test callback functionality
      const clearLogsButton = page.locator('button:has-text("Clear Logs")');
      await clearLogsButton.click();

      const callbackLogs = page.locator('[data-testid="callback-logs"]');
      const initialLogText = await callbackLogs.textContent();
      expect(initialLogText).toContain('No callback events yet...');

      // Show modal and verify callback logging
      const showModalButton = page.locator('[data-testid="show-onboarding-modal"]');
      await showModalButton.click();

      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      // Verify onboarding show callback was logged
      await page.waitForTimeout(500);
      const showLogText = await callbackLogs.textContent();
      expect(showLogText).toContain('Showing onboarding modal');

      // Verify modal content for ready state
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Setup Complete');

      // Test dismiss callback
      const closeButton = iframeFrame.locator('[data-testid="modal-close"]');
      await closeButton.click();
      await modalOverlay.waitFor({ state: 'hidden' });

      // Verify dismiss callback was logged
      await page.waitForTimeout(500);
      const dismissLogText = await callbackLogs.textContent();
      expect(dismissLogText).toContain('Onboarding dismissed');
    });
  });

  describe('appStatus=setup', () => {
    let serverManager: BodhiAppServerManager;
    let serverUrl: string;

    beforeEach(async () => {
      // Start Bodhi App server with setup status
      serverManager = new BodhiAppServerManager({
        authUrl: process.env.INTEG_TEST_AUTH_URL!,
        authRealm: process.env.INTEG_TEST_AUTH_REALM!,
        clientId: process.env.INTEG_TEST_CLIENT_ID!,
        clientSecret: process.env.INTEG_TEST_CLIENT_SECRET!,
        appClientId: process.env.INTEG_TEST_APP_CLIENT_ID!,
        appStatus: 'setup',
      });

      serverUrl = await serverManager.start();
      await browserManager.configureExtensionSettings(serverUrl);
    });

    afterEach(async () => {
      if (serverManager) {
        await serverManager.stop();
      }
    });

    it('should display server state as setup', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized (could be detected or setup)
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Wait for server state section to appear
      await page.waitForSelector('.server-state');

      // Check that server state shows setup status
      const serverStateSection = page.locator('.server-state');
      await playwrightExpect(serverStateSection).toBeVisible();

      const statusMessage = await serverStateSection.locator('.status-message').textContent();
      expect(statusMessage).toContain('Status: setup (v1.0.0-test)');

      // Check that server URL is displayed
      const serverUrlElement = serverStateSection.locator('.server-url');
      await playwrightExpect(serverUrlElement).toBeVisible();

      const urlText = await serverUrlElement.textContent();
      expect(urlText).toContain('URL:');

      // Check that refresh button is available
      const refreshButton = serverStateSection.locator('button');
      await playwrightExpect(refreshButton).toBeVisible();
      expect(await refreshButton.textContent()).toBe('Refresh Server State');

      // Test modal functionality for server setup scenario
      const modalButton = page.locator('[data-testid="show-onboarding-modal"]');
      expect(await modalButton.isVisible()).toBe(true);
      expect(await modalButton.isEnabled()).toBe(true);
      await modalButton.click();

      // Verify modal overlay appears with correct content for setup state
      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });
      expect(await modalOverlay.isVisible()).toBe(true);

      // Verify iframe content shows "Server Setup Required" for setup state
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Server Setup Required');

      // Test modal dismissal
      await iframeFrame.locator('[data-testid="modal-close"]').click();
      await modalOverlay.waitFor({ state: 'hidden' });
      await playwrightExpect(modalOverlay).not.toBeVisible();
    });

    it('should test enhanced modal functionality for server setup state', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Test callback functionality for setup state
      const clearLogsButton = page.locator('button:has-text("Clear Logs")');
      await clearLogsButton.click();

      const showModalButton = page.locator('[data-testid="show-onboarding-modal"]');
      await showModalButton.click();

      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      // Verify modal content for setup state
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Server Setup Required');

      // Verify status section shows appropriate information
      const statusSection = iframeFrame.locator('.status');
      await playwrightExpect(statusSection).toBeVisible();

      const statusItems = iframeFrame.locator('.status-item');
      const statusCount = await statusItems.count();
      expect(statusCount).toBeGreaterThan(0);

      // Test modal dismissal
      const closeButton = iframeFrame.locator('[data-testid="modal-close"]');
      await closeButton.click();
      await modalOverlay.waitFor({ state: 'hidden' });
    });
  });

  describe('appStatus=resource-admin', () => {
    let serverManager: BodhiAppServerManager;
    let serverUrl: string;

    beforeEach(async () => {
      // Start Bodhi App server with resource-admin status
      serverManager = new BodhiAppServerManager({
        authUrl: process.env.INTEG_TEST_AUTH_URL!,
        authRealm: process.env.INTEG_TEST_AUTH_REALM!,
        clientId: process.env.INTEG_TEST_CLIENT_ID!,
        clientSecret: process.env.INTEG_TEST_CLIENT_SECRET!,
        appClientId: process.env.INTEG_TEST_APP_CLIENT_ID!,
        appStatus: 'resource-admin',
      });

      serverUrl = await serverManager.start();
      await browserManager.configureExtensionSettings(serverUrl);
    });

    afterEach(async () => {
      if (serverManager) {
        await serverManager.stop();
      }
    });

    it('should display server state as resource-admin', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized (could be detected or setup)
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Wait for server state section to appear
      await page.waitForSelector('.server-state');

      // Check that server state shows resource-admin status
      const serverStateSection = page.locator('.server-state');
      await playwrightExpect(serverStateSection).toBeVisible();

      const statusMessage = await serverStateSection.locator('.status-message').textContent();
      expect(statusMessage).toContain('Status: resource-admin (v1.0.0-test)');

      // Check that server URL is displayed
      const serverUrlElement = serverStateSection.locator('.server-url');
      await playwrightExpect(serverUrlElement).toBeVisible();

      const urlText = await serverUrlElement.textContent();
      expect(urlText).toContain('URL:');

      // Check that refresh button is available
      const refreshButton = serverStateSection.locator('button');
      await playwrightExpect(refreshButton).toBeVisible();
      expect(await refreshButton.textContent()).toBe('Refresh Server State');

      // Test modal functionality for server resource-admin scenario
      const modalButton = page.locator('[data-testid="show-onboarding-modal"]');
      expect(await modalButton.isVisible()).toBe(true);
      expect(await modalButton.isEnabled()).toBe(true);
      await modalButton.click();

      // Verify modal overlay appears with correct content for resource-admin state
      const modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });
      expect(await modalOverlay.isVisible()).toBe(true);

      // Verify iframe content shows "Server Setup Required" for resource-admin state
      const iframe = page.locator('iframe');
      const iframeFrame = iframe.contentFrame();
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Server Setup Required');

      // Test modal dismissal
      await iframeFrame.locator('[data-testid="modal-close"]').click();
      await modalOverlay.waitFor({ state: 'hidden' });
      await playwrightExpect(modalOverlay).toBeHidden();
    });

    it('should test enhanced modal functionality for server resource-admin state', async () => {
      await page.goto(globalThis.TEST_APP_URL);
      await page.waitForLoadState('networkidle');

      // Wait for platform to be initialized
      await page.waitForSelector('.extension-status.detected, .extension-status.setup');

      // Test modal with different themes for resource-admin state
      const autoThemeButton = page.locator('[data-testid="show-onboarding-modal"]');
      await autoThemeButton.click();

      let modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      let iframe = page.locator('iframe');
      let iframeFrame = iframe.contentFrame();

      // Verify modal content is displayed for resource-admin state
      const title = await iframeFrame.locator('h1').textContent();
      expect(title).toBe('Server Setup Required');

      // Close modal
      const closeButton = iframeFrame.locator('[data-testid="modal-close"]');
      await closeButton.click();
      await modalOverlay.waitFor({ state: 'hidden' });

      // Test non-dismissible modal for resource-admin state
      const nonDismissibleButton = page.locator(
        '[data-testid="show-onboarding-modal-non-dismissible"]'
      );
      await nonDismissibleButton.click();

      modalOverlay = page.locator('[data-testid="onboarding-modal-overlay"]');
      await modalOverlay.waitFor({ state: 'visible' });

      // Try clicking backdrop (should not close modal)
      await modalOverlay.click({ position: { x: 10, y: 10 } });

      // Modal should still be visible
      await playwrightExpect(modalOverlay).toBeVisible();

      // Close modal using the close button
      iframe = page.locator('iframe');
      iframeFrame = iframe.contentFrame();
      const finalCloseButton = iframeFrame.locator('[data-testid="modal-close"]');
      await finalCloseButton.click();

      await modalOverlay.waitFor({ state: 'hidden' });
    });
  });
});
