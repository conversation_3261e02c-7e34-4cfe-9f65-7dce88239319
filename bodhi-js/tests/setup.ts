import { config } from 'dotenv';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import { beforeAll, afterAll } from 'vitest';
import { TestAppManager } from './test-utils';
import type { TestAppManagerConfig } from './test-utils';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load .env.test file for test environment variables
config({ path: '.env.test' });

// Create global constants from environment variables
declare global {
  var INTEG_TEST_AUTH_URL: string;
  var INTEG_TEST_AUTH_REALM: string;
  var INTEG_TEST_CLIENT_ID: string;
  var INTEG_TEST_CLIENT_SECRET: string;
  var INTEG_TEST_USERNAME: string;
  var INTEG_TEST_PASSWORD: string;
  var INTEG_TEST_APP_CLIENT_ID: string;
  var INTEG_TEST_APP_CLIENT_SECRET: string;
  var TEST_APP_URL: string;
}

// Set global constants that can be accessed by test files
globalThis.INTEG_TEST_AUTH_URL = process.env.INTEG_TEST_AUTH_URL || '';
globalThis.INTEG_TEST_AUTH_REALM = process.env.INTEG_TEST_AUTH_REALM || '';
globalThis.INTEG_TEST_CLIENT_ID = process.env.INTEG_TEST_CLIENT_ID || '';
globalThis.INTEG_TEST_CLIENT_SECRET = process.env.INTEG_TEST_CLIENT_SECRET || '';
globalThis.INTEG_TEST_USERNAME = process.env.INTEG_TEST_USERNAME || '';
globalThis.INTEG_TEST_PASSWORD = process.env.INTEG_TEST_PASSWORD || '';
globalThis.INTEG_TEST_APP_CLIENT_ID = process.env.INTEG_TEST_APP_CLIENT_ID || '';
globalThis.INTEG_TEST_APP_CLIENT_SECRET = process.env.INTEG_TEST_APP_CLIENT_SECRET || '';

// Validate required environment variables
const requiredVars = [
  'INTEG_TEST_AUTH_URL',
  'INTEG_TEST_AUTH_REALM',
  'INTEG_TEST_CLIENT_ID',
  'INTEG_TEST_CLIENT_SECRET',
  'INTEG_TEST_USERNAME',
  'INTEG_TEST_PASSWORD',
  'INTEG_TEST_APP_CLIENT_ID',
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('Missing required environment variables:', missingVars);
  process.exit(1);
}

// Local test app manager (not exposed globally)
let testAppManager: TestAppManager | null = null;

// Global setup using Vitest lifecycle hooks
beforeAll(async () => {
  console.log('[global-setup] Starting global test setup...');

  // Build the browser extension once for the entire test session
  const extensionPath = path.resolve(__dirname, '../../bodhi-browser-ext');

  console.log('[global-setup] Building browser extension...');
  await buildExtension(extensionPath);
  console.log('[global-setup] Extension build completed');

  // Create and build the test app once for the entire test session
  const testAppPath = path.resolve(__dirname, 'bodhijs-test-app-react');
  const testAppConfig: TestAppManagerConfig = {
    appPath: testAppPath,
    port: 12345,
  };

  testAppManager = new TestAppManager(testAppConfig);

  console.log('[global-setup] Building test app...');
  await testAppManager.buildApp();
  console.log('[global-setup] Test app build completed');

  console.log('[global-setup] Starting test app...');
  const testAppUrl = await testAppManager.start();

  // Store globally for access by tests (only URL, not manager)
  globalThis.TEST_APP_URL = testAppUrl;

  console.log(`[global-setup] Test app started at ${testAppUrl}`);
  console.log('[global-setup] Global setup completed successfully');
});

afterAll(async () => {
  console.log('[global-teardown] Starting global test teardown...');

  // Stop the global test app
  if (testAppManager) {
    console.log('[global-teardown] Stopping test app...');
    await testAppManager.stop();
    console.log('[global-teardown] Test app stopped');
  }

  console.log('[global-teardown] Global teardown completed');
});

async function buildExtension(extensionPath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    // Use shell: true to ensure npm is found in PATH on all platforms
    const isWindows = process.platform === 'win32';
    const npmCommand = isWindows ? 'npm.cmd' : 'npm';

    const buildProcess = spawn(npmCommand, ['run', 'build:fast'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: extensionPath,
      shell: true,
      env: {
        ...process.env,
      },
    });

    buildProcess.stdout?.on('data', data => {
      process.stdout.write(`[global-setup-ext] ${data}`);
    });

    buildProcess.stderr?.on('data', data => {
      process.stderr.write(`[global-setup-ext] ${data}`);
    });

    buildProcess.on('exit', code => {
      if (code === 0) {
        console.log('[global-setup-ext] Extension build completed successfully');
        resolve();
      } else {
        reject(new Error(`Extension build failed with exit code ${code}`));
      }
    });

    buildProcess.on('error', error => {
      reject(new Error(`Extension build process error: ${error.message}`));
    });
  });
}
