/**
 * Type definitions for the Bodhi JS library.
 *
 * This module contains all TypeScript type definitions used throughout the library,
 * including API request/response types, chat completion types, and browser extension interfaces.
 */

// Import types from setup-modal for compatibility
import type {
  ExtensionState as SetupModalExtensionState,
  ServerState as SetupModalServerState,
} from './onboarding/types';

// Forward declaration for BodhiExtClient to avoid circular dependency
// The actual class is defined in core.ts
export declare class BodhiExtClient {
  constructor(extensionId: string);
  getExtensionId(): string;
  sendApiRequest(
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse>;
  sendStreamRequest(
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<AsyncIterable<StreamChunk>>;
  ping(): Promise<{ message: string }>;
  // getServerState() method removed - now internal to BodhiPlatform
}

/**
 * Response object returned by API requests through the browser extension.
 *
 * @example
 * ```typescript
 * const response: ApiResponse = await client.sendApiRequest('GET', '/ping');
 * console.log('Status:', response.status);
 * console.log('Headers:', response.headers);
 * console.log('Body:', response.body);
 * ```
 */
export interface ApiResponse {
  /** The response body data (parsed JSON or raw data) */
  body: any;
  /** HTTP response headers as key-value pairs */
  headers: Record<string, string>;
  /** HTTP status code (200, 404, 500, etc.) */
  status: number;
}

/**
 * Individual chunk received from streaming API requests.
 *
 * Streaming responses are delivered as a series of chunks, each containing
 * partial data. Headers and status are typically only present in the first chunk.
 *
 * @example
 * ```typescript
 * const stream = await client.sendStreamRequest('POST', '/v1/chat/completions', {
 *   model: 'llama3',
 *   messages: [{ role: 'user', content: 'Hello!' }],
 *   stream: true
 * });
 *
 * for await (const chunk: StreamChunk of stream) {
 *   if (chunk.body?.choices?.[0]?.delta?.content) {
 *     process.stdout.write(chunk.body.choices[0].delta.content);
 *   }
 * }
 * ```
 */
export interface StreamChunk {
  /** The chunk body data (typically contains partial response data) */
  body: any;
  /** HTTP response headers (usually only present in first chunk) */
  headers?: Record<string, string>;
  /** HTTP status code (usually only present in first chunk) */
  status?: number;
}

/**
 * Extension state compatible with setup-modal.
 * Discriminated union with 'ready' | 'unreachable' | 'not-installed' | 'unsupported' statuses.
 */
export type ExtensionState = SetupModalExtensionState;

/**
 * Server state information for BodhiPlatform detection.
 *
 * This interface represents the current state of the Bodhi App server,
 * including setup status, version information, and error details.
 *
 * This is now internal to BodhiPlatform - users should use the simplified
 * isReady() method instead of handling discriminated union states directly.
 */
/**
 * Server state compatible with setup-modal.
 * Discriminated union with detailed status and error information.
 */
export type ServerState = SetupModalServerState;

/**
 * Public platform state interface providing simplified interface.
 *
 * This interface defines the public API for checking platform readiness.
 * Implementations should provide the isReady() method to determine if both
 * extension and server components are ready for API communication.
 *
 * @example
 * ```typescript
 * const platform = new BodhiPlatform();
 * const state = await platform.initialize();
 *
 * if (state.isReady()) {
 *   const client = platform.getClient();
 *   const response = await client.sendApiRequest('GET', '/ping');
 * } else {
 *   platform.showOnboarding();
 * }
 * ```
 */
export interface BodhiPlatformState {
  /**
   * Checks if the platform is ready for API communication.
   *
   * @returns True if both extension and server are ready
   */
  isReady(): boolean;
}

/**
 * Internal platform state interface.
 * Contains detailed extension and server state information and implements BodhiPlatformState.
 * @internal
 */
export interface InternalPlatformState extends BodhiPlatformState {
  /** Extension state details */
  extension: ExtensionState;
  /** Server state details */
  server: ServerState;

  /**
   * Internal method to access the full state (for BodhiPlatform class use only).
   * @internal
   */
  _getInternalState(): InternalPlatformState;
}

/**
 * Factory function to create platform state objects.
 *
 * Creates a platform state object that implements both the public BodhiPlatformState
 * interface and the internal InternalPlatformState interface.
 *
 * @param extension - Extension state
 * @param server - Server state
 * @returns Platform state object implementing BodhiPlatformState
 */
export function createPlatformState(
  extension: ExtensionState,
  server: ServerState
): InternalPlatformState {
  const state: InternalPlatformState = {
    extension,
    server,

    isReady(): boolean {
      return this.extension.status === 'ready' && this.server.status === 'ready';
    },

    _getInternalState(): InternalPlatformState {
      return this;
    },
  };

  return state;
}

/**
 * Server state information returned by the /bodhi/v1/info endpoint.
 *
 * This interface represents the current state of the Bodhi App server,
 * including setup status, version information, and error details.
 *
 */
export interface ServerStateInfo {
  /** Current server status */
  status: 'setup' | 'ready' | 'resource-admin' | 'error' | 'unreachable';
  /** Server version (if available) */
  version?: string;
  /** Server URL */
  url?: string;
  /** Error details if status is 'error' or 'unreachable' */
  error?: {
    /** Error message */
    message: string;
    /** Error type */
    type?: string;
    /** Error code */
    code?: string;
    /** Parameter that caused the error */
    param?: string;
  };
}

/**
 * Request parameters for chat completion API calls.
 *
 * This interface is compatible with the OpenAI Chat Completions API format,
 * allowing easy migration from OpenAI to local LLM services.
 *
 * @example
 * ```typescript
 * const request: ChatRequest = {
 *   model: 'llama3',
 *   messages: [
 *     { role: 'system', content: 'You are a helpful assistant.' },
 *     { role: 'user', content: 'What is the capital of France?' }
 *   ],
 *   temperature: 0.7,
 *   max_tokens: 150,
 *   stream: true
 * };
 *
 * const stream = await client.sendStreamRequest('POST', '/v1/chat/completions', request);
 * ```
 */
export interface ChatRequest {
  /** The model identifier to use for the completion */
  model: string;
  /** Array of messages representing the conversation history */
  messages: ChatMessage[];
  /** Whether to stream the response (default: false) */
  stream?: boolean;
  /** Controls randomness in the output (0.0 to 2.0, default varies by model) */
  temperature?: number;
  /** Maximum number of tokens to generate */
  max_tokens?: number;
  /** Controls diversity via nucleus sampling (0.0 to 1.0) */
  top_p?: number;
  /** Penalizes frequent tokens (-2.0 to 2.0) */
  frequency_penalty?: number;
  /** Penalizes repeated tokens (-2.0 to 2.0) */
  presence_penalty?: number;
  /** Sequences where the API will stop generating tokens */
  stop?: string | string[];
}

/**
 * Individual message in a chat conversation.
 *
 * @example
 * ```typescript
 * const messages: ChatMessage[] = [
 *   { role: 'system', content: 'You are a helpful coding assistant.' },
 *   { role: 'user', content: 'How do I create a TypeScript interface?' },
 *   { role: 'assistant', content: 'You can create a TypeScript interface using...' }
 * ];
 * ```
 */
export interface ChatMessage {
  /** The role of the message sender */
  role: 'system' | 'user' | 'assistant';
  /** The content of the message */
  content: string;
}

/**
 * Complete response from a non-streaming chat completion request.
 *
 * This is the final response format when stream is false or not specified.
 * Compatible with OpenAI's chat completion response format.
 *
 * @example
 * ```typescript
 * const response = await client.sendApiRequest('POST', '/v1/chat/completions', {
 *   model: 'llama3',
 *   messages: [{ role: 'user', content: 'Hello!' }]
 * });
 *
 * const chatResponse = response.body as ChatResponse;
 * console.log('Response:', chatResponse.choices[0].message.content);
 * console.log('Tokens used:', chatResponse.usage.total_tokens);
 * ```
 */
export interface ChatResponse {
  /** Unique identifier for the chat completion */
  id: string;
  /** Object type (always "chat.completion") */
  object: string;
  /** Unix timestamp of when the completion was created */
  created: number;
  /** The model used for the completion */
  model: string;
  /** Array of completion choices (usually contains one choice) */
  choices: Array<{
    /** Index of the choice (0 for single completions) */
    index: number;
    /** The generated message */
    message: {
      /** Role of the generated message (always "assistant") */
      role: string;
      /** The generated content */
      content: string;
    };
    /** Reason why the completion finished */
    finish_reason: string;
  }>;
  /** Token usage statistics */
  usage: {
    /** Number of tokens in the prompt */
    prompt_tokens: number;
    /** Number of tokens in the completion */
    completion_tokens: number;
    /** Total tokens used (prompt + completion) */
    total_tokens: number;
  };
}

/**
 * Individual chunk from a streaming chat completion response.
 *
 * When streaming is enabled, the response is delivered as a series of chunks,
 * each containing incremental data. The delta contains only the new content
 * since the previous chunk.
 *
 * @example
 * ```typescript
 * const stream = await client.sendStreamRequest('POST', '/v1/chat/completions', {
 *   model: 'llama3',
 *   messages: [{ role: 'user', content: 'Write a poem' }],
 *   stream: true
 * });
 *
 * for await (const chunk of stream) {
 *   const chatChunk = chunk.body as ChatChunk;
 *   const content = chatChunk.choices[0]?.delta?.content;
 *   if (content) {
 *     process.stdout.write(content);
 *   }
 * }
 * ```
 */
export interface ChatChunk {
  /** Unique identifier for the chat completion */
  id: string;
  /** Object type (always "chat.completion.chunk") */
  object: string;
  /** Unix timestamp of when the chunk was created */
  created: number;
  /** The model used for the completion */
  model: string;
  /** Array of choice deltas (usually contains one choice) */
  choices: Array<{
    /** Index of the choice (0 for single completions) */
    index: number;
    /** Delta containing incremental changes */
    delta: {
      /** Role (only present in first chunk) */
      role?: string;
      /** Incremental content since last chunk */
      content?: string;
    };
    /** Reason why the completion finished (only in final chunk) */
    finish_reason?: string;
  }>;
}

/**
 * Interface for the window.bodhiext object provided by the browser extension.
 *
 * This interface defines the low-level API exposed by the Bodhi browser extension
 * on the window object. Most users should use the BodhiExtClient class instead of
 * accessing this interface directly.
 *
 * @internal
 */
export interface WindowBodhiExt {
  /**
   * The extension ID (may be null during initialization)
   * @deprecated Use getExtensionId() method instead
   */
  extension_id: string | null;

  /**
   * Asynchronously retrieves the extension ID.
   * @returns Promise resolving to the extension's unique identifier
   */
  getExtensionId(): Promise<string>;

  /**
   * Sends an HTTP API request through the extension.
   * @param method - HTTP method
   * @param endpoint - API endpoint path
   * @param body - Request body data
   * @param headers - HTTP headers
   * @returns Promise resolving to the API response
   */
  sendApiRequest(
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse>;

  /**
   * Sends a streaming HTTP API request through the extension.
   * @param method - HTTP method
   * @param endpoint - API endpoint path
   * @param body - Request body data
   * @param headers - HTTP headers
   * @returns Promise resolving to an AsyncIterable of response chunks
   */
  sendStreamRequest(
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<AsyncIterable<StreamChunk>>;

  /**
   * Sends a ping request to test connectivity.
   * @returns Promise resolving to a ping response
   */
  ping(): Promise<{ message: string }>;

  /**
   * Gets server state information from /bodhi/v1/info endpoint.
   * @returns Promise resolving to server state information
   */
  serverState(): Promise<ServerStateInfo>;
}

/**
 * Configuration for BodhiPlatform initialization.
 *
 * This interface defines all configuration options available when creating
 * a BodhiPlatform instance, including timeout settings, onboarding preferences,
 * and analytics options.
 *
 * @example
 * ```typescript
 * const config: BodhiConfig = {
 *   timeout: 15000
 * };
 *
 * const platform = new BodhiPlatform(config);
 * const state = await platform.initialize();
 *
 * if (state.isReady()) {
 *   // Platform ready for use
 * } else {
 *   // Show onboarding
 *   platform.showOnboarding({
 *     dismissible: true,
 *     callbacks: {
 *       onComplete: () => console.log('Setup complete'),
 *       onRedetection: () => platform.initialize()
 *     }
 *   });
 * }
 * ```
 */
export interface BodhiConfig {
  /** Maximum time to wait for extension detection in milliseconds (default: 10000) */
  timeout?: number;
}

/**
 * Global window interface extension for TypeScript support.
 *
 * This declaration extends the global Window interface to include the
 * bodhiext property, enabling proper TypeScript support when accessing
 * window.bodhiext in browser environments.
 */
declare global {
  interface Window {
    /** The Bodhi browser extension API (available when extension is loaded) */
    bodhiext?: WindowBodhiExt;
  }
}
