import type {
  ApiResponse,
  StreamChunk,
  ExtensionState,
  ServerState,
  BodhiConfig,
  BodhiPlatformState,
  InternalPlatformState,
} from './types';
import { createPlatformState } from './types';
import {
  BodhiOnboardingModal,
  OnboardingModalConfig,
  PlatformStateProvider,
  SERVER_UNEXPECTED_ERROR,
  SERVER_CONN_REFUSED,
  SERVER_IN_SETUP_STATUS,
  SERVER_IN_ADMIN_STATUS,
  SERVER_PENDING_EXT_READY,
  EXT_NOT_INSTALLED,
  EXT_CONNECTION_FAILED,
} from './onboarding';

/**
 * Main platform class for managing Bodhi browser extension and server integration.
 *
 * The BodhiPlatform provides a unified interface for initializing, managing, and
 * interacting with the Bodhi Platform components. It handles extension detection,
 * server state management, and provides a clean API for applications to integrate
 * with the Bodhi ecosystem.
 *
 * @example Basic Usage
 * ```typescript
 * import { BodhiPlatform } from '@bodhiapp/bodhijs';
 *
 * async function main() {
 *   // Create platform with default configuration
 *   const platform = new BodhiPlatform({ timeout: 3000 });
 *
 *   // Initialize and get platform state
 *   const state = await platform.initialize();
 *
 *   if (state.isReady()) {
 *     const client = platform.getClient();
 *     const ping = await client.ping();
 *     console.log('Platform ready:', ping.message);
 *   } else {
 *     platform.showOnboarding({
 *       dismissible: true,
 *       callbacks: {
 *         onClose: (state, action) => {
 *           if (action === 'complete') {
 *             console.log('Setup completed');
 *             // Re-initialize platform after successful setup
 *             platform.initialize();
 *           } else {
 *             console.log('Modal dismissed');
 *           }
 *         }
 *       }
 *     });
 *   }
 * }
 * ```
 */
export class BodhiPlatform implements PlatformStateProvider {
  private config: BodhiConfig;
  private currentState: BodhiPlatformState | null = null;
  private client: BodhiExtClient | null = null;
  private onboardingModal: BodhiOnboardingModal | null = null;

  /**
   * Creates a new BodhiPlatform instance.
   *
   * @param config - Configuration options for the platform
   *
   * @example
   * ```typescript
   * // With timeout
   * const platform = new BodhiPlatform({
   *   timeout: 15000
   * });
   * ```
   */
  constructor(config: BodhiConfig = {}) {
    this.config = this.mergeDefaultConfig(config);
  }

  /**
   * Initializes the platform by detecting extension and server states.
   *
   * This method performs sequential detection: first checks for the extension,
   * then (if extension is ready) checks server state. It never throws errors,
   * instead returning appropriate state information.
   *
   * @returns Promise resolving to the current platform state
   *
   */
  async initialize(): Promise<BodhiPlatformState> {
    try {
      // Step 1: Detect extension state
      const extensionState = await loadExtensionState(this.config.timeout || 10000);

      // Step 2: If extension is ready, detect server state and create client
      if (extensionState.status === 'ready') {
        const client = new BodhiExtClient(extensionState.id!);
        const serverState = await this.getServerStateInternal(client);

        // Build platform state and store client
        this.currentState = this.buildPlatformState(extensionState, serverState);
        this.client = client;
      } else {
        // Extension not ready, build state with pending server status
        const serverState: ServerState = {
          status: 'pending-extension-ready',
          error: {
            message: 'Server detection requires extension setup first',
            code: SERVER_PENDING_EXT_READY,
          },
        };
        this.currentState = this.buildPlatformState(extensionState, serverState);
        this.client = null;
      }

      return this.currentState;
    } catch (error) {
      // Should never throw, but provide fallback error state
      const errorState = createPlatformState(
        {
          status: 'not-installed',
          error: {
            message: error instanceof Error ? error.message : 'Unknown initialization error',
            code: EXT_CONNECTION_FAILED,
          },
        },
        {
          status: 'pending-extension-ready',
          error: {
            message: 'Server detection requires extension setup first',
            code: SERVER_PENDING_EXT_READY,
          },
        }
      );

      this.currentState = errorState;
      this.client = null;
      return errorState;
    }
  }

  /**
   * Gets the BodhiExtClient instance.
   *
   * Returns the client only when the platform has been initialized and the
   * extension is functional. Throws an error if the platform is not initialized
   * or the client is not available.
   *
   * @returns BodhiExtClient instance
   * @throws {Error} If platform not initialized or client not available
   */
  getClient(): BodhiExtClient {
    if (!this.client) {
      throw new Error(
        'Platform not initialized or extension not available. Call initialize() first and ensure platform status is ready.'
      );
    }
    return this.client;
  }

  /**
   * Shows the onboarding modal interface.
   *
   * This method displays the onboarding modal to guide users through
   * setting up the required components. The modal uses iframe srcdoc
   * for CSP compliance and self-contained content.
   *
   * @param config - Optional configuration for modal behavior
   *
   * @example
   * ```typescript
   * const platform = new BodhiPlatform();
   * const state = await platform.initialize();
   *
   * if (!state.isReady()) {
   *   platform.showOnboarding({
   *     dismissible: true,
   *     callbacks: {
   *       onClose: (state, action) => {
   *         if (action === 'complete') {
   *           console.log('Setup complete');
   *           // Re-initialize platform after successful setup
   *           platform.initialize();
   *         } else {
   *           console.log('Modal dismissed');
   *         }
   *       }
   *     }
   *   });
   * }
   * ```
   */
  async getSetupState(): Promise<InternalPlatformState> {
    const state = await this.initialize();
    return state as InternalPlatformState;
  }

  showOnboarding(config: OnboardingModalConfig = {}): void {
    if (!this.currentState) {
      throw new Error('Platform not initialized. Call initialize() first.');
    }

    // Hide existing modal if present
    if (this.onboardingModal) {
      this.onboardingModal.hide();
    }

    // Set defaults for missing config
    const modalConfig: OnboardingModalConfig = {
      dismissible: config.dismissible ?? true,
      callbacks: {
        onClose: config.callbacks?.onClose,
      },
    };

    // Create and show modal
    this.onboardingModal = new BodhiOnboardingModal(
      this, // Pass the platform instance as state provider
      modalConfig
    );
    this.onboardingModal.show();
  }

  /**
   * Builds platform state from extension and server states.
   *
   * @private
   * @param extension - Extension state
   * @param server - Server state
   * @returns Combined platform state
   */
  private buildPlatformState(extension: ExtensionState, server: ServerState): BodhiPlatformState {
    return createPlatformState(extension, server);
  }

  /**
   * Gets server state information using the client.
   *
   * @private
   * @param client - BodhiExtClient instance
   * @returns Promise resolving to server state
   */
  private async getServerStateInternal(client: BodhiExtClient): Promise<ServerState> {
    return await client.getServerState();
  }

  /**
   * Merges user configuration with default values.
   *
   * @private
   * @param config - User-provided configuration
   * @returns Merged configuration with defaults
   */
  private mergeDefaultConfig(config: BodhiConfig): BodhiConfig {
    return {
      timeout: config.timeout ?? 10000,
    };
  }
}

/**
 * Main client class for interacting with the Bodhi browser extension.
 *
 * The BodhiExtClient provides a typed, promise-based interface for communicating with
 * the Bodhi browser extension. It handles API requests, streaming responses, and
 * error management while abstracting away the underlying window.bodhiext interface.
 *
 * @example
 * ```typescript
 * // ...
 *   const client = platform.getClient();
 *
 *   // Make API requests
 *   const response = await client.sendApiRequest('GET', '/ping');
 *
 *   // Use streaming requests
 *   const stream = await client.sendStreamRequest('POST', '/v1/chat/completions', {
 *     model: 'llama3',
 *     messages: [{ role: 'user', content: 'Hello!' }],
 *     stream: true
 *   });
 *
 *   for await (const chunk of stream) {
 *     console.log(chunk.body);
 *   }
 * // ...
 * ```
 */
export class BodhiExtClient {
  private extensionId: string;

  /**
   * Creates a new BodhiExtClient instance.
   *
   * Note: You should typically use `BodhiPlatform.getClient()` instead of constructing
   * this class directly, as BodhiPlatform handles extension detection and state management.
   *
   * @param extensionId - The unique identifier of the browser extension
   */
  constructor(extensionId: string) {
    this.extensionId = extensionId;
  }

  /**
   * Gets the cached extension ID.
   *
   * @returns The unique identifier of the browser extension
   */
  getExtensionId(): string {
    return this.extensionId;
  }

  /**
   * Sends a standard HTTP API request through the browser extension.
   *
   * This method provides a generic interface for making HTTP requests to the
   * configured backend server through the browser extension. The extension
   * handles authentication, CORS, and other browser security restrictions.
   *
   * @param method - HTTP method (GET, POST, PUT, DELETE, etc.)
   * @param endpoint - API endpoint path (e.g., '/v1/chat/completions')
   * @param body - Request body data (will be JSON stringified if object)
   * @param headers - Additional HTTP headers to include
   * @returns Promise resolving to the API response
   *
   * @throws {Error} If the extension is not available
   *
   * @example
   * ```typescript
   *   // Simple GET request
   *   const pingResponse = await client.sendApiRequest('GET', '/ping');
   *
   *   // POST request with JSON body
   *   const chatResponse = await client.sendApiRequest('POST', '/v1/chat/completions', {
   *     model: 'llama3',
   *     messages: [{ role: 'user', content: 'Hello!' }]
   *   }, {
   *     'Content-Type': 'application/json'
   *   });
   * ```
   */
  async sendApiRequest(
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse> {
    this.ensureExtensionAvailable();
    return window.bodhiext!.sendApiRequest(method, endpoint, body, headers);
  }

  /**
   * Sends a streaming HTTP API request through the browser extension.
   *
   * This method enables real-time streaming responses, particularly useful for
   * chat completions and other long-running operations. The response is returned
   * as an AsyncIterable that yields chunks as they arrive.
   *
   * @param method - HTTP method (typically POST for streaming endpoints)
   * @param endpoint - API endpoint path (e.g., '/v1/chat/completions')
   * @param body - Request body data (should include stream: true for streaming endpoints)
   * @param headers - Additional HTTP headers to include
   * @returns Promise resolving to an AsyncIterable of response chunks
   *
   * @throws {Error} If the extension is not available
   *
   * @example
   * ```typescript
   *   const client = platform.getClient();
   *
   *   // Streaming chat completion
   *   const stream = await client.sendStreamRequest('POST', '/v1/chat/completions', {
   *     model: 'llama3',
   *     messages: [{ role: 'user', content: 'Write a poem about AI' }],
   *     stream: true
   *   });
   *
   *   // Process chunks as they arrive
   *   for await (const chunk of stream) {
   *     if (chunk.body?.choices?.[0]?.delta?.content) {
   *       process.stdout.write(chunk.body.choices[0].delta.content);
   *     }
   *   }
   * ```
   */
  async sendStreamRequest(
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<AsyncIterable<StreamChunk>> {
    this.ensureExtensionAvailable();
    return window.bodhiext!.sendStreamRequest(method, endpoint, body, headers);
  }

  /**
   * Sends a ping request to test connectivity with the extension and backend server.
   *
   * This is useful for health checks and verifying that the extension is properly
   * configured and can communicate with the backend server.
   *
   * @returns Promise resolving to a ping response object
   *
   * @throws {Error} If the extension is not available or the ping fails
   *
   */
  async ping(): Promise<{ message: string }> {
    this.ensureExtensionAvailable();
    return window.bodhiext!.ping();
  }

  /**
   * Gets server state information from the backend server.
   *
   * This method queries the server status and maps the response to the library's
   * ServerState format for consistent handling across the platform.
   *
   * @returns Promise resolving to server state information
   *
   * @throws {Error} If the extension is not available
   */
  async getServerState(): Promise<ServerState> {
    this.ensureExtensionAvailable();
    
    try {
      const serverInfo = await window.bodhiext!.serverState();

      // Map ServerStateInfo to ServerState format based on status
      switch (serverInfo.status) {
        case 'ready':
          return {
            status: 'ready',
            version: serverInfo.version || '1.0.0',
          };
        case 'setup':
          return {
            status: 'setup',
            version: serverInfo.version || '1.0.0',
            error: {
              message: serverInfo.error?.message || 'Server requires initial setup',
              code: SERVER_IN_SETUP_STATUS,
            },
          };
        case 'resource-admin':
          return {
            status: 'resource-admin',
            version: serverInfo.version || '1.0.0',
            error: {
              message:
                serverInfo.error?.message || 'Server is in admin mode for resource management',
              code: SERVER_IN_ADMIN_STATUS,
            },
          };
        case 'unreachable':
          return {
            status: 'unreachable',
            error: {
              message: serverInfo.error?.message || 'Server not reachable',
              code: SERVER_CONN_REFUSED,
            },
          };
        default:
          return {
            status: 'error',
            error: {
              message: serverInfo.error?.message || 'Unknown server error',
              code: SERVER_UNEXPECTED_ERROR,
            },
          };
      }
    } catch (error) {
      return {
        status: 'error',
        error: {
          message: error instanceof Error ? error.message : 'Unknown server error',
          code: SERVER_UNEXPECTED_ERROR,
        },
      };
    }
  }

  /**
   * Ensures the browser extension is available before making API calls.
   *
   * @private
   * @throws {Error} If window.bodhiext is not available
   */
  private ensureExtensionAvailable(): void {
    if (typeof window.bodhiext === 'undefined') {
      throw new Error(
        'Bodhi extension not available. Extension must be loaded before creating BodhiExtClient.'
      );
    }
  }
}

/**
 * Load extension state without throwing errors.
 *
 * This function provides detailed extension state information, returning structured
 * state instead of throwing exceptions. This is now a private implementation detail
 * used by BodhiPlatform.
 *
 * @private
 * @param timeout - Maximum time to wait for extension detection in milliseconds (default: 10000)
 * @returns Promise resolving to detailed extension state
 */
async function loadExtensionState(timeout: number = 10000): Promise<ExtensionState> {
  try {
    // Step 1: Check if window.bodhiext exists
    const extensionDetected = await new Promise<boolean>(resolve => {
      const startTime = Date.now();

      const checkExtension = () => {
        if (typeof window.bodhiext !== 'undefined') {
          resolve(true);
          return;
        }

        if (Date.now() - startTime >= timeout) {
          resolve(false);
          return;
        }

        setTimeout(checkExtension, 100);
      };

      checkExtension();
    });

    if (!extensionDetected) {
      return {
        status: 'not-installed',
        error: {
          message: 'Extension not installed',
          code: EXT_NOT_INSTALLED,
        },
      };
    }

    // Step 2: Try to get extension ID to verify it's functional
    try {
      const extensionIdPromise = window.bodhiext!.getExtensionId();
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Extension ID timeout')), timeout);
      });

      const extensionId = await Promise.race([extensionIdPromise, timeoutPromise]);

      return {
        status: 'ready',
        version: '1.0.0', // Default version
        id: extensionId,
      };
    } catch (error) {
      return {
        status: 'unreachable',
        error: {
          message: error instanceof Error ? error.message : 'Extension not responding',
          code: EXT_CONNECTION_FAILED,
        },
      };
    }
  } catch (error) {
    return {
      status: 'not-installed',
      error: {
        message: error instanceof Error ? error.message : 'Extension detection failed',
        code: EXT_CONNECTION_FAILED,
      },
    };
  }
}
