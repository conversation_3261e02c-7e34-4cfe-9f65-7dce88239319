/**
 * @fileoverview Bodhi JS Library - Client-based interface for Bodhi browser extension
 *
 * This library provides a clean, typed interface for interacting with the Bodhi browser extension.
 * It follows proven patterns from the extension's internal implementation and provides comprehensive
 * TypeScript support for extension detection, API requests, and streaming responses.
 *
 * @example Basic Usage
 * ```typescript
 * import { BodhiPlatform } from '@bodhiapp/bodhijs';
 *
 * async function main() {
 *   // Create platform with default configuration
 *   const platform = new BodhiPlatform();
 *
 *   // Initialize and get platform state
 *   const state = await platform.initialize();
 *
 *   if (state.isReady()) {
 *     const client = platform.getClient();
 *
 *     // Test connectivity
 *     const ping = await client.ping();
 *     console.log('Server status:', ping.message);
 *
 *     // Make API requests
 *     const response = await client.sendApiRequest('POST', '/v1/chat/completions', {
 *       model: 'llama3',
 *       messages: [{ role: 'user', content: 'Hello!' }]
 *     });
 *
 *     console.log('Response:', response.body);
 *   } else {
 *     console.log('Platform needs setup - showing onboarding');
 *     platform.showOnboarding();
 *   }
 * }
 * ```
 *
 * <AUTHOR> Search
 * @license MIT
 */

import { BodhiExtClient, BodhiPlatform } from './core';
import { BodhiOnboardingModal } from './onboarding';
import type {
  ApiResponse,
  StreamChunk,
  ChatRequest,
  ChatResponse,
  ChatChunk,
  ChatMessage,
  BodhiPlatformState,
  BodhiConfig,
} from './types';
import type { OnboardingModalConfig, OnboardingModalCallbacks, CloseAction } from './onboarding';

/**
 * Main platform class for managing Bodhi browser extension and server integration.
 *
 * This is the recommended entry point for applications integrating with Bodhi Platform.
 */
export { BodhiPlatform };

/**
 * Main client class for interacting with the Bodhi browser extension.
 *
 * Typically accessed through BodhiPlatform.getClient() after initialization.
 */
export { BodhiExtClient };

/**
 * Modal component for Bodhi onboarding with iframe srcdoc approach.
 *
 * Provides self-contained modal with CSP-compliant content generation.
 */
export { BodhiOnboardingModal };

// All system detection utilities, URL helpers, configuration constants,
// and error codes are now internal implementation details

// Type exports with documentation

/**
 * Response object returned by API requests through the browser extension.
 */
export type { ApiResponse };

/**
 * Individual chunk received from streaming API requests.
 */
export type { StreamChunk };

/**
 * Request parameters for chat completion API calls (OpenAI-compatible).
 */
export type { ChatRequest };

/**
 * Complete response from a non-streaming chat completion request.
 */
export type { ChatResponse };

/**
 * Individual chunk from a streaming chat completion response.
 */
export type { ChatChunk };

/**
 * Individual message in a chat conversation.
 */
export type { ChatMessage };

// Internal types WindowBodhiExt, ServerStateInfo, ExtensionState,
// and ServerState are no longer exported - they are implementation details

/**
 * Overall platform state combining extension and server states.
 */
export type { BodhiPlatformState };

/**
 * Configuration for BodhiPlatform initialization.
 */
export type { BodhiConfig };

/**
 * Configuration for onboarding modal behavior.
 */
export type { OnboardingModalConfig };

/**
 * Callback functions for onboarding modal events.
 */
export type { OnboardingModalCallbacks };

/**
 * Action type for modal close events.
 */
export type { CloseAction };
