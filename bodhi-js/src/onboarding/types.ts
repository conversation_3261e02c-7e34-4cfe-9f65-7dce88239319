// Browser and OS type definitions
export type BrowserType = 'chrome' | 'edge' | 'firefox' | 'safari' | 'unknown';
export type OSType = 'macos' | 'windows' | 'linux' | 'unknown';

// Step enum for navigation
export enum SetupStep {
  PLATFORM_CHECK = 'platform-check',
  EXTENSION_SETUP = 'extension-setup',
  SERVER_SETUP = 'server-setup',
  COMPLETE = 'complete'
}

// Message type constants
export type ModalMessageType = 'modal:state';
export const MODAL_MESSAGE_STATE: ModalMessageType = 'modal:state';

export type ModalOutMessageType = 'modal_out:ready' | 'modal_out:refresh' | 'modal_out:complete' | 'modal_out:close';
export const MODAL_OUT_READY: ModalOutMessageType = 'modal_out:ready';
export const MODAL_OUT_REFRESH: ModalOutMessageType = 'modal_out:refresh';
export const MODAL_OUT_COMPLETE: ModalOutMessageType = 'modal_out:complete';
export const MODAL_OUT_CLOSE: ModalOutMessageType = 'modal_out:close';

// Error code constants
export type ServerErrorCode = 'server-pending-ext-ready' | 'server-conn-refused' | 'server-conn-timeout' | 'server-not-found' | 'server-network-unreachable' | 'server-service-unavailable' | 'server-unexpected-error' | 'server-in-setup-status' | 'server-in-admin-status';
export type ExtensionErrorCode = 'ext-not-installed' | 'ext-connection-failed' | 'ext-unsupported-version';

// Server error code constants
export const SERVER_PENDING_EXT_READY: ServerErrorCode = 'server-pending-ext-ready';
export const SERVER_CONN_REFUSED: ServerErrorCode = 'server-conn-refused';
export const SERVER_CONN_TIMEOUT: ServerErrorCode = 'server-conn-timeout';
export const SERVER_NOT_FOUND: ServerErrorCode = 'server-not-found';
export const SERVER_NETWORK_UNREACHABLE: ServerErrorCode = 'server-network-unreachable';
export const SERVER_SERVICE_UNAVAILABLE: ServerErrorCode = 'server-service-unavailable';
export const SERVER_UNEXPECTED_ERROR: ServerErrorCode = 'server-unexpected-error';
export const SERVER_IN_SETUP_STATUS: ServerErrorCode = 'server-in-setup-status';
export const SERVER_IN_ADMIN_STATUS: ServerErrorCode = 'server-in-admin-status';

// Extension error code constants
export const EXT_NOT_INSTALLED: ExtensionErrorCode = 'ext-not-installed';
export const EXT_CONNECTION_FAILED: ExtensionErrorCode = 'ext-connection-failed';
export const EXT_UNSUPPORTED_VERSION: ExtensionErrorCode = 'ext-unsupported-version';

// Environment state interface
export interface EnvState {
  os: OSType;
  browser: BrowserType;
}

// Browser platform definitions
export interface SupportedBrowser {
  id: BrowserType;
  status: 'supported';
  name: string;
  extension_url: string;
}

export interface NotSupportedBrowser {
  id: BrowserType;
  status: 'not-supported';
  name: string;
  github_issue_url?: string;
}

export type Browser = SupportedBrowser | NotSupportedBrowser;

// OS platform definitions
export interface SupportedOS {
  id: OSType;
  status: 'supported';
  name: string;
  download_url: string;
}

export interface NotSupportedOS {
  id: OSType;
  status: 'not-supported';
  name: string;
  github_issue_url?: string;
}

export type OS = SupportedOS | NotSupportedOS;

// Extension state interfaces
export interface ExtensionStateReady {
  /** Current extension status */
  status: 'ready';
  /** Extension version */
  version: string;
  /** Extension ID (always present when ready) */
  id: string;
}

export interface ExtensionStateNotReady {
  /** Current extension status */
  status: 'unreachable' | 'not-installed' | 'unsupported';
  /** Error details */
  error: {
    /** Error message */
    message: string;
    /** Error code */
    code: ExtensionErrorCode;
  };
}

export type ExtensionState = ExtensionStateReady | ExtensionStateNotReady;

// Server state interfaces
export interface ServerStateReady {
  /** Current server status */
  status: 'ready';
  /** Server version */
  version: string;
}

export interface ServerStateReachable {
  /** Current server status */
  status: 'setup' | 'resource-admin';
  /** Server version */
  version: string;
  /** Error details */
  error: {
    /** Error message */
    message: string;
    /** Error code */
    code: ServerErrorCode;
  };
}

export interface ServerStatePending {
  /** Current server status */
  status: 'pending-extension-ready';
  /** Error details */
  error: {
    /** Error message */
    message: string;
    /** Error code */
    code: ServerErrorCode;
  };
}

export interface ServerStateUnreachable {
  /** Current server status */
  status: 'unreachable';
  /** Error details */
  error: {
    /** Error message */
    message: string;
    /** Error code */
    code: ServerErrorCode;
  };
}

export interface ServerStateError {
  /** Current server status */
  status: 'error';
  /** Error details */
  error: {
    /** Error message */
    message: string;
    /** Error code */
    code: ServerErrorCode;
  };
}

export type ServerState = ServerStateReady | ServerStateReachable | ServerStatePending | ServerStateUnreachable | ServerStateError;

// Main setup state interface
export interface SetupState {
  /** Extension state details */
  extension: ExtensionState;
  /** Server state details */
  server: ServerState;
  /** Environment detection */
  env: EnvState;
  /** Browser platforms list */
  browsers: Browser[];
  /** Operating systems list */
  os: OS[];
}

// Nullable setup state type for loading states
export type SetupStateOrNull = SetupState | null;