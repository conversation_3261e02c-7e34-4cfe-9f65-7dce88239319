/**
 * @fileoverview Onboarding module exports
 *
 * This module provides minimal public exports for the onboarding system.
 * Most functionality is now internal implementation.
 */

// Public modal interface - only what users need
export { BodhiOnboardingModal } from './modal';
export type { OnboardingModalConfig, OnboardingModalCallbacks, CloseAction, PlatformStateProvider } from './modal';

// Internal exports for use by other parts of the library (not public API)

// State generation (used internally by BodhiPlatform)
export { SetupStateGenerator } from './state-generator';

// Detection utilities (used internally)
export { detectBrowser, detectOS } from './detection';
export type { BrowserInfo, OSInfo } from './detection';

// Constants and configuration (used internally)
export {
  EXTENSION_DOWNLOAD_URLS,
  SERVER_DOWNLOAD_URLS,
  BROWSER_SUPPORT,
  OS_SUPPORT,
  PLATFORM_CONFIG,
} from './constants';

// Setup-modal types (used internally)
export type {
  BrowserType,
  OSType,
  SetupStep,
  ModalMessageType,
  ModalOutMessageType,
  ServerErrorCode,
  ExtensionErrorCode,
  EnvState,
  SupportedBrowser,
  NotSupportedBrowser,
  Browser,
  SupportedOS,
  NotSupportedOS,
  OS,
  ExtensionState,
  ExtensionStateReady,
  ExtensionStateNotReady,
  ServerState,
  ServerStateReady,
  ServerStateReachable,
  ServerStatePending,
  ServerStateUnreachable,
  ServerStateError,
  SetupState,
} from './types';

// Error code constants (used internally)
export {
  MODAL_MESSAGE_STATE,
  MODAL_OUT_READY,
  MODAL_OUT_REFRESH,
  MODAL_OUT_COMPLETE,
  MODAL_OUT_CLOSE,
  EXT_NOT_INSTALLED,
  EXT_CONNECTION_FAILED,
  EXT_UNSUPPORTED_VERSION,
  SERVER_PENDING_EXT_READY,
  SERVER_CONN_REFUSED,
  SERVER_CONN_TIMEOUT,
  SERVER_NOT_FOUND,
  SERVER_NETWORK_UNREACHABLE,
  SERVER_SERVICE_UNAVAILABLE,
  SERVER_UNEXPECTED_ERROR,
  SERVER_IN_SETUP_STATUS,
  SERVER_IN_ADMIN_STATUS,
} from './types';
