/**
 * Configuration constants for onboarding modal system
 *
 * This file contains all URLs, templates, and configuration values used
 * in the onboarding system. Update these values as needed for different
 * environments or when actual URLs become available.
 */

import type { <PERSON><PERSON><PERSON>, <PERSON> } from './types';

/**
 * Download URLs for browser extensions
 */
export const EXTENSION_DOWNLOAD_URLS = {
  chrome: 'https://chrome.google.com/webstore/detail/bodhi-extension/placeholder-extension-id',
  edge: 'https://microsoftedge.microsoft.com/addons/detail/bodhi-extension/placeholder-extension-id',
} as const;

/**
 * Download URLs for Bodhi App server by operating system
 */
export const SERVER_DOWNLOAD_URLS = {
  macos: 'https://releases.bodhi.app/macos/latest/bodhi-app.dmg',
} as const;

/**
 * Platform configuration for setup modal
 */
export const PLATFORM_CONFIG = {
  browsers: [
    {
      id: 'chrome' as const,
      status: 'supported' as const,
      name: 'Google Chrome',
      extension_url:
        'https://chrome.google.com/webstore/detail/bodhi-extension/actual-extension-id',
    },
    {
      id: 'edge' as const,
      status: 'supported' as const,
      name: 'Microsoft Edge',
      extension_url:
        'https://microsoftedge.microsoft.com/addons/detail/bodhi-extension/actual-extension-id',
    },
    {
      id: 'firefox' as const,
      status: 'not-supported' as const,
      name: 'Firefox',
      github_issue_url: 'https://github.com/BodhiSearch/bodhi-browser/issues/firefox-support',
    },
    {
      id: 'safari' as const,
      status: 'not-supported' as const,
      name: 'Safari',
      github_issue_url: 'https://github.com/BodhiSearch/bodhi-browser/issues/safari-support',
    },
    {
      id: 'unknown' as const,
      status: 'not-supported' as const,
      name: 'Unknown Browser',
      github_issue_url:
        'https://github.com/BodhiSearch/bodhi-browser/issues/unknown-browser-support',
    },
  ] satisfies Browser[],
  os: [
    {
      id: 'macos' as const,
      status: 'supported' as const,
      name: 'macOS',
      download_url:
        'https://github.com/BodhiSearch/bodhi-server/releases/download/latest/bodhi-server-macos.dmg',
    },
    {
      id: 'windows' as const,
      status: 'not-supported' as const,
      name: 'Windows',
      github_issue_url: 'https://github.com/BodhiSearch/bodhi-browser/issues/windows-support',
    },
    {
      id: 'linux' as const,
      status: 'not-supported' as const,
      name: 'Linux',
      github_issue_url: 'https://github.com/BodhiSearch/bodhi-browser/issues/linux-support',
    },
    {
      id: 'unknown' as const,
      status: 'not-supported' as const,
      name: 'Unknown OS',
      github_issue_url: 'https://github.com/BodhiSearch/bodhi-browser/issues/unknown-os-support',
    },
  ] satisfies OS[],
} as const;

/**
 * Server endpoints configuration
 */
export const SERVER_ENDPOINTS = {
  default: 'http://localhost:1135',
  info: '/bodhi/v1/info',
  health: '/ping',
} as const;

/**
 * Browser support configuration
 */
export const BROWSER_SUPPORT = {
  supported: ['chrome', 'edge'] as const,
  notSupported: ['firefox', 'safari', 'unknown'] as const,
} as const;

/**
 * Operating system support configuration
 */
export const OS_SUPPORT = {
  supported: ['macos'] as const,
  notSupported: ['windows', 'linux', 'unknown'] as const,
} as const;

/**
 * Creates a dynamic unknown browser configuration
 */
export function createUnknownBrowserConfig(browserName: string): Browser {
  // Generate GitHub issue slug from browser name
  const githubSlug = browserName.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

  return {
    id: 'unknown' as const,
    status: 'not-supported' as const,
    name: browserName,
    github_issue_url: `https://github.com/BodhiSearch/bodhi-browser/issues/${githubSlug}-support`,
  };
}
