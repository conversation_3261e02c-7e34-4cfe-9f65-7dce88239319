/**
 * Modal infrastructure for Bodhi onboarding
 * Implements iframe-srcdoc integration following test-iframe-srcdoc pattern
 */

// Import the platform state types
import type { BodhiPlatformState, InternalPlatformState } from '../types';
import modalHtml from './modal.html';
import { SetupStateGenerator } from './state-generator';

export type CloseAction = 'complete' | 'dismiss';

export interface PlatformStateProvider {
  getSetupState(): Promise<InternalPlatformState>;
}

export interface OnboardingModalCallbacks {
  onClose?: (state: BodhiPlatformState, action: CloseAction) => void;
}

export interface OnboardingModalConfig {
  dismissible?: boolean;
  callbacks?: OnboardingModalCallbacks;
}

/**
 * Onboarding modal implementation using iframe-srcdoc integration
 *
 * This modal hosts the setup-modal component inside an iframe using srcdoc,
 * enabling secure communication via postMessage while maintaining isolation.
 *
 * Message flow:
 * 1. Host -> iframe: 'modal:state' with SetupState data
 * 2. iframe -> Host: 'modal_out:ready|refresh|complete|close' with user actions
 */
export class BodhiOnboardingModal {
  private modalElement: HTMLElement | null = null;
  private iframeElement: HTMLIFrameElement | null = null;
  private stateProvider: PlatformStateProvider;
  private config: OnboardingModalConfig;
  private callbacks: OnboardingModalCallbacks;
  private isVisible = false;
  private messageHandler: ((event: MessageEvent) => void) | null = null;
  private keydownHandler: ((event: KeyboardEvent) => void) | null = null;

  constructor(stateProvider: PlatformStateProvider, config: OnboardingModalConfig = {}) {
    this.stateProvider = stateProvider;
    this.config = config;
    this.callbacks = config.callbacks || {};
  }

  /**
   * Show the modal
   */
  show(): void {
    if (this.isVisible) return;

    this.createModal();
    this.setupEventListeners();
    this.isVisible = true;
  }

  /**
   * Hide the modal
   */
  hide(): void {
    if (!this.isVisible) return;

    this.cleanup();
    this.isVisible = false;
  }

  /**
   * Update the modal with fresh platform state
   */
  async updateState(): Promise<void> {
    await this.sendStateToModal();
  }

  /**
   * Check if modal is currently visible
   */
  isModalVisible(): boolean {
    return this.isVisible;
  }

  /**
   * Create the modal DOM structure
   */
  private createModal(): void {
    // Create modal overlay
    this.modalElement = document.createElement('div');
    this.modalElement.setAttribute('data-testid', 'onboarding-modal-overlay');
    this.modalElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10000;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      box-sizing: border-box;
      backdrop-filter: blur(2px);
    `;

    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.setAttribute('data-testid', 'modal-container');
    modalContainer.style.cssText = `
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      width: 100%;
      max-width: 900px;
      min-width: 800px;
      height: 700px;
      max-height: 85vh;
      overflow: hidden;
      position: relative;
      animation: modalSlideIn 0.3s ease-out;
    `;

    // Create iframe with setup-modal content
    this.iframeElement = document.createElement('iframe');
    this.iframeElement.setAttribute('data-testid', 'setup-modal-iframe');
    this.iframeElement.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      display: block;
      border-radius: 12px;
    `;

    // Set security attributes
    this.iframeElement.sandbox.add('allow-scripts', 'allow-same-origin');
    this.iframeElement.srcdoc = modalHtml;

    // Handle iframe load
    this.iframeElement.addEventListener('load', () => {
      // Give iframe time to initialize before sending state
      setTimeout(() => {
        this.sendStateToModal();
      }, 100);
    });

    // Assemble modal
    modalContainer.appendChild(this.iframeElement);
    this.modalElement.appendChild(modalContainer);

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes modalSlideIn {
        from {
          opacity: 0;
          transform: translateY(-20px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }
    `;
    document.head.appendChild(style);

    // Add to DOM
    document.body.appendChild(this.modalElement);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  /**
   * Setup event listeners for modal interactions
   */
  private setupEventListeners(): void {
    if (!this.modalElement) return;

    // Handle messages from iframe
    this.messageHandler = (event: MessageEvent) => {
      // Validate message source and type
      if (!event.data?.type?.startsWith('modal_out:')) return;
      if (event.source !== this.iframeElement?.contentWindow) return;

      const { type } = event.data;

      switch (type) {
        case 'modal_out:ready':
          this.sendStateToModal();
          break;
        case 'modal_out:refresh':
          this.handleRefresh();
          break;
        case 'modal_out:complete':
          this.handleCloseCallback('complete');
          this.hide();
          break;
        case 'modal_out:close':
          this.handleCloseCallback('dismiss');
          this.hide();
          break;
        default:
          console.warn('[BodhiOnboardingModal] Unknown message type:', type);
      }
    };
    window.addEventListener('message', this.messageHandler);
    if (this.config.dismissible !== false) {
      this.modalElement.addEventListener('click', e => {
        if (e.target === this.modalElement) {
          this.handleCloseCallback('dismiss');
          this.hide();
        }
      });
    }
    if (this.config.dismissible !== false) {
      this.keydownHandler = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          this.handleCloseCallback('dismiss');
          this.hide();
        }
      };
      document.addEventListener('keydown', this.keydownHandler);
    }
  }

  /**
   * Handle close callback with fresh state
   */
  private async handleCloseCallback(action: CloseAction): Promise<void> {
    if (this.callbacks.onClose) {
      try {
        const platformState = await this.stateProvider.getSetupState();
        this.callbacks.onClose(platformState, action);
      } catch (error) {
        console.error('[BodhiOnboardingModal] Error getting state for close callback:', error);
      }
    }
  }

  /**
   * Handle refresh request from modal - get fresh platform state and send to modal
   */
  private async handleRefresh(): Promise<void> {
    try {
      await this.sendStateToModal();
    } catch (error) {
      console.error('[BodhiOnboardingModal] Error refreshing platform state:', error);
    }
  }

  /**
   * Send fresh platform state to the iframe modal
   */
  private async sendStateToModal(): Promise<void> {
    if (!this.iframeElement?.contentWindow) {
      console.warn('[BodhiOnboardingModal] Cannot send state: iframe not ready');
      return;
    }
    try {
      const platformState = await this.stateProvider.getSetupState();
      const setupState = SetupStateGenerator.generateSetupState(platformState);
      this.iframeElement.contentWindow.postMessage(
        {
          type: 'modal:state',
          data: setupState,
        },
        '*'
      );
    } catch (error) {
      console.error('[BodhiOnboardingModal] Error getting/sending state to modal:', error);
    }
  }

  /**
   * Clean up modal and restore page state
   */
  private cleanup(): void {
    // Remove modal from DOM
    if (this.modalElement && this.modalElement.parentNode) {
      this.modalElement.parentNode.removeChild(this.modalElement);
      this.modalElement = null;
    }

    // Restore body scroll
    document.body.style.overflow = '';

    // Remove event listeners
    if (this.messageHandler) {
      window.removeEventListener('message', this.messageHandler);
      this.messageHandler = null;
    }

    if (this.keydownHandler) {
      document.removeEventListener('keydown', this.keydownHandler);
      this.keydownHandler = null;
    }

    // Clear iframe reference
    this.iframeElement = null;
  }
}
