// Use internal state types directly
interface InternalPlatformState {
  extension: ExtensionState;
  server: ServerState;
}
import { PLATFORM_CONFIG, createUnknownBrowserConfig } from './constants';
import { detectBrowser, detectOS } from './detection';
import type { Browser, ExtensionState, ServerState, SetupState } from './types';
import {
  EXT_CONNECTION_FAILED,
  EXT_NOT_INSTALLED,
  EXT_UNSUPPORTED_VERSION,
  SERVER_CONN_REFUSED,
  SERVER_CONN_TIMEOUT,
  SERVER_IN_ADMIN_STATUS,
  SERVER_IN_SETUP_STATUS,
  SERVER_NETWORK_UNREACHABLE,
  SERVER_NOT_FOUND,
  SERVER_PENDING_EXT_READY,
  SERVER_SERVICE_UNAVAILABLE,
  SERVER_UNEXPECTED_ERROR,
} from './types';

/**
 * Generates SetupState for the modal from BodhiPlatformState
 */
export class SetupStateGenerator {
  /**
   * Generate SetupState for the modal from BodhiPlatformState
   */
  static generateSetupState(bodhiState: InternalPlatformState): SetupState {
    const browser = detectBrowser();
    const os = detectOS();

    // Prepare browsers list - inject actual browser info if unknown
    let browsersConfig: Browser[] = [...PLATFORM_CONFIG.browsers];
    
    // If detected browser is unknown, replace the static unknown entry with dynamic one
    if (browser.type === 'unknown') {
      // Remove static unknown browser entry if present
      browsersConfig = browsersConfig.filter(b => b.id !== 'unknown');
      // Add dynamic unknown browser configuration with actual browser name
      browsersConfig.push(createUnknownBrowserConfig(browser.name));
    }

    return {
      extension: SetupStateGenerator.convertExtensionState(bodhiState.extension),
      server: SetupStateGenerator.convertServerState(bodhiState.server),
      env: {
        browser: browser.type,
        os: os.type,
      },
      browsers: browsersConfig,
      os: [...PLATFORM_CONFIG.os],
    };
  }

  /**
   * Convert internal extension state to setup-modal ExtensionState
   */
  private static convertExtensionState(
    extensionState: InternalPlatformState['extension']
  ): ExtensionState {
    switch (extensionState.status) {
      case 'ready':
        return {
          status: 'ready',
          version: extensionState.version,
          id: extensionState.id,
        };
      case 'not-installed':
        return {
          status: 'not-installed',
          error: {
            message: 'Bodhi extension is not installed or not detected',
            code: EXT_NOT_INSTALLED,
          },
        };
      case 'unreachable':
        return {
          status: 'unreachable',
          error: {
            message: 'Failed to connect to Bodhi extension',
            code: EXT_CONNECTION_FAILED,
          },
        };
      case 'unsupported':
        return {
          status: 'unsupported',
          error: {
            message: 'Bodhi extension version is not supported',
            code: EXT_UNSUPPORTED_VERSION,
          },
        };
      default:
        return {
          status: 'not-installed',
          error: {
            message: 'Extension status unknown',
            code: EXT_NOT_INSTALLED,
          },
        };
    }
  }

  /**
   * Convert internal server state to setup-modal ServerState
   */
  private static convertServerState(serverState: InternalPlatformState['server']): ServerState {
    switch (serverState.status) {
      case 'ready':
        return {
          status: 'ready',
          version: serverState.version,
        };
      case 'setup':
        return {
          status: 'setup',
          version: serverState.version,
          error: {
            message: 'Bodhi server requires initial setup',
            code: SERVER_IN_SETUP_STATUS,
          },
        };
      case 'resource-admin':
        return {
          status: 'resource-admin',
          version: serverState.version,
          error: {
            message: 'Bodhi server is in admin mode for resource management',
            code: SERVER_IN_ADMIN_STATUS,
          },
        };
      case 'pending-extension-ready':
        return {
          status: 'pending-extension-ready',
          error: {
            message: 'Server is waiting for extension to be ready',
            code: SERVER_PENDING_EXT_READY,
          },
        };
      case 'unreachable':
        // Map different error scenarios to appropriate codes
        if (serverState.error?.message?.includes('Connection refused')) {
          return {
            status: 'unreachable',
            error: {
              message: serverState.error.message,
              code: SERVER_CONN_REFUSED,
            },
          };
        } else if (serverState.error?.message?.includes('timeout')) {
          return {
            status: 'unreachable',
            error: {
              message: serverState.error.message,
              code: SERVER_CONN_TIMEOUT,
            },
          };
        } else if (
          serverState.error?.message?.includes('Not Found') ||
          serverState.error?.message?.includes('404')
        ) {
          return {
            status: 'unreachable',
            error: {
              message: serverState.error.message,
              code: SERVER_NOT_FOUND,
            },
          };
        } else if (serverState.error?.message?.includes('Network unreachable')) {
          return {
            status: 'unreachable',
            error: {
              message: serverState.error.message,
              code: SERVER_NETWORK_UNREACHABLE,
            },
          };
        } else if (serverState.error?.message?.includes('Service Unavailable')) {
          return {
            status: 'unreachable',
            error: {
              message: serverState.error.message,
              code: SERVER_SERVICE_UNAVAILABLE,
            },
          };
        } else {
          return {
            status: 'unreachable',
            error: {
              message: serverState.error?.message,
              code: SERVER_UNEXPECTED_ERROR,
            },
          };
        }
      case 'error':
        return {
          status: 'error',
          error: {
            message: serverState.error?.message,
            code: SERVER_UNEXPECTED_ERROR,
          },
        };
      default:
        return {
          status: 'unreachable',
          error: {
            message: 'Server status unknown',
            code: SERVER_UNEXPECTED_ERROR,
          },
        };
    }
  }
}
