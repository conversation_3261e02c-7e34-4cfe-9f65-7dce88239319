import { defineConfig } from 'vitest/config';
import { config } from 'dotenv';
import { string } from 'rollup-plugin-string';

// Load .env.test file for test environment variables
config({ path: '.env.test' });

export default defineConfig({
  plugins: [
    string({
      include: '**/*.html',
    }),
  ],
  test: {
    exclude: ['**/node_modules/**'],
    environment: 'node',

    // Global timeout configuration - 30 seconds for all tests
    testTimeout: 30000,
    hookTimeout: 30000,

    setupFiles: ['./tests/setup.ts'],
    sequence: {
      hooks: 'list', // Run hooks in order to maintain compatibility with Jest
      setupFiles: 'list', // Execute setup files in sequence
    },

    globals: true,

    // Run only one file at a time for integration tests
    fileParallelism: false, // Ensure files run sequentially
  },
});
