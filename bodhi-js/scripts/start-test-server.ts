#!/usr/bin/env tsx

import { createServer } from 'http';
import { AddressInfo } from 'net';
import { mkdtempSync } from 'fs';
import { tmpdir } from 'os';
import { join, resolve, dirname } from 'path';
import { fileURLToPath } from 'url';
import { homedir } from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface ServerConfig {
  appStatus: string;
  authUrl: string;
  authRealm: string;
  clientId?: string;
  clientSecret?: string;
  port: number;
  timeout: number;
}

// Default configuration
const defaultConfig: ServerConfig = {
  appStatus: 'setup',
  authUrl: 'https://main-id.getbodhi.app',
  authRealm: 'bodhi',
  port: 51135,
  timeout: 10000,
};

function parseArgs(): ServerConfig {
  const config = { ...defaultConfig };
  const args = process.argv.slice(2);

  for (let i = 0; i < args.length; i += 2) {
    const key = args[i];
    const value = args[i + 1];

    switch (key) {
      case '--app-status':
        config.appStatus = value;
        break;
      case '--auth-url':
        config.authUrl = value;
        break;
      case '--auth-realm':
        config.authRealm = value;
        break;
      case '--client-id':
        config.clientId = value;
        break;
      case '--client-secret':
        config.clientSecret = value;
        break;
      case '--port':
        config.port = parseInt(value, 10);
        break;
      case '--timeout':
        config.timeout = parseInt(value, 10);
        break;
      case '--help':
      case '-h':
        printHelp();
        process.exit(0);
        break;
      default:
        console.warn(`Unknown argument: ${key}`);
        break;
    }
  }

  return config;
}

function printHelp() {
  console.log(`
Usage: tsx scripts/start-test-server.ts [options]

Options:
  --app-status <status>     App status (default: ready)
                           Options: ready, setup, resource-admin
  --auth-url <url>         Auth URL (default: https://auth.bodhiapp.io)
  --auth-realm <realm>     Auth realm (default: bodhiapp)
  --client-id <id>         Client ID (default: test-client)
  --client-secret <secret> Client secret (default: test-secret)
  --port <port>            Server port (default: 50000)
  --timeout <ms>           Health check timeout in ms (default: 10000)
  --help, -h               Show this help message

Examples:
  tsx scripts/start-test-server.ts
  tsx scripts/start-test-server.ts --app-status resource-admin
  tsx scripts/start-test-server.ts --port 51000 --app-status setup
`);
}

async function findAvailablePort(preferredPort: number): Promise<number> {
  return new Promise((resolve, reject) => {
    const server = createServer();
    server.listen(preferredPort, () => {
      const port = (server.address() as AddressInfo).port;
      server.close(() => resolve(port));
    });
    server.on('error', (err: any) => {
      if (err.code === 'EADDRINUSE') {
        // Port is in use, find a random available port
        const fallbackServer = createServer();
        fallbackServer.listen(0, () => {
          const port = (fallbackServer.address() as AddressInfo).port;
          fallbackServer.close(() => resolve(port));
        });
        fallbackServer.on('error', reject);
      } else {
        reject(err);
      }
    });
  });
}

async function waitForHealthCheck(serverUrl: string, timeout: number): Promise<void> {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(`${serverUrl}/ping`);
      if (response.ok) {
        console.log(`✅ Health check passed at ${serverUrl}`);
        return;
      }
    } catch {
      // Server not ready yet, continue waiting
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  throw new Error(`❌ Bodhi server failed to start within ${timeout}ms`);
}

async function createBodhiServer(config: ServerConfig, actualPort: number, tempHome: string) {
  console.log('📦 Loading app-bindings...');

  // Load app-bindings
  const appBindings = await import('@bodhiapp/app-bindings');
  const {
    createNapiAppOptions,
    setEnvVar,
    setSystemSetting,
    setAppSetting,
    setAppStatus,
    setClientCredentials,
    BodhiServer,
    BODHI_HOST,
    BODHI_PORT,
    BODHI_ENV_TYPE,
    BODHI_APP_TYPE,
    BODHI_VERSION,
    BODHI_LOG_LEVEL,
    BODHI_LOG_STDOUT,
    BODHI_AUTH_URL,
    BODHI_AUTH_REALM,
    BODHI_EXEC_LOOKUP_PATH,
  } = appBindings;

  // Build configuration for the Bodhi server
  let napiConfig = createNapiAppOptions();

  // Environment variables
  napiConfig = setEnvVar(napiConfig, 'HOME', tempHome);
  napiConfig = setEnvVar(napiConfig, BODHI_HOST, '127.0.0.1');
  napiConfig = setEnvVar(napiConfig, BODHI_PORT, actualPort.toString());

  // Set HF_HOME for model caching
  const hfHome = resolve(homedir(), '.cache', 'huggingface');
  napiConfig = setEnvVar(napiConfig, 'HF_HOME', hfHome);

  // System settings
  napiConfig = setSystemSetting(napiConfig, BODHI_ENV_TYPE, 'development');
  napiConfig = setSystemSetting(napiConfig, BODHI_APP_TYPE, 'container');
  napiConfig = setSystemSetting(napiConfig, BODHI_VERSION, '1.0.0-test');
  napiConfig = setSystemSetting(napiConfig, BODHI_AUTH_URL, config.authUrl);
  napiConfig = setSystemSetting(napiConfig, BODHI_AUTH_REALM, config.authRealm);

  // App settings
  napiConfig = setAppSetting(napiConfig, BODHI_LOG_LEVEL, 'debug');
  napiConfig = setAppSetting(napiConfig, BODHI_LOG_STDOUT, 'true');

  // Set bin path for executable lookup
  const binPath = resolve(__dirname, '../../bin');
  napiConfig = setAppSetting(napiConfig, BODHI_EXEC_LOOKUP_PATH, binPath);

  // Set app status and client credentials
  napiConfig = setAppStatus(napiConfig, config.appStatus);
  if (config.clientId && config.clientSecret) {
    napiConfig = setClientCredentials(napiConfig, config.clientId, config.clientSecret);
  }

  const server = new BodhiServer(napiConfig);
  return server;
}

async function main() {
  const config = parseArgs();

  console.log('🚀 Starting Bodhi Test Server');
  console.log('📋 Configuration:');
  console.log(`   App Status: ${config.appStatus}`);
  console.log(`   Auth URL: ${config.authUrl}`);
  console.log(`   Auth Realm: ${config.authRealm}`);
  if (config.clientId && config.clientSecret) {
    console.log(`   Client ID: ${config.clientId}`);
    console.log(`   Client Secret: ${config.clientSecret.replace(/./g, '*')}`);
  }
  console.log(`   Port: ${config.port}`);
  console.log('');

  let server: any = null;
  let tempHome: string | null = null;

  try {
    // Find available port
    const actualPort = await findAvailablePort(config.port);
    if (actualPort !== config.port) {
      console.log(`⚠️  Port ${config.port} was in use, using port ${actualPort} instead`);
    }

    const serverUrl = `http://localhost:${actualPort}`;

    // Create temporary home directory
    tempHome = mkdtempSync(join(tmpdir(), 'bodhi-test-'));
    console.log(`📁 Using temp directory: ${tempHome}`);

    // Create and start Bodhi server
    console.log('🔧 Creating Bodhi server...');
    server = await createBodhiServer(config, actualPort, tempHome);

    console.log('▶️  Starting server...');
    await server.start();

    // Verify server is running
    const isRunning = await server.isRunning();
    if (!isRunning) {
      throw new Error('Bodhi server failed to start');
    }

    // Wait for server to be ready
    console.log('🔍 Waiting for server to be ready...');
    await waitForHealthCheck(serverUrl, config.timeout);

    console.log('');
    console.log('🎉 Bodhi Test Server is running!');
    console.log(`📍 Server URL: ${serverUrl}`);
    console.log(`📊 Status: ${config.appStatus}`);
    console.log('');
    console.log('💡 You can now:');
    console.log(`   - Visit: ${serverUrl}`);
    console.log(`   - Health check: ${serverUrl}/ping`);
    console.log(`   - Test with browser extension`);
    console.log('');
    console.log('Press Ctrl+C to stop the server');

    // Keep the process running
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down server...');
      await cleanup();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\n🛑 Shutting down server...');
      await cleanup();
      process.exit(0);
    });

    // Keep the process alive by reading from stdin
    process.stdin.resume();

    // This will keep the process running until killed
    await new Promise<void>(_resolve => {
      // This promise will only resolve when the process is killed
      // The signal handlers above will handle cleanup and exit
    });
  } catch (error) {
    console.error('❌ Error starting server:', error);
    await cleanup();
    process.exit(1);
  }

  async function cleanup() {
    if (server) {
      try {
        console.log('🔄 Stopping Bodhi server...');
        await server.stop();
        console.log('✅ Server stopped');
      } catch (error) {
        console.error('❌ Error stopping server:', error);
      }
    }

    if (tempHome) {
      try {
        const { rmSync } = await import('fs');
        rmSync(tempHome, { recursive: true, force: true });
        console.log('🧹 Cleaned up temp directory');
      } catch (error) {
        console.warn('⚠️  Could not clean up temp directory:', error);
      }
    }
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
