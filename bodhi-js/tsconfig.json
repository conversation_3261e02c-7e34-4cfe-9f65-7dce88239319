{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["dom", "esnext"], "declaration": true, "declarationDir": "dist/dts", "sourceMap": true, "noImplicitAny": true, "strictNullChecks": true, "moduleResolution": "node", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "isolatedModules": true, "preserveSymlinks": true, "resolveJsonModule": true, "types": ["node", "vitest/globals"]}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "examples"]}