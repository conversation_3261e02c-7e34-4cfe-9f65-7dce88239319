import typescript from '@rollup/plugin-typescript';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import terser from '@rollup/plugin-terser';
import { dts } from 'rollup-plugin-dts';
import { string } from 'rollup-plugin-string';
import { readFileSync } from 'fs';

const pkg = JSON.parse(readFileSync(new URL('./package.json', import.meta.url)));

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Banner comment to be added to the top of each file
const banner = `/**
 * Bodhi.js - Bridge to access local LLM capabilities from web pages
 * @version ${pkg.version}
 * @license ${pkg.license}
 * @see ${pkg.homepage}
 * @build ${isDevelopment ? 'development' : 'production'}
 */`;

const configs = [
  {
    input: 'src/index.ts',
    output: [
      {
        file: pkg.main,
        format: 'cjs',
        sourcemap: true,
        banner,
        exports: 'named',
      },
      {
        file: pkg.module,
        format: 'es',
        sourcemap: true,
        banner,
        exports: 'named',
      },
    ],
    plugins: [
      string({
        include: '**/*.html',
      }),
      typescript({
        tsconfig: './tsconfig.json',
        sourceMap: true,
        include: ['src/**/*'],
        exclude: ['node_modules/**'],
      }),
      resolve({
        preferBuiltins: true,
      }),
      commonjs(),
      // Only minify in production
      !isDevelopment && terser(),
    ].filter(Boolean),
    external: ['@bodhiapp/ts-client'],
  },
  // Type definitions bundle
  {
    input: 'dist/dts/index.d.ts',
    output: [{ file: 'dist/index.d.ts', format: 'es' }],
    plugins: [dts()],
    external: ['@bodhiapp/ts-client'],
  },
];

export default configs;
