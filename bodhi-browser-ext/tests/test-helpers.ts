import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import os from 'os';
import { chromium, B<PERSON><PERSON>, <PERSON>, BrowserContext } from 'playwright';

// Get current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
export const extensionPath = path.resolve(__dirname, '..', 'dist');
export const testExtensionPath = path.resolve(__dirname, 'test-extension');
export const testPagePath = path.resolve(__dirname, 'test-page');

// Default timeout for all Playwright operations (in ms)
export const DEFAULT_TIMEOUT = 10000;

// Platform detection
const isMac = os.platform() === 'darwin';
export const isCI = process.env.CI === 'true';

// Server startup/teardown helpers (migrated from setup.ts)
import { startMockLLMServer, startStaticServer } from '@bodhiapp/mock-llm-server';
import { ChildProcess, spawn } from 'child_process';

export interface ServerInfo {
  server: any;
  port: number;
  close: () => void;
}

export interface TestEnvironment {
  staticServer: ServerInfo;
  staticPort: number;
  mockServer?: ReturnType<typeof startMockLLMServer>;
  mockPort?: number;
}

export async function startTestEnvironment(options?: {
  noMockLLMServer?: boolean;
}): Promise<TestEnvironment> {
  // Start static file server for test pages
  const staticServer = startStaticServer({ staticPath: testPagePath });
  const staticPort = staticServer.port;
  if (options?.noMockLLMServer) {
    return { staticServer, staticPort };
  }
  // Start mock API server
  const mockServer = startMockLLMServer();
  const mockPort = mockServer.port;
  return { staticServer, staticPort, mockServer, mockPort };
}

export async function stopTestEnvironment(env: TestEnvironment | undefined) {
  if (!env) return;
  if (env.staticServer) env.staticServer.close();
  if (env.mockServer) env.mockServer.close();
}

// OAuth App Server Management
export interface OAuthAppServerInfo {
  server: ChildProcess;
  port: number;
  url: string;
}

export async function startOAuthAppServer(port: number): Promise<OAuthAppServerInfo> {
  const oauthAppPath = path.resolve(__dirname, 'test-app-oauth');

  const server = spawn('python3', ['-m', 'http.server', '-d', 'dist', port.toString()], {
    cwd: oauthAppPath,
    stdio: 'inherit',
  });

  const url = `http://localhost:${port}`;

  // Wait for server to start
  await new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`OAuth app server failed to start within 10 seconds on port ${port}`));
    }, 10000);

    const checkServer = async () => {
      try {
        const response = await fetch(url, {
          signal: AbortSignal.timeout(500),
        });
        if (response.ok) {
          clearTimeout(timeout);
          resolve(undefined);
        } else {
          setTimeout(checkServer, 100);
        }
      } catch {
        setTimeout(checkServer, 100);
      }
    };
    setTimeout(checkServer, 100);
  });

  return { server, port, url };
}

export function stopOAuthAppServer(serverInfo: OAuthAppServerInfo | null) {
  if (serverInfo?.server) {
    serverInfo.server.kill();
  }
}

interface BrowserConfig {
  executablePath: string | null;
}

// Browser configuration
export const getBrowserConfig = (): BrowserConfig => {
  const browserName = process.env.BROWSER || 'chrome';
  let executablePath = process.env.BROWSER_PATH || null;

  // If path is explicitly provided, use it
  if (executablePath) {
    console.log(`Using browser at specified path: ${executablePath}`);
    return { executablePath };
  }

  // For Chrome, let Playwright find it automatically unless overridden
  if (browserName === 'chrome' && !process.env.FORCE_PATH) {
    console.log('Using Playwright default Chrome/Chromium');
    return { executablePath: null };
  }

  // Platform-specific paths for known browsers
  const browserPaths: Record<string, Record<string, string>> = {
    mac: {
      chrome: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
      brave: '/Applications/Brave Browser.app/Contents/MacOS/Brave Browser',
      edge: '/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge',
    },
    windows: {
      chrome: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      brave: 'C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe',
      edge: 'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
    },
    linux: {
      chrome: '/usr/bin/google-chrome',
      brave: '/usr/bin/brave-browser',
      edge: '/usr/bin/microsoft-edge',
    },
  };

  // Select appropriate paths based on platform
  const platformPaths = isMac
    ? browserPaths.mac
    : os.platform() === 'win32'
      ? browserPaths.windows
      : browserPaths.linux;

  executablePath = platformPaths[browserName];

  // In CI environment, we typically rely on pre-installed browsers or let Playwright handle it
  if (isCI) {
    console.log('Running in CI environment, using default browser');
    return { executablePath: null };
  }

  // Verify browser path exists if we're not in CI
  if (executablePath && !isCI && !fs.existsSync(executablePath)) {
    console.warn(`Warning: Browser not found at path: ${executablePath}`);
    console.warn(`Available configured browsers: ${Object.keys(platformPaths).join(', ')}`);
    console.warn('Falling back to Playwright default');
    executablePath = null;
  }

  console.log(
    `Using ${browserName}${executablePath ? ` at ${executablePath}` : ' (Playwright default)'}`
  );
  return { executablePath };
};

export const executablePath: string | undefined = getBrowserConfig().executablePath || undefined;

// Helper function to get test result from UI
export const getTestResult = async (page: Page, outputId: string): Promise<any> => {
  return await page.evaluate((id: string) => {
    const output = document.getElementById(id);
    return JSON.parse(output!.textContent!);
  }, outputId);
};

export const waitForBodhiExtInfo = async (page: Page): Promise<string | null> => {
  // Wait for the bodhiext object and getExtensionId method to be defined
  await page.waitForFunction(
    () =>
      typeof (window as any).bodhiext !== 'undefined' &&
      typeof (window as any).bodhiext.getExtensionId === 'function',
    { timeout: DEFAULT_TIMEOUT }
  );

  // Call the async getExtensionId method in the browser context and return the result
  return await page.evaluate(async () => {
    return await (window as any).bodhiext.getExtensionId();
  });
};

export const waitForTestOutput = async (
  page: Page,
  outputId = 'testOutput',
  timeout = DEFAULT_TIMEOUT
): Promise<void> => {
  await page
    .waitForFunction(
      (id: string) => {
        const classNames = document.getElementById(id)?.className;
        return classNames?.includes('success') || classNames?.includes('error');
      },
      outputId,
      { timeout }
    )
    .catch(() => {
      throw new Error(`Test output for ${outputId} not found after timeout`);
    });
};

// Browser setup interface
export interface BrowserSetup {
  browser: Browser;
  context: BrowserContext;
  extensionId: string | null;
}

// Improved browser setup with proper lifecycle management
export interface BrowserManager {
  setup: BrowserSetup;
  createPage: () => Promise<Page>;
  cleanup: () => Promise<void>;
}

interface LaunchOptions {
  loadTestExtension?: boolean;
  staticPort: number;
  mockPort: number;
}

// Enhanced browser launcher with proper lifecycle management
export const createBrowserManager = async ({
  loadTestExtension = false,
  staticPort,
  mockPort,
}: LaunchOptions): Promise<BrowserManager> => {
  console.log(`Launching browser with${loadTestExtension ? '' : 'out'} test extension...`);

  const extensionsToLoad = [extensionPath];

  if (loadTestExtension) {
    extensionsToLoad.push(testExtensionPath);
  }

  // For Playwright, we need to use launchPersistentContext to load extensions
  const context = await chromium.launchPersistentContext('', {
    headless: isCI ? true : false, // Use headless in CI, non-headless otherwise
    executablePath, // Configurable browser path (null = use playwright default)
    args: [
      `--disable-extensions-except=${extensionsToLoad.join(',')}`,
      `--load-extension=${extensionsToLoad.join(',')}`,
      '--no-sandbox',
      '--disable-setuid-sandbox', // Needed for CI environments
      '--disable-dev-shm-usage', // Needed for CI environments
      '--disable-accelerated-2d-canvas',
      '--disable-gpu',
    ],
  });
  const browser = context.browser()!;
  const initialPage = await context.newPage();
  initialPage.on('console', msg => console.log('INITIAL PAGE:', msg.text()));
  initialPage.on('pageerror', err => console.log('INITIAL PAGE ERROR:', err));
  initialPage.setDefaultTimeout(DEFAULT_TIMEOUT);
  await initialPage.goto(`http://localhost:${staticPort}/`);
  const extensionId = await waitForBodhiExtInfo(initialPage);
  if (loadTestExtension) {
    await initialPage.waitForFunction(
      () => {
        return typeof (window as any).bodhiTestExtension !== 'undefined';
      },
      { timeout: 10000 }
    );
  }
  if (extensionId) {
    await setBackendUrl(context, extensionId, mockPort);
  } else {
    throw new Error('Bodhi extension not loaded properly - extension ID not detected');
  }

  // Close the initial page after setup
  await initialPage.close();

  const setup: BrowserSetup = { browser, context, extensionId };

  return {
    setup,
    createPage: async (): Promise<Page> => {
      const page = await context.newPage();
      page.setDefaultTimeout(DEFAULT_TIMEOUT);

      // Add console message logging
      page.on('console', msg => console.log('BROWSER:', msg.text()));

      return page;
    },
    cleanup: async (): Promise<void> => {
      // Close all pages first
      const pages = context.pages();
      await Promise.all(pages.map(page => page.close().catch(() => {})));

      // Close the browser
      await browser.close().catch(() => {});
    },
  };
};

// Legacy function for backward compatibility - will be deprecated
export const launchBrowser = async (
  options: LaunchOptions
): Promise<{ browser: Browser; page: Page; context: BrowserContext }> => {
  const manager = await createBrowserManager(options);
  const page = await manager.createPage();

  return {
    browser: manager.setup.browser,
    page,
    context: manager.setup.context,
  };
};

export const setBackendUrl = async (
  context: BrowserContext,
  extensionId: string,
  backendPort: number
) => {
  // Create a popup page to set the URL using the same context that has the extension loaded
  const popupPage = await context.newPage();

  // Set default timeout for popup page too
  popupPage.setDefaultTimeout(DEFAULT_TIMEOUT);
  try {
    await popupPage.goto(`chrome-extension://${extensionId}/index.html`);
    await popupPage.waitForSelector('form');
    await popupPage.locator('#backendUrl').fill(`http://localhost:${backendPort}`);
    await popupPage.click('button[type="submit"]');

    // Wait for message-container with success or error class
    await popupPage.waitForSelector('.message-container.success, .message-container.error', {
      timeout: DEFAULT_TIMEOUT,
    });

    // Check if there's an error and fail the test if so
    const hasError = await popupPage.evaluate(() => {
      const messageContainer = document.querySelector('.message-container');
      return messageContainer && messageContainer.classList.contains('error');
    });

    if (hasError) {
      const errorMessage = await popupPage.evaluate(() => {
        const element = document.querySelector('.message-container');
        return element ? element.textContent : 'Unknown error';
      });
      throw new Error(`Failed to set backend URL: ${errorMessage}`);
    }
  } finally {
    await popupPage.close();
    // Don't close the context since it's the main context with the extension
  }
};
