import { expect } from '@playwright/test';
import { exec as execCallback } from 'child_process';
import path from 'path';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { fileURLToPath } from 'url';
import { promisify } from 'util';
import { afterAll, beforeAll, describe, test } from 'vitest';
import { createBodhiServer } from './bodhi-server-config.js';
import {
  launchBrowser,
  startOAuthAppServer,
  stopOAuthAppServer,
  OAuthAppServerInfo,
} from './test-helpers';

const exec = promisify(execCallback);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const oauthAppPath = path.resolve(__dirname, 'test-app-oauth');

describe('OAuth Test App Integration', () => {
  let browser: Browser;
  let page: Page;
  let bodhiServer: any;
  let serverPort: number;
  let bodhiAuthUrl: string;
  let testUser: string;
  let testPassword: string;
  let oauthAppServer: OAuthAppServerInfo | null = null;
  const oauthAppPort = 12345;

  beforeAll(async () => {
    const appBuild = await exec('npm run build', { cwd: oauthAppPath });
    if (appBuild.stdout) process.stdout.write(appBuild.stdout);
    if (appBuild.stderr) process.stderr.write(appBuild.stderr);

    // Start the OAuth app server using the utility
    oauthAppServer = await startOAuthAppServer(oauthAppPort);

    serverPort = Math.floor(Math.random() * (30000 - 20000) + 20000);
    const { homedir } = await import('os');
    const { resolve } = await import('path');
    const userHome = homedir();
    const hfHome = resolve(userHome, '.cache', 'huggingface');

    const resourceClientId = process.env.INTEG_TEST_CLIENT_ID!;
    const resourceClientSecret = process.env.INTEG_TEST_CLIENT_SECRET!;
    testUser = process.env.INTEG_TEST_USERNAME!;
    testPassword = process.env.INTEG_TEST_PASSWORD!;
    bodhiAuthUrl = process.env.INTEG_TEST_AUTH_URL!;
    const realm = process.env.INTEG_TEST_AUTH_REALM!;
    const { server } = await createBodhiServer({
      hf_home: hfHome,
      port: serverPort,
      host: '127.0.0.1',
      logLevel: 'debug',
      logToStdout: true,
      appStatus: 'ready',
      clientId: resourceClientId,
      clientSecret: resourceClientSecret,
      authUrl: bodhiAuthUrl,
      authRealm: realm,
    });
    bodhiServer = server;
    await bodhiServer.start();
    // Launch browser with extension
    const browserSetup = await launchBrowser({
      loadTestExtension: false,
      staticPort: oauthAppPort,
      mockPort: serverPort,
    });

    browser = browserSetup.browser;
    page = browserSetup.page;
    page.on('console', msg => console.log('PAGE:', msg.text()));
    page.on('pageerror', err => console.log('PAGE ERROR:', err));
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }

    stopOAuthAppServer(oauthAppServer);

    if (bodhiServer) {
      await bodhiServer.stop();
    }
  });

  test('OAuth test app should load and detect browser extension', async () => {
    await page.goto(`http://localhost:${oauthAppPort}`);
    await page.waitForSelector('#readyStatus', { state: 'visible' });
    const readyStatus = page.locator('#readyStatus');
    expect(readyStatus).toBeVisible();
    const loginButton = page.locator('#loginButton');
    expect(loginButton).toBeVisible();
    const errorElement = page.locator('#error');
    expect(errorElement).not.toBeVisible();

    // Check initial user info state in navigation
    const userInfo = page.locator('[data-testid="user-info"]');
    await expect(userInfo).toBeVisible();
    const userInfoText = await userInfo.textContent();
    expect(userInfoText).toContain('Not authenticated');

    await loginButton.click();
    await page.waitForURL(url => url.origin === bodhiAuthUrl);
    await page.waitForLoadState('networkidle');

    const usernameField = page.locator('input[name="username"], input[type="email"], #username');
    const passwordField = page.locator('input[name="password"], input[type="password"], #password');
    const submitButton = page.locator(
      'button[type="submit"], input[type="submit"], button:has-text("Sign In")'
    );

    await expect(usernameField).toBeVisible();
    await expect(passwordField).toBeVisible();

    await usernameField.fill(testUser);
    await passwordField.fill(testPassword);
    await submitButton.click();
    await page.waitForURL(
      url => url.origin === `http://localhost:${oauthAppPort}` && url.pathname === '/'
    );
    await page.waitForLoadState('networkidle');
    const errorElementAfterLogin = page.locator('#error');
    expect(errorElementAfterLogin).not.toBeVisible();
    const bodyRow = page
      .locator('#userDetails .info-row')
      .filter({
        has: page.locator('.info-label', { hasText: 'Body' }),
      })
      .first();
    const bodyJsonText = await bodyRow.locator('.info-value').textContent();
    expect(bodyJsonText).toBeTruthy();
    const bodyObj = JSON.parse(bodyJsonText!);
    expect(bodyObj).toEqual({
      logged_in: true,
      email: testUser,
      role: 'scope_user_user',
      token_type: 'bearer',
      role_source: 'scope_user',
    });
  });

  test('should detect bodhiext object injection in API test page', async () => {
    await page.goto(`http://localhost:${oauthAppPort}/api-test.html`);
    await page.waitForLoadState('networkidle');

    // Wait for extension status to be detected
    await page.waitForSelector('[data-testid="extension-status"]', { state: 'visible' });

    // Check that the page loaded and bodhiext is available
    const bodhiextExists = await page.evaluate(() => {
      return typeof window.bodhiext !== 'undefined';
    });

    expect(bodhiextExists).toBe(true);

    // Check user info display in navigation on API test page
    const userInfo = page.locator('[data-testid="user-info"]');
    await expect(userInfo).toBeVisible();
    // Note: User info might show authenticated state if previous tests have logged in
    // This is expected behavior as authentication state persists
  });

  test('should get extension ID from API test page', async () => {
    await page.goto(`http://localhost:${oauthAppPort}/api-test.html`);
    await page.waitForLoadState('networkidle');

    const extensionId = await page.evaluate(async () => {
      return await window.bodhiext.getExtensionId();
    });

    expect(typeof extensionId).toBe('string');
    expect(extensionId.length).toBeGreaterThan(0);
  });

  test('should receive pong from /ping endpoint without authentication', async () => {
    await page.goto(`http://localhost:${oauthAppPort}/api-test.html`);
    await page.waitForLoadState('networkidle');

    // Wait for API status to be ready
    await page.waitForSelector('[data-testid="api-status"]', { state: 'visible' });

    // Configure the API request using test IDs
    await page.selectOption('[data-testid="api-method"]', 'GET');
    await page.fill('[data-testid="api-endpoint"]', '/ping');
    await page.fill('[data-testid="api-body"]', '');
    await page.fill('[data-testid="api-headers"]', '');

    // Ensure authentication is disabled and streaming is disabled
    await page.uncheck('[data-testid="include-auth"]');
    await page.uncheck('[data-testid="streaming-mode"]');

    // Submit the request
    await page.click('[data-testid="submit-api-request"]');

    // Wait for the response to appear
    await page.waitForFunction(() => {
      const responseBody = document.querySelector('[data-testid="response-body"]');
      return responseBody?.textContent && responseBody.textContent !== '(no response yet)';
    });

    // Check the response
    const responseBody = await page.locator('[data-testid="response-body"]').textContent();
    const responseObj = JSON.parse(responseBody || '{}');

    expect(responseObj).toEqual({ message: 'pong' });
  });

  test('should receive chat response from /v1/chat/completions with authentication', async () => {
    await page.goto(`http://localhost:${oauthAppPort}/api-test.html`);
    await page.waitForLoadState('networkidle');

    // Wait for API status to be ready
    await page.waitForSelector('[data-testid="api-status"]', { state: 'visible' });

    // Configure the API request using test IDs
    await page.selectOption('[data-testid="api-method"]', 'POST');
    await page.fill('[data-testid="api-endpoint"]', '/v1/chat/completions');
    await page.fill(
      '[data-testid="api-body"]',
      JSON.stringify({
        model: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M',
        messages: [{ role: 'user', content: 'Answer in one word: what day comes after Monday?' }],
      })
    );
    await page.fill('[data-testid="api-headers"]', '');

    // Enable authentication, disable streaming
    await page.check('[data-testid="include-auth"]');
    await page.uncheck('[data-testid="streaming-mode"]');

    // Submit the request
    await page.click('[data-testid="submit-api-request"]');

    // Wait for the response to appear
    await page.waitForFunction(() => {
      const responseBody = document.querySelector('[data-testid="response-body"]');
      return responseBody?.textContent && responseBody.textContent !== '(no response yet)';
    });

    // Check the response
    const responseBody = await page.locator('[data-testid="response-body"]').textContent();
    const responseObj = JSON.parse(responseBody || '{}');

    expect(responseObj).toBeDefined();
    expect(responseObj.choices).toBeDefined();
    expect(responseObj.choices[0]).toBeDefined();
    expect(responseObj.choices[0].message).toBeDefined();
    expect(responseObj.choices[0].message.content).toBeDefined();
    expect(typeof responseObj.choices[0].message.content).toBe('string');
  });

  test('should handle error responses for invalid endpoint', async () => {
    await page.goto(`http://localhost:${oauthAppPort}/api-test.html`);
    await page.waitForLoadState('networkidle');

    // Wait for API status to be ready
    await page.waitForSelector('[data-testid="api-status"]', { state: 'visible' });

    // Configure the API request for invalid endpoint using test IDs
    await page.selectOption('[data-testid="api-method"]', 'POST');
    await page.fill('[data-testid="api-endpoint"]', '/invalid-action');
    await page.fill('[data-testid="api-body"]', '{}');
    await page.fill('[data-testid="api-headers"]', '');

    // Disable authentication and streaming
    await page.uncheck('[data-testid="include-auth"]');
    await page.uncheck('[data-testid="streaming-mode"]');

    // Submit the request
    await page.click('[data-testid="submit-api-request"]');

    // Wait for the response to appear (even for errors)
    await page.waitForFunction(() => {
      const responseStatus = document.querySelector('[data-testid="response-status"]');
      return responseStatus?.textContent && responseStatus.textContent !== '(no response yet)';
    });

    // Check the response status
    const responseStatus = await page.locator('[data-testid="response-status"]').textContent();
    expect(responseStatus).toContain('404');

    // Check the response body - real server returns empty string for 404
    const responseBody = await page.locator('[data-testid="response-body"]').textContent();
    expect(responseBody).toBe('""');
  });

  test('should receive streaming chat completion with authentication', async () => {
    await page.goto(`http://localhost:${oauthAppPort}/api-test.html`);
    await page.waitForLoadState('networkidle');

    // Wait for API status to be ready
    await page.waitForSelector('[data-testid="api-status"]', { state: 'visible' });

    // Configure the streaming API request using test IDs
    await page.selectOption('[data-testid="api-method"]', 'POST');
    await page.fill('[data-testid="api-endpoint"]', '/v1/chat/completions');
    await page.fill(
      '[data-testid="api-body"]',
      JSON.stringify({
        model: 'bartowski/microsoft_Phi-4-mini-instruct-GGUF:Q4_K_M',
        messages: [{ role: 'user', content: 'Answer in one word: what day comes after Monday?' }],
      })
    );
    await page.fill('[data-testid="api-headers"]', '');

    // Enable authentication and streaming
    await page.check('[data-testid="include-auth"]');
    await page.check('[data-testid="streaming-mode"]');

    // Submit the request
    await page.click('[data-testid="submit-api-request"]');

    // Wait for streaming section to appear
    await page.waitForFunction(() => {
      const streamSection = document.querySelector('[data-testid="stream-section"]');
      return streamSection?.style.display !== 'none';
    });

    // Wait for streaming to complete (response appears)
    await page.waitForFunction(
      () => {
        const responseBody = document.querySelector('[data-testid="response-body"]');
        return responseBody?.textContent && responseBody.textContent !== '(no response yet)';
      },
      { timeout: 10000 }
    );

    // Check that stream content was populated
    const streamContent = await page.locator('[data-testid="stream-content"]').textContent();
    expect(streamContent).toBeDefined();
    expect(streamContent!.length).toBeGreaterThan(0);

    // Check the final response
    const responseBody = await page.locator('[data-testid="response-body"]').textContent();
    const responseObj = JSON.parse(responseBody || '{}');

    expect(responseObj).toBeDefined();
    expect(responseObj.choices).toBeDefined();
  });

  test('should show proper status indicators during API request lifecycle', async () => {
    await page.goto(`http://localhost:${oauthAppPort}/api-test.html`);
    await page.waitForLoadState('networkidle');

    // Wait for initial status indicators to be visible
    await page.waitForSelector('[data-testid="extension-status"]', { state: 'visible' });
    await page.waitForSelector('[data-testid="api-status"]', { state: 'visible' });

    // Check initial extension status (should show success after detection)
    const extensionStatus = page.locator('[data-testid="extension-status"]');
    await expect(extensionStatus).toContainText('Connected ✓');

    // Check initial API status (should be ready)
    const apiStatus = page.locator('[data-testid="api-status"]');
    await expect(apiStatus).toContainText('Ready');

    // Configure a simple API request
    await page.selectOption('[data-testid="api-method"]', 'GET');
    await page.fill('[data-testid="api-endpoint"]', '/ping');
    await page.uncheck('[data-testid="include-auth"]');
    await page.uncheck('[data-testid="streaming-mode"]');

    // Submit the request and verify status changes
    await page.click('[data-testid="submit-api-request"]');

    // API status should change to "Calling API..." briefly, then to "Completed"
    // Note: The calling state might be too brief to catch reliably, so we focus on the final state
    await page.waitForFunction(() => {
      const apiStatusElement = document.querySelector('[data-testid="api-status"]');
      return apiStatusElement?.textContent?.includes('Completed');
    });

    // Verify final status
    await expect(apiStatus).toContainText('Completed');

    // Verify response was received
    const responseBody = await page.locator('[data-testid="response-body"]').textContent();
    expect(responseBody).toContain('pong');
  });

  test('should display user info consistently across pages after authentication', async () => {
    // Since authentication state persists from previous tests, we can test that
    // user info is displayed consistently across pages

    // Navigate to API test page and check user info display
    await page.goto(`http://localhost:${oauthAppPort}/api-test.html`);
    await page.waitForLoadState('networkidle');

    // Check that user info is displayed as authenticated on API test page
    const userInfoApi = page.locator('[data-testid="user-info"]');
    await expect(userInfoApi).toBeVisible();

    // Wait for user info to be loaded and displayed
    await page.waitForFunction(
      () => {
        const userInfoElement = document.querySelector('[data-testid="user-info"]');
        return userInfoElement?.textContent && userInfoElement.textContent.trim() !== '';
      },
      { timeout: 5000 }
    );

    let userInfoText = await userInfoApi.textContent();

    // If user is authenticated (from previous test), verify it shows user email
    if (userInfoText && userInfoText.includes(testUser)) {
      expect(userInfoText).toContain(testUser); // Should show the user's email
      expect(userInfoText).not.toContain('Not authenticated');

      // Navigate back to home page and verify user info is still displayed
      await page.goto(`http://localhost:${oauthAppPort}/`);
      await page.waitForLoadState('networkidle');

      const userInfoHomeAfterAuth = page.locator('[data-testid="user-info"]');
      await expect(userInfoHomeAfterAuth).toBeVisible();

      // Wait for user info to be loaded on home page
      await page.waitForFunction(
        () => {
          const userInfoElement = document.querySelector('[data-testid="user-info"]');
          return userInfoElement?.textContent && userInfoElement.textContent.trim() !== '';
        },
        { timeout: 5000 }
      );

      userInfoText = await userInfoHomeAfterAuth.textContent();
      expect(userInfoText).toContain(testUser); // Should show the user's email
      expect(userInfoText).not.toContain('Not authenticated');
    } else {
      // If not authenticated, verify it shows not authenticated state
      expect(userInfoText).toContain('Not authenticated');
    }
  });
});
