/**
 * Integration tests for libbodhiext.ts using Playwright
 * Tests extension detection with browser launched with/without extension
 */

import { test, expect, beforeAll, afterAll } from 'vitest';
import { expect as playwrightExpect } from '@playwright/test';
import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import path from 'path';
import { startOAuthAppServer, stopOAuthAppServer, OAuthAppServerInfo } from './test-helpers';

const EXTENSION_PATH = path.resolve(__dirname, '../dist');
const TEST_APP_PORT = 3000;
const TEST_PAGE_URL = `http://localhost:${TEST_APP_PORT}/`;

let oauthAppServer: OAuthAppServerInfo | null = null;

beforeAll(async () => {
  // Start the OAuth app server
  oauthAppServer = await startOAuthAppServer(TEST_APP_PORT);
});

afterAll(async () => {
  stopOAuthAppServer(oauthAppServer);
});

test('loadExtensionClient works when extension is installed', async () => {
  let context: any = null;
  let page: Page;

  try {
    // Launch browser with extension installed
    context = await chromium.launchPersistentContext('', {
      headless: false,
      args: [
        `--disable-extensions-except=${EXTENSION_PATH}`,
        `--load-extension=${EXTENSION_PATH}`,
        '--no-sandbox',
        '--disable-setuid-sandbox',
      ],
    });

    page = await context.newPage();

    // Navigate to our test page
    await page.goto(TEST_PAGE_URL);

    // Wait for the page to load and auto-detection to complete
    await page.waitForTimeout(3000);

    // Check that extension was detected successfully
    const extensionStatus = await page.textContent('[data-testid="extension-status"]');
    expect(extensionStatus).toContain('Successfully connected to Bodhi browser extension');

    // Verify extension ID format and consistency with window.bodhiext
    const extensionIdText = await page.textContent('[data-testid="extension-id"]');
    const extensionId = extensionIdText?.replace('Extension ID: ', '');
    expect(extensionId).toMatch(/^[a-z]{32}$/);

    const windowExtensionId = await page.evaluate(async () => {
      return await (window as any).bodhiext?.getExtensionId();
    });
    expect(windowExtensionId).toBe(extensionId);

    // Check that the detection output shows success
    const detectionOutput = await page.textContent('[data-testid="detection-output"]');
    expect(detectionOutput).toContain('success');
    expect(detectionOutput).toContain('extensionId');

    // Verify server state section appears with functional elements
    await playwrightExpect(page.locator('.server-state')).toBeVisible();
    await playwrightExpect(page.locator('.status-message')).toContainText('Status: unreachable');
    await playwrightExpect(page.locator('.server-state button')).toBeVisible();
  } finally {
    if (context) {
      await context.close();
    }
  }
}, 60000); // 60 second timeout

test('loadExtensionClient throws ExtensionNotFoundError when extension is not installed', async () => {
  let browser: Browser | null = null;
  let page: Page;

  try {
    // Launch browser WITHOUT extension
    browser = await chromium.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    page = await browser.newPage();

    // Navigate to our test page
    await page.goto(TEST_PAGE_URL);

    // Wait for the page to load and detection to timeout
    await page.waitForTimeout(6000); // Wait longer than the 2s timeout

    // Check that extension was not detected
    const extensionStatus = await page.textContent('[data-testid="extension-status"]');
    expect(extensionStatus).toContain('Not found ✗');

    // Check that extension ID shows not detected
    const extensionId = await page.textContent('[data-testid="extension-id"]');
    expect(extensionId).toBe('Extension not detected');

    // Check that the detection output shows error
    const detectionOutput = await page.textContent('[data-testid="detection-output"]');
    expect(detectionOutput).toContain('failed');
    expect(detectionOutput).toContain('Extension Not Installed');
    expect(detectionOutput).toContain('ExtensionNotFoundError');

    // Extension detection failed as expected - no additional buttons to check

    // Verify retry button is visible
    const retryButtonVisible = await page.isVisible('[data-testid="retry-detection"]');
    expect(retryButtonVisible).toBe(true);

    // Verify server state section is hidden when extension not detected
    await playwrightExpect(page.locator('.server-state')).not.toBeVisible();
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}, 60000); // 60 second timeout

test('retry functionality works correctly', async () => {
  let browser: Browser | null = null;
  let page: Page;

  try {
    // Launch browser WITHOUT extension first
    browser = await chromium.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    page = await browser.newPage();

    // Navigate to our test page
    await page.goto(TEST_PAGE_URL);

    // Wait for initial detection to fail (increased timeout for merged page)
    await page.waitForTimeout(6000);

    // Verify retry button is visible
    const retryButtonVisible = await page.isVisible('[data-testid="retry-detection"]');
    expect(retryButtonVisible).toBe(true);

    // Click retry button
    await page.click('[data-testid="retry-detection"]');

    // Wait a bit for retry to start
    await page.waitForTimeout(1000);

    // Check that status shows detecting again
    const extensionStatus = await page.textContent('[data-testid="extension-status"]');
    expect(extensionStatus).toContain('Detecting...');

    // Wait for retry to complete (should fail again)
    await page.waitForTimeout(4000);

    // Verify it still shows not found
    const finalStatus = await page.textContent('[data-testid="extension-status"]');
    expect(finalStatus).toContain('Not found ✗');
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}, 90000); // 90 second timeout for this longer test
