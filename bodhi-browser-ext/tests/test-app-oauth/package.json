{"name": "test-app-oauth", "version": "1.0.0", "description": "OAuth test app for bodhi-browser-ext integration testing", "main": "dist/index.html", "scripts": {"build": "npm run clean && npm run compile && npm run copy-html", "clean": "rm -rf dist", "compile": "tsc", "copy-html": "cp src/*.html dist/ && cp src/*.ico dist/ && cp src/*.css dist/", "serve": "python3 -m http.server -d dist 3000", "dev": "npm run build && npm run serve"}, "devDependencies": {"typescript": "^5.3.0", "@types/node": "^20.0.0"}, "private": true}