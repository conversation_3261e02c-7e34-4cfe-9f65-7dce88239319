# OAuth Test App

A test application that demonstrates OAuth2 integration with BodhiApp through the browser extension.

## Purpose

This test app validates the complete OAuth2 token exchange flow for third-party applications integrating with BodhiApp via the `bodhiext` browser extension. It implements the app-to-BodhiApp OAuth flow as described in the integration documentation.

## Architecture

- **TypeScript**: Compiled to ES2020 modules for browser compatibility
- **Static HTML/CSS/JS**: No heavy frameworks, served via Python HTTP server
- **OAuth2 + PKCE**: Secure authorization code flow with PKCE
- **Browser Extension Integration**: Uses `window.bodhiext.callAPI()` for BodhiApp communication

## OAuth Flow

1. **Landing Page** (`/`) - Waits for browser extension, shows login button
2. **Resource Access** - Calls `POST /bodhi/v1/auth/request-access` to get resource scope
3. **OAuth Authorization** - Redirects to OAuth server with proper scopes
4. **Callback Page** (`/callback`) - Handles authorization code and token exchange
5. **User Dashboard** (`/user`) - Displays user info from `GET /bodhi/v1/user`

## Configuration

Edit `src/constants.ts` to configure:

```typescript
export const APP_CLIENT_ID = 'test-app-oauth-client';
export const BODHI_AUTH_URL = 'https://main-id.getbodhi.app';
export const AUTH_REALM = 'bodhi';
```

## Build & Run

```bash
# Install dependencies
npm install

# Build TypeScript to static files
npm run build

# Serve on http://localhost:3000
npm run serve

# Or build and serve in one command
npm run dev
```

## Testing Integration

This app is designed for automated testing with Playwright:

```javascript
// In test file
const { spawn } = require('child_process');

// Build the app
await exec('npm run build', { cwd: 'bodhi-browser-ext/tests/test-app-oauth' });

// Start static server
const server = spawn('python3', ['-m', 'http.server', '-d', 'dist', '3000'], {
  cwd: 'bodhi-browser-ext/tests/test-app-oauth'
});

// Navigate to http://localhost:3000 in Playwright
await page.goto('http://localhost:3000');
```

## File Structure

```
src/
├── constants.ts       # OAuth configuration
├── utils.ts          # OAuth flow utilities and bodhiext integration
├── index.html        # Landing page
├── index.ts          # Landing page logic
├── callback.html     # OAuth callback page
└── callback.ts       # Token exchange logic

dist/                 # Built files (generated)
├── *.html           # HTML files
├── *.js             # Compiled JavaScript
└── *.js.map         # Source maps
```

## Error Handling

Each page includes comprehensive error handling:

- Network failures during OAuth flow
- Missing or invalid tokens
- Browser extension unavailable
- API communication errors

Errors are displayed in dedicated `#error` sections on each page.

## Dependencies

- **TypeScript**: Compilation and type safety
- **Python 3**: Static file serving (built-in HTTP server)
- **Modern Browser**: ES2020, Web Crypto API, Fetch API

## Integration Points

### Browser Extension (`window.bodhiext`)
- `callAPI(method, endpoint, body?, headers?)` - API calls to BodhiApp

### BodhiApp APIs
- `POST /bodhi/v1/auth/request-access` - Get resource scope
- `GET /bodhi/v1/user` - Get authenticated user info

### OAuth Server
- Authorization endpoint for user consent
- Token endpoint for code-to-token exchange

This test app provides a complete reference implementation for third-party OAuth2 integration with BodhiApp. 