/* ==========================================================================
   Unified Design System for OAuth Test App
   ========================================================================== */

/* CSS Variables - Design System Foundation
   ========================================================================== */
:root {
  /* Primary Colors */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #dbeafe;
  
  /* Secondary Colors */
  --color-secondary: #64748b;
  --color-secondary-hover: #475569;
  --color-secondary-light: #f1f5f9;
  
  /* Status Colors */
  --color-success: #059669;
  --color-success-light: #d1fae5;
  --color-warning: #d97706;
  --color-warning-light: #fef3c7;
  --color-error: #dc2626;
  --color-error-light: #fee2e2;
  --color-info: #0891b2;
  --color-info-light: #cffafe;
  
  /* Neutral Colors */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Background Colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;
  
  /* Font Families */
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  
  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  
  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem;   /* 8px */
  --radius-xl: 0.75rem;  /* 12px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* Responsive Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

/* Base Styles and Reset
   ========================================================================== */
* {
  box-sizing: border-box;
}

html {
  line-height: var(--leading-normal);
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  /* Cross-browser font rendering */
  text-rendering: optimizeLegibility;
  -webkit-font-feature-settings: "kern" 1;
  -moz-font-feature-settings: "kern" 1;
  -o-font-feature-settings: "kern" 1;
  font-feature-settings: "kern" 1;
}

body {
  margin: 0;
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-gray-900);
  background-color: var(--color-bg-secondary);
  /* Cross-browser font smoothing */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Prevent horizontal scrolling on mobile */
  overflow-x: hidden;
  /* Improve text rendering */
  text-rendering: optimizeLegibility;
}

/* Typography
   ========================================================================== */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  color: var(--color-gray-900);
}

h1 {
  font-size: var(--text-3xl);
  margin-bottom: var(--space-8);
}

h2 {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-6);
}

h3 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-4);
}

p {
  margin: 0 0 var(--space-4) 0;
  line-height: var(--leading-relaxed);
}

/* Layout Components
   ========================================================================== */
.app-container {
  min-height: 100vh;
  background-color: var(--color-bg-secondary);
  display: flex;
  flex-direction: column;
}

.app-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  background-color: var(--color-bg-primary);
  box-shadow: var(--shadow-sm);
  min-height: calc(100vh - 120px);
}

.container {
  background: var(--color-bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  margin: var(--space-4);
}

.section-container {
  padding: var(--space-8);
}

.section-header {
  margin-bottom: var(--space-8);
  text-align: center;
}

.section-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
}

.section-description {
  font-size: var(--text-lg);
  color: var(--color-gray-600);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* App Header Component
   ========================================================================== */
.app-header {
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.app-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.app-subtitle {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  margin: 0;
}

.app-status-bar {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.extension-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.auth-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

/* Navigation Component
   ========================================================================== */
.app-navigation {
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-gray-200);
  padding: 0 var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-1);
}

.nav-item {
  position: relative;
}

.nav-link {
  display: block;
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--color-gray-600);
  text-decoration: none;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.nav-link:hover:not(.active) {
  color: var(--color-primary);
  background-color: var(--color-gray-50);
}

.nav-link.active {
  color: var(--color-primary);
  background-color: var(--color-bg-secondary);
  border-bottom: 2px solid var(--color-primary);
  font-weight: var(--font-semibold);
}

.nav-link:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

/* Navigation for current pages (simple version) */
nav {
  text-align: center;
  margin-bottom: var(--space-5);
  padding-bottom: var(--space-5);
  border-bottom: 1px solid var(--color-gray-200);
}

nav a {
  margin: 0 var(--space-3);
  color: var(--color-primary);
  text-decoration: none;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all 0.2s ease-in-out;
}

nav a.active {
  color: var(--color-gray-600);
  font-weight: var(--font-bold);
  background-color: var(--color-gray-100);
}

nav a:hover:not(.active) {
  background-color: var(--color-gray-50);
}

/* Navigation User Info Display
   ========================================================================== */
.app-navigation .user-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-gray-200);
  min-width: 200px;
  justify-content: center;
}

.app-navigation .user-info.authenticated {
  color: var(--color-success);
  border-color: var(--color-success);
  background-color: var(--color-success-light);
}

.app-navigation .user-info::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: var(--space-2);
}

/* Button Components
   ========================================================================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  font-family: inherit;
}

.btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: white;
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.btn-danger {
  background-color: var(--color-error);
  color: white;
  border-color: var(--color-error);
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

/* Legacy button support for existing HTML */
button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-family: inherit;
}

button:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
}

button:disabled {
  background-color: var(--color-gray-400);
  cursor: not-allowed;
}

button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button.secondary {
  background-color: var(--color-secondary);
}

button.secondary:hover:not(:disabled) {
  background-color: var(--color-secondary-hover);
}

button.danger {
  background-color: var(--color-error);
}

button.danger:hover:not(:disabled) {
  background-color: #b91c1c;
}

/* Form Components
   ========================================================================== */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--space-3);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-gray-900);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--color-gray-400);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-help {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  margin-top: var(--space-1);
}

/* Legacy form support */
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
  width: 100%;
  padding: var(--space-3);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-gray-900);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  font-family: var(--font-mono);
  box-sizing: border-box;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

/* Form layout components */
.form-section {
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  margin: var(--space-5) 0;
}

.form-section h3 {
  margin-top: 0;
  color: var(--color-gray-700);
}

.form-section .section-header {
  margin-bottom: var(--space-6);
  text-align: left;
}

.form-section .section-title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-2);
}

.form-section .section-description {
  font-size: var(--text-base);
  max-width: none;
  margin: 0;
}

.form-group-section {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
}

.form-group-section:last-of-type {
  border-bottom: none;
  margin-bottom: var(--space-6);
}

.form-group-section h4 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-gray-700);
  margin: 0 0 var(--space-4) 0;
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-start;
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-gray-200);
}

.form-row {
  display: flex;
  gap: var(--space-4);
  align-items: end;
  margin-bottom: var(--space-4);
}

.form-row > div {
  flex: 1;
}

.checkbox-group {
  display: flex;
  gap: var(--space-8);
  margin: var(--space-4) 0;
  align-items: center;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.help-text {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  margin-top: var(--space-1);
  font-weight: var(--font-normal);
}

/* Status Indicator Components
   ========================================================================== */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border-radius: var(--radius-md);
  border: 1px solid;
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-2);
}

.status-ready {
  background-color: var(--color-info-light);
  border-color: var(--color-info);
  color: var(--color-info);
}

.status-ready::before {
  background-color: var(--color-info);
}

.status-calling,
.status-loading {
  background-color: var(--color-warning-light);
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.status-calling::before,
.status-loading::before {
  background-color: var(--color-warning);
  animation: pulse 2s infinite;
}

.status-streaming {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.status-streaming::before {
  background-color: var(--color-primary);
  animation: pulse 2s infinite;
}

.status-completed,
.status-success {
  background-color: var(--color-success-light);
  border-color: var(--color-success);
  color: var(--color-success);
}

.status-completed::before,
.status-success::before {
  background-color: var(--color-success);
}

.status-error {
  background-color: var(--color-error-light);
  border-color: var(--color-error);
  color: var(--color-error);
}

.status-error::before {
  background-color: var(--color-error);
}

/* Legacy status support */
.status {
  padding: var(--space-4);
  margin: var(--space-5) 0;
  border-radius: var(--radius-md);
  text-align: center;
  border: 1px solid;
}

.waiting {
  background-color: var(--color-warning-light);
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.ready {
  background-color: var(--color-success-light);
  border-color: var(--color-success);
  color: var(--color-success);
}

.loading {
  background-color: var(--color-info-light);
  border-color: var(--color-info);
  color: var(--color-info);
}

.success {
  background-color: var(--color-success-light);
  border-color: var(--color-success);
  color: var(--color-success);
}

.error {
  background-color: var(--color-error-light);
  border-color: var(--color-error);
  color: var(--color-error);
}

.processing {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* Card Components
   ========================================================================== */
.card {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-bg-secondary);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-gray-900);
  margin: 0;
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-bg-secondary);
}

/* Info and content sections */
.info {
  background-color: var(--color-info-light);
  border: 1px solid var(--color-info);
  color: var(--color-info);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  margin: var(--space-5) 0;
}

.user-info {
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  margin: var(--space-5) 0;
}

.user-info h3 {
  margin-top: 0;
  color: var(--color-gray-700);
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-gray-200);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: var(--font-semibold);
  color: var(--color-gray-600);
}

.info-value {
  color: var(--color-gray-700);
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

/* Status Section Components
   ========================================================================== */
.status-section {
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  margin: var(--space-5) 0;
}

.status-section h3 {
  margin-top: 0;
  margin-bottom: var(--space-4);
  color: var(--color-gray-700);
}

.status-indicators {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.status-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-600);
  min-width: 80px;
}

/* Output and Code Display
   ========================================================================== */
.output-section {
  margin-top: var(--space-8);
}

.output-section h3 {
  margin-bottom: var(--space-2);
  color: var(--color-gray-900);
}

pre {
  background: var(--color-bg-tertiary);
  padding: var(--space-4);
  overflow-x: auto;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  margin: var(--space-2) 0;
  max-height: 300px;
  overflow-y: auto;
  font-family: var(--font-mono);
  white-space: pre-wrap;
}

.stream-container {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin: var(--space-2) 0;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
  font-family: var(--font-mono);
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  white-space: pre-wrap;
}

/* Test Section Styling */
.test-section {
  margin: var(--space-5) 0;
  padding: var(--space-4);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  background-color: var(--color-bg-primary);
}

/* Badge Components */
.badge {
  display: inline-block;
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  text-transform: uppercase;
  border-radius: var(--radius-sm);
  margin-left: var(--space-2);
}

.badge-success {
  background-color: var(--color-success);
  color: white;
}

.badge-info {
  background-color: var(--color-info);
  color: white;
}

/* Progress Components (for callback page)
   ========================================================================== */
.progress-container {
  margin: var(--space-8) 0;
  padding: var(--space-5);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--radius-lg);
}

.progress-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-5);
  margin-bottom: var(--space-5);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  min-width: 120px;
}

.step-indicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  transition: all 0.3s ease;
}

.step-indicator.completed {
  background-color: var(--color-success);
  color: white;
}

.step-indicator.active {
  background-color: var(--color-primary);
  color: white;
  animation: pulse 2s infinite;
}

.step-indicator.pending {
  background-color: var(--color-gray-200);
  color: var(--color-gray-500);
}

.step-label {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  text-align: center;
}

.step-connector {
  width: 40px;
  height: 2px;
  background-color: var(--color-gray-200);
  margin: 0 var(--space-3);
}

.step-connector.completed {
  background-color: var(--color-success);
}

.success-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-4);
  background-color: var(--color-success);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-2xl);
}

.redirect-info {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  margin-top: var(--space-4);
}

/* Actions Section */
.actions {
  text-align: center;
  margin-top: var(--space-8);
  padding-top: var(--space-5);
  border-top: 1px solid var(--color-gray-200);
}

/* Loading Animations and Transitions
   ========================================================================== */
.spinner {
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-4);
  will-change: transform; /* Performance optimization */
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Micro-interactions and enhanced transitions */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Enhanced button interactions */
.btn {
  position: relative;
  overflow: hidden;
  transform: translateZ(0); /* Performance optimization */
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:active {
  transform: translateY(1px);
}

/* Enhanced card animations */
.card {
  animation: fadeIn 0.3s ease-out;
  transition: box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Status indicator animations */
.status-indicator {
  animation: slideIn 0.3s ease-out;
  transition: all 0.2s ease-in-out;
}

.status-indicator:hover {
  transform: scale(1.05);
}

/* Form input enhancements */
.form-input,
.form-textarea,
.form-select {
  transition: all 0.2s ease-in-out;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  transform: scale(1.02);
}

/* Navigation link enhancements */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width 0.3s ease-in-out;
}

.nav-link:hover::after {
  width: 100%;
}

/* Progress step enhancements */
.step-indicator {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-indicator.active {
  box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* Loading state improvements */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
  animation: fadeIn 0.2s ease-out;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Performance optimizations */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* GPU acceleration for animations */
.btn,
.card,
.status-indicator,
.nav-link,
.step-indicator {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Mobile-First Responsive Design
   ========================================================================== */

/* Base styles are mobile-first (320px+) */

/* Small screens (480px+) */
@media (min-width: 480px) {
  .section-container {
    padding: var(--space-6) var(--space-4);
  }
  
  .form-actions {
    justify-content: flex-start;
  }
  
  .form-actions .btn {
    min-width: 120px;
  }
}

/* Medium screens (640px+) */
@media (min-width: 640px) {
  .app-header {
    flex-direction: row;
    text-align: left;
  }
  
  .app-navigation {
    flex-direction: row;
    padding: 0 var(--space-6);
  }
  
  .nav-list {
    justify-content: flex-start;
  }
  
  .app-navigation .user-info {
    min-width: 200px;
    width: auto;
    margin: 0;
  }
  
  .form-row {
    flex-direction: row;
    align-items: end;
  }
  
  .checkbox-group {
    flex-direction: row;
    align-items: center;
    gap: var(--space-8);
  }
  
  .info-row {
    flex-direction: row;
  }
  
  .info-value {
    text-align: right;
    max-width: 60%;
  }
  
  .progress-steps {
    flex-direction: row;
    gap: var(--space-5);
  }
  
  .step-connector {
    width: 40px;
    height: 2px;
    margin: 0 var(--space-3);
  }
}

/* Large screens (768px+) */
@media (min-width: 768px) {
  .section-container {
    padding: var(--space-8);
  }
  
  .container {
    margin: var(--space-4);
    padding: var(--space-8);
  }
  
  .status-indicators {
    flex-direction: row;
    gap: var(--space-6);
  }
  
  .status-item {
    flex: 1;
    justify-content: center;
  }
}

/* Extra large screens (1024px+) */
@media (min-width: 1024px) {
  .app-main {
    max-width: 1200px;
  }
  
  .section-description {
    max-width: 700px;
  }
  
  .form-section {
    padding: var(--space-6);
  }
}

/* Mobile-specific overrides (max-width approach for specific cases) */
@media (max-width: 639px) {
  /* Mobile-first base styles */
  .container {
    margin: var(--space-2);
    padding: var(--space-4);
  }
  
  .section-title {
    font-size: var(--text-2xl);
  }
  
  h1 {
    font-size: var(--text-2xl);
  }
  
  .btn,
  button {
    width: 100%;
    margin: var(--space-2) 0;
    justify-content: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .btn {
    width: 100%;
  }
  
  /* Navigation mobile styles */
  .app-navigation {
    flex-direction: column;
    gap: var(--space-3);
    padding: var(--space-4);
  }
  
  .nav-list {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .app-navigation .user-info {
    min-width: auto;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }
  
  /* App header mobile styles */
  .app-header {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }
  
  .app-status-bar {
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-2);
  }
  
  /* Form mobile styles */
  .form-row {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .checkbox-group {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  /* Progress mobile styles */
  .progress-steps {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .step-connector {
    width: 2px;
    height: 20px;
    margin: var(--space-1) 0;
  }
  
  /* Info display mobile styles */
  .info-row {
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .info-value {
    text-align: left;
    max-width: 100%;
  }
  
  /* Status indicators mobile */
  .status-indicators {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  /* Navigation links mobile */
  nav a {
    display: block;
    margin: var(--space-1) 0;
  }
}

/* Very small screens (max-width: 479px) */
@media (max-width: 479px) {
  .container {
    padding: var(--space-3);
    margin: var(--space-1);
  }
  
  .section-container {
    padding: var(--space-4);
  }
  
  .app-header {
    padding: var(--space-3);
  }
  
  .app-navigation {
    padding: var(--space-3);
  }
  
  .card-body,
  .card-header {
    padding: var(--space-4);
  }
  
  .form-section {
    padding: var(--space-4);
  }
  
  .nav-link {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
  }
}

/* Accessibility Improvements
   ========================================================================== */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip links for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 1000;
  font-weight: var(--font-medium);
  transition: top 0.2s ease-in-out;
}

.skip-link:focus {
  top: 6px;
}

/* Enhanced focus styles for better keyboard navigation */
.btn:focus-visible,
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
a:focus-visible {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--color-bg-primary);
}

/* Focus styles for interactive elements */
.nav-link:focus-visible {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
}

.status-indicator:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Keyboard navigation improvements */
.btn:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
  z-index: 1;
}

/* Enhanced button states for accessibility */
.btn:disabled,
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn[aria-pressed="true"] {
  background-color: var(--color-primary-hover);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Form validation and error states */
.form-input:invalid,
.form-textarea:invalid,
.form-select:invalid {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px var(--color-error-light);
}

.form-input:invalid:focus,
.form-textarea:invalid:focus,
.form-select:invalid:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px var(--color-error-light);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #0000ff;
    --color-primary-hover: #0000cc;
    --color-success: #008000;
    --color-error: #ff0000;
    --color-warning: #ff8c00;
    --color-gray-900: #000000;
    --color-gray-100: #ffffff;
  }
  
  .btn,
  button {
    border: 2px solid currentColor;
  }
  
  .card {
    border: 2px solid var(--color-gray-900);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .spinner {
    animation: none;
    border: 3px solid var(--color-gray-200);
    border-top: 3px solid var(--color-primary);
  }
}

/* Dark mode support (respects system preference) */
@media (prefers-color-scheme: dark) {
  :root {
    --color-bg-primary: #1f2937;
    --color-bg-secondary: #111827;
    --color-bg-tertiary: #374151;
    --color-gray-900: #f9fafb;
    --color-gray-800: #f3f4f6;
    --color-gray-700: #e5e7eb;
    --color-gray-600: #d1d5db;
    --color-gray-500: #9ca3af;
    --color-gray-400: #6b7280;
    --color-gray-300: #4b5563;
    --color-gray-200: #374151;
    --color-gray-100: #1f2937;
    --color-gray-50: #111827;
  }
  
  .card {
    background-color: var(--color-bg-primary);
    border-color: var(--color-gray-200);
  }
  
  .form-input,
  .form-textarea,
  .form-select {
    background-color: var(--color-bg-primary);
    color: var(--color-gray-900);
    border-color: var(--color-gray-300);
  }
}

/* Print styles */
@media print {
  .skip-link,
  .app-navigation,
  .btn,
  button,
  .spinner,
  .loading-overlay {
    display: none !important;
  }
  
  .app-container {
    background: white !important;
    color: black !important;
  }
  
  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  pre {
    white-space: pre-wrap;
    word-break: break-word;
  }
}

/* Focus management for modal-like overlays */
.loading-overlay {
  /* Trap focus within loading overlay */
  isolation: isolate;
}

/* Improved color contrast ratios */
.status-ready {
  background-color: #e0f2fe;
  border-color: #0277bd;
  color: #01579b;
}

.status-success {
  background-color: #e8f5e8;
  border-color: #2e7d32;
  color: #1b5e20;
}

.status-error {
  background-color: #ffebee;
  border-color: #c62828;
  color: #b71c1c;
}

.status-warning,
.status-calling,
.status-loading {
  background-color: #fff3e0;
  border-color: #ef6c00;
  color: #e65100;
}

/* Enhanced keyboard navigation for complex components */
.progress-steps {
  /* Allow keyboard navigation through progress steps */
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-5);
  margin-bottom: var(--space-5);
}

.step-indicator[tabindex="0"]:focus {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
}

/* Improved form accessibility */
.form-group {
  position: relative;
}

.form-input:focus + .form-help,
.form-textarea:focus + .form-help,
.form-select:focus + .form-help {
  color: var(--color-primary);
  font-weight: var(--font-medium);
}

/* Better error message styling */
.error[role="alert"] {
  border-left: 4px solid var(--color-error);
  padding-left: var(--space-4);
  animation: slideIn 0.3s ease-out;
}

/* Improved status indicator accessibility */
.status-indicator[role="status"] {
  position: relative;
}

.status-indicator[role="status"]::after {
  content: attr(aria-label);
  position: absolute;
  left: 50%;
  bottom: 100%;
  transform: translateX(-50%);
  background: var(--color-gray-900);
  color: var(--color-gray-50);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
  z-index: 1000;
}

.status-indicator[role="status"]:hover::after,
.status-indicator[role="status"]:focus::after {
  opacity: 1;
}invalid:focus,
.form-select:invalid:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px var(--color-error-light);
}

.form-error {
  color: var(--color-error);
  font-size: var(--text-sm);
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.form-error::before {
  content: '⚠';
  font-weight: bold;
}

/* Improved contrast for status indicators */
.status-indicator {
  min-height: 44px; /* Minimum touch target size */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Enhanced loading states */
.spinner {
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-4);
}

@media (prefers-reduced-motion: reduce) {
  .spinner {
    animation: none;
    border-top-color: var(--color-primary);
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-gray-300: #000000;
    --color-gray-600: #000000;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.5);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.5), 0 2px 4px -2px rgb(0 0 0 / 0.5);
  }
  
  .btn,
  button {
    border: 2px solid currentColor;
  }
  
  .status-indicator {
    border-width: 2px;
    font-weight: var(--font-bold);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-bg-primary: #1f2937;
    --color-bg-secondary: #111827;
    --color-bg-tertiary: #374151;
    --color-gray-900: #f9fafb;
    --color-gray-800: #f3f4f6;
    --color-gray-700: #e5e7eb;
    --color-gray-600: #d1d5db;
    --color-gray-500: #9ca3af;
    --color-gray-400: #6b7280;
    --color-gray-300: #4b5563;
    --color-gray-200: #374151;
    --color-gray-100: #1f2937;
    --color-gray-50: #111827;
  }
}

/* Print styles */
@media print {
  .spinner,
  .progress-container,
  button,
  .btn,
  .skip-link {
    display: none !important;
  }
  
  .container {
    box-shadow: none;
    border: 1px solid var(--color-gray-300);
  }
  
  body {
    background-color: white;
    color: black;
  }
  
  .status-indicator {
    border: 1px solid black;
    background: white !important;
    color: black !important;
  }
}

/* Touch device improvements */
@media (hover: none) and (pointer: coarse) {
  .btn,
  button,
  .nav-link,
  input,
  textarea,
  select {
    min-height: 44px; /* Minimum touch target size */
  }
  
  .checkbox-item input[type="checkbox"] {
    min-width: 44px;
    min-height: 44px;
  }
  
  .nav-link {
    padding: var(--space-4) var(--space-6);
  }
}

/* Large text support */
@media (min-resolution: 2dppx) {
  .status-indicator::before {
    width: 10px;
    height: 10px;
  }
}

/* Motion preferences */
@media (prefers-reduced-motion: no-preference) {
  .btn,
  button,
  .nav-link,
  .status-indicator {
    transition: all 0.2s ease-in-out;
  }
  
  .spinner {
    animation: spin 1s linear infinite;
  }
  
  .step-indicator.active::before {
    animation: pulse 2s infinite;
  }
}