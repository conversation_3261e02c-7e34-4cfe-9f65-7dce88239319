<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OAuth Test App - API Test</title>
  <meta name="description" content="API testing interface for Bodhi Browser Extension OAuth integration">
  <meta name="theme-color" content="#2563eb">
  <meta name="color-scheme" content="light">
  <!-- Preload critical CSS for performance -->
  <link rel="preload" href="styles.css" as="style">
  <link rel="stylesheet" href="styles.css">
  <!-- DNS prefetch for potential external resources -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com">
</head>
<body>
  <!-- Skip links for keyboard navigation -->
  <a href="#main-content" class="skip-link">Skip to main content</a>
  <a href="#main-navigation" class="skip-link">Skip to navigation</a>
  
  <div class="app-container">
    <!-- App Header -->
    <header class="app-header" role="banner">
      <div>
        <h1 class="app-title">OAuth Test App</h1>
        <p class="app-subtitle">API Testing Interface</p>
      </div>
      <div class="app-status-bar" role="status" aria-live="polite">
        <div class="extension-status">
          <span id="headerExtensionStatus" aria-label="Extension status">Detecting...</span>
        </div>
        <div class="auth-status">
          <span id="headerAuthStatus" aria-label="Authentication status">Not authenticated</span>
        </div>
      </div>
    </header>

    <!-- Navigation -->
    <nav id="main-navigation" class="app-navigation" role="navigation" aria-label="Main navigation">
      <ul class="nav-list" role="menubar">
        <li class="nav-item" role="none">
          <a href="/" class="nav-link" role="menuitem" data-testid="nav-home">Home</a>
        </li>
        <li class="nav-item" role="none">
          <a href="/api-test.html" class="nav-link active" role="menuitem" aria-current="page" data-testid="nav-api-test">API Test</a>
        </li>
      </ul>
      <div class="user-info" id="userInfo" data-testid="user-info" role="status" aria-live="polite" aria-label="User authentication information">
        <span id="userInfoText">Not authenticated</span>
        <button id="navLogoutButton" class="btn btn-small btn-secondary" style="display: none; margin-left: 10px;" data-testid="nav-logout-button" aria-label="Logout from application">Logout</button>
      </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="app-main" role="main">
      <div class="container">
    
    <div id="error" class="error" role="alert" aria-live="assertive" aria-atomic="true"></div>
    
    <!-- API Configuration Form -->
    <section class="form-section" aria-labelledby="api-config-heading">
      <div class="section-header">
        <h2 id="api-config-heading" class="section-title">API Request Configuration</h2>
        <p class="section-description">Configure and send API requests through the Bodhi extension. This page works with or without authentication.</p>
      </div>
      
      <form id="apiForm" aria-labelledby="api-config-heading">
        <!-- Request Configuration -->
        <fieldset class="form-group-section">
          <legend>Request Configuration</legend>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label" for="apiMethod">HTTP Method</label>
              <select id="apiMethod" class="form-select" data-testid="api-method" aria-describedby="method-help">
                <option value="GET" selected>GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="PATCH">PATCH</option>
                <option value="DELETE">DELETE</option>
              </select>
              <div id="method-help" class="form-help">Select the HTTP method for your API request</div>
            </div>
            <div class="form-group" style="flex: 2;">
              <label class="form-label" for="apiEndpoint">API Endpoint</label>
              <input type="text" id="apiEndpoint" class="form-input" placeholder="/ping" value="/ping" data-testid="api-endpoint" aria-describedby="endpoint-help" required />
              <div id="endpoint-help" class="form-help">Enter the API endpoint path (e.g., /ping, /api/users)</div>
            </div>
          </div>
        </fieldset>

        <!-- Request Data -->
        <fieldset class="form-group-section">
          <legend>Request Data</legend>
          <div class="form-group">
            <label class="form-label" for="apiBody">Request Body (JSON)</label>
            <textarea id="apiBody" class="form-textarea" placeholder='{"message": "Hello, world!"}' data-testid="api-body" aria-describedby="body-help"></textarea>
            <div id="body-help" class="form-help">JSON object or string data for the request body.</div>
          </div>

          <div class="form-group">
            <label class="form-label" for="apiHeaders">Custom Headers (one per line)</label>
            <textarea id="apiHeaders" class="form-textarea" placeholder="X-Custom-Header: value&#10;Content-Type: application/json" data-testid="api-headers" aria-describedby="headers-help"></textarea>
            <div id="headers-help" class="form-help">Format: "Header-Name: value" (one per line). Authentication header will be added automatically if enabled.</div>
          </div>
        </fieldset>
        
        <!-- Request Options -->
        <fieldset class="form-group-section">
          <legend>Request Options</legend>
          <div class="checkbox-group" role="group" aria-labelledby="options-legend">
            <div class="checkbox-item">
              <input type="checkbox" id="streamingMode" data-testid="streaming-mode" aria-describedby="streaming-help">
              <label for="streamingMode">Enable Streaming</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="includeAuth" data-testid="include-auth" aria-describedby="auth-help">
              <label for="includeAuth">Include Authentication Token</label>
            </div>
          </div>
          <div id="streaming-help" class="form-help">Enable streaming mode for real-time response data</div>
          <div id="auth-help" class="form-help">Include authentication token in request headers</div>
        </fieldset>

        <!-- Actions -->
        <div class="form-actions" role="group" aria-label="Form actions">
          <button type="submit" id="submitApiRequest" class="btn btn-primary" data-testid="submit-api-request" aria-describedby="api-config-heading">Send API Request</button>
          <button type="button" id="clearOutput" class="btn btn-secondary" data-testid="clear-output" aria-describedby="clear-help">Clear Output</button>
          <div id="clear-help" class="form-help">Clear all response output and reset the form</div>
        </div>
      </form>
    </section>
    
    <!-- Status Display -->
    <section class="status-section" aria-labelledby="status-heading">
      <h3 id="status-heading">Status</h3>
      <div class="status-indicators" role="group" aria-labelledby="status-heading">
        <div class="status-item">
          <span class="status-label" id="extension-status-label">Extension:</span>
          <div id="extensionStatus" class="status-indicator status-loading" data-testid="extension-status" role="status" aria-live="polite" aria-labelledby="extension-status-label">Detecting...</div>
        </div>
        <div class="status-item">
          <span class="status-label" id="api-status-label">API:</span>
          <div id="apiStatus" class="status-indicator status-ready" data-testid="api-status" role="status" aria-live="polite" aria-labelledby="api-status-label">Ready</div>
        </div>
      </div>
    </section>
    
    <!-- Stream Content (only visible during streaming) -->
    <section id="streamSection" style="display: none;" data-testid="stream-section" aria-labelledby="stream-heading">
      <h3 id="stream-heading">Stream Content</h3>
      <div id="streamContent" class="stream-container" data-testid="stream-content" role="log" aria-live="polite" aria-labelledby="stream-heading"></div>
    </section>
    
    <!-- Response Output -->
    <section class="output-section" aria-labelledby="response-heading">
      <h3 id="response-heading">Response</h3>
      <div>
        <h4 id="status-output-heading">Status:</h4>
        <pre id="responseStatus" data-testid="response-status" role="log" aria-labelledby="status-output-heading">(no response yet)</pre>
      </div>
      <div>
        <h4 id="headers-output-heading">Headers:</h4>
        <pre id="responseHeaders" data-testid="response-headers" role="log" aria-labelledby="headers-output-heading">(no response yet)</pre>
      </div>
      <div>
        <h4 id="body-output-heading">Body:</h4>
        <pre id="responseBody" data-testid="response-body" role="log" aria-labelledby="body-output-heading">(no response yet)</pre>
      </div>
      </div>
    </main>
  </div>

  <script type="module" src="api-test.js"></script>
</body>
</html>