import {
  appCore,
  LoadingManager,
  Utils,
  ExtensionNotFoundError,
  ExtensionTimeoutError,
} from './core.js';
import { loadExtensionClient, ExtensionClient, ServerStateInfo } from './libbodhiext.js';
import { oauthManager } from './oauth.js';

// Global client instance for extension testing
let bodhiExtClient: ExtensionClient | null = null;

async function initializePage(): Promise<void> {
  try {
    appCore.errorManager.clear();

    // Show loading state
    LoadingManager.showLoadingState();

    // Initialize core application systems
    await appCore.initialize();

    // Load and display detailed user info if authenticated
    if (appCore.authManager.isAuthenticated()) {
      await loadUserDetails();
    }

    // Initialize OAuth flow
    await initializeOAuthFlow();

    // Initialize extension testing
    initializeExtensionTesting();

    // Hide loading state
    LoadingManager.hideLoadingState();
  } catch (error) {
    LoadingManager.hideLoadingState();
    appCore.errorManager.handleError(error, 'Failed to initialize');
  }
}

async function initializeOAuthFlow(): Promise<void> {
  try {
    // Wait for bodhiext to be available for OAuth
    await loadExtensionClient({ timeout: 2000 });

    // Show ready status for OAuth
    const waitingStatus = document.getElementById('waitingStatus');
    const readyStatus = document.getElementById('readyStatus');
    const loginButton = document.getElementById('loginButton');
    const logoutButton = document.getElementById('logoutButton');

    if (waitingStatus) waitingStatus.style.display = 'none';
    if (readyStatus) readyStatus.style.display = 'block';

    // Show appropriate button based on authentication state
    if (appCore.authManager.isAuthenticated()) {
      // User is authenticated, show logout button
      if (loginButton) loginButton.style.display = 'none';
      if (logoutButton) logoutButton.style.display = 'inline-block';

      // Set up logout button handler
      if (logoutButton) {
        logoutButton.addEventListener('click', handleLogout);
      }
    } else {
      // User is not authenticated, show login button
      if (loginButton) loginButton.style.display = 'inline-block';
      if (logoutButton) logoutButton.style.display = 'none';

      // Set up login button handler
      if (loginButton) {
        loginButton.addEventListener('click', handleLogin);
      }
    }

    appCore.statusManager.updateStatus('headerExtension', 'ready', 'Ready');
  } catch {
    appCore.statusManager.updateStatus('headerExtension', 'not-found', 'Not found');
  }
}

function initializeExtensionTesting(): void {
  // DOM elements
  const detectButton = document.getElementById('detectExtension');
  const retryButton = document.getElementById('retryDetection');
  const refreshServerStateButton = document.getElementById('refreshServerState');

  // Event listeners
  if (detectButton) {
    detectButton.addEventListener('click', detectExtension);
  }
  if (retryButton) {
    retryButton.addEventListener('click', detectExtension);
  }
  if (refreshServerStateButton) {
    refreshServerStateButton.addEventListener('click', refreshServerState);
  }

  // Expose functions for testing
  (window as any).testFunctions = {
    detectExtension,
    refreshServerState,
    getClient: () => bodhiExtClient,
  };

  // Auto-detect extension for testing (like the original libbodhiext-test.html)
  // This needs to happen after the page is fully loaded
  setTimeout(() => {
    detectExtension();
  }, 100);
}

async function handleLogin(): Promise<void> {
  const loginButton = document.getElementById('loginButton') as HTMLButtonElement;

  try {
    appCore.errorManager.clear();

    if (loginButton) {
      loginButton.disabled = true;
      loginButton.textContent = 'Requesting Access...';
    }

    // Get ExtensionClient instance
    const client = await appCore.authManager.getExtensionClient();

    // Step 1: Request resource access
    await oauthManager.requestResourceAccess(client);

    if (loginButton) {
      loginButton.textContent = 'Redirecting to OAuth...';
    }

    // Step 2: Build auth URL and redirect
    const authUrl = await oauthManager.buildAuthUrl();

    // Redirect to OAuth server
    window.location.href = authUrl;
  } catch (error) {
    appCore.errorManager.handleError(error, 'Login failed');

    if (loginButton) {
      loginButton.disabled = false;
      loginButton.textContent = 'Login with BodhiApp';
    }
  }
}

async function handleLogout(): Promise<void> {
  const logoutButton = document.getElementById('logoutButton') as HTMLButtonElement;

  try {
    appCore.errorManager.clear();

    if (logoutButton) {
      logoutButton.disabled = true;
      logoutButton.textContent = 'Logging out...';
    }

    // Use the centralized logout function
    appCore.authManager.logout();
  } catch (error) {
    appCore.errorManager.handleError(error, 'Logout failed');

    if (logoutButton) {
      logoutButton.disabled = false;
      logoutButton.textContent = 'Logout';
    }
  }
}

// Extension detection function (merged from libbodhiext-test.html)
async function detectExtension(): Promise<void> {
  const extensionStatus = document.getElementById('extensionStatus');
  const extensionId = document.getElementById('extensionId');
  const detectButton = document.getElementById('detectExtension') as HTMLButtonElement;
  const retryButton = document.getElementById('retryDetection');

  if (extensionStatus) {
    extensionStatus.textContent = 'Detecting...';
    extensionStatus.className = 'status-indicator status-loading';
  }
  if (extensionId) {
    extensionId.textContent = 'Not detected';
  }
  if (detectButton) {
    detectButton.disabled = true;
  }
  if (retryButton) {
    retryButton.style.display = 'none';
  }

  Utils.displayResult('detectionOutput', 'Detecting Bodhi extension...', 'loading');
  appCore.statusManager.updateStatus('headerExtension', 'detecting', 'Detecting...');

  try {
    // Use the new loadExtensionClient function
    bodhiExtClient = await loadExtensionClient({ timeout: 2000 });

    // Get the extension ID
    const extId = bodhiExtClient.getExtensionId();

    // Update UI with success
    if (extensionStatus) {
      extensionStatus.textContent = 'Successfully connected to Bodhi browser extension';
      extensionStatus.className = 'status-indicator status-success extension-status detected';
    }
    if (extensionId) {
      extensionId.textContent = `Extension ID: ${extId}`;
    }

    Utils.displayResult(
      'detectionOutput',
      {
        test: 'Extension Detection',
        status: 'success',
        extensionId: extId,
        clientMethods: {
          getExtensionId: typeof bodhiExtClient.getExtensionId,
          sendApiRequest: typeof bodhiExtClient.sendApiRequest,
          sendStreamRequest: typeof bodhiExtClient.sendStreamRequest,
          ping: typeof bodhiExtClient.ping,
          serverState: typeof bodhiExtClient.serverState,
        },
        timestamp: new Date().toISOString(),
      },
      'success'
    );

    appCore.statusManager.updateStatus('headerExtension', 'ready', 'Ready');

    // Show server state section and load server state
    const serverStateSection = document.querySelector('.server-state') as HTMLElement;
    if (serverStateSection) {
      serverStateSection.style.display = 'block';
      // Load server state automatically when extension is detected
      setTimeout(() => {
        refreshServerState();
      }, 100);
    }
  } catch (error) {
    // Update UI with error
    if (extensionStatus) {
      extensionStatus.textContent = 'Not found ✗';
      extensionStatus.className = 'status-indicator status-error';
    }
    if (extensionId) {
      extensionId.textContent = 'Extension not detected';
    }
    if (retryButton) {
      retryButton.style.display = 'inline-block';
    }

    let errorType = 'Unknown Error';
    let errorMessage = error instanceof Error ? error.message : String(error);

    if (error instanceof ExtensionNotFoundError) {
      errorType = 'Extension Not Installed';
      errorMessage =
        'The Bodhi extension is not installed or not enabled. Please install the extension and refresh the page.';
    } else if (error instanceof ExtensionTimeoutError) {
      errorType = 'Extension Timeout';
      errorMessage = 'The extension was found but did not respond in time. Please try again.';
    }

    Utils.displayResult(
      'detectionOutput',
      {
        test: 'Extension Detection',
        status: 'failed',
        errorType: errorType,
        errorMessage: errorMessage,
        originalError: error instanceof Error ? error.message : String(error),
        errorName: error instanceof Error ? error.name : 'Unknown',
        timestamp: new Date().toISOString(),
      },
      'error'
    );

    appCore.statusManager.updateStatus('headerExtension', 'not-found', 'Not found');

    // Hide server state section when extension is not detected
    const serverStateSection = document.querySelector('.server-state') as HTMLElement;
    if (serverStateSection) {
      serverStateSection.style.display = 'none';
    }
  } finally {
    if (detectButton) {
      detectButton.disabled = false;
    }
  }
}

// Load and display detailed user information
async function loadUserDetails(): Promise<void> {
  try {
    // Fetch user info from BodhiApp via extension
    const userInfoResponse = await appCore.authManager.getUserInfo();

    // Display user information in the details section (pass full response)
    displayUserInfo(userInfoResponse);

    // Show user info section
    const userInfoSection = document.getElementById('userInfoSection');
    if (userInfoSection) userInfoSection.style.display = 'block';
  } catch (error) {
    console.error('Failed to load user details:', error);
    // Don't show error here as the navigation will still show basic auth status
  }
}

// Display detailed user information (moved from user.ts)
function displayUserInfo(userInfoResponse: any): void {
  const userDetails = document.getElementById('userDetails');
  if (!userDetails) return;

  // Clear existing content
  userDetails.innerHTML = '';

  // Helper function to create info rows
  function createInfoRow(label: string, value: any, badge?: string): void {
    const row = document.createElement('div');
    row.className = 'info-row';

    const labelSpan = document.createElement('span');
    labelSpan.className = 'info-label';
    labelSpan.textContent = label;

    const valueSpan = document.createElement('span');
    valueSpan.className = 'info-value';

    if (typeof value === 'boolean') {
      valueSpan.textContent = value ? 'Yes' : 'No';
      if (badge) {
        const badgeSpan = document.createElement('span');
        badgeSpan.className = `badge ${badge}`;
        badgeSpan.textContent = value ? 'Active' : 'Inactive';
        valueSpan.appendChild(badgeSpan);
      }
    } else if (Array.isArray(value)) {
      valueSpan.textContent = value.join(', ');
    } else if (value !== null && typeof value === 'object') {
      valueSpan.textContent = JSON.stringify(value, null, 2);
      valueSpan.style.whiteSpace = 'pre-wrap';
      valueSpan.style.fontFamily = 'monospace';
    } else if (value !== null && value !== undefined) {
      valueSpan.textContent = String(value);
    } else {
      valueSpan.textContent = 'N/A';
    }

    row.appendChild(labelSpan);
    row.appendChild(valueSpan);
    userDetails!.appendChild(row);
  }

  // First, display the raw response body as expected by the test
  if (userInfoResponse.body) {
    createInfoRow('Body', userInfoResponse.body);
  }

  // Extract user info from response body for individual field display
  const userInfo = userInfoResponse.body || userInfoResponse;

  // Display common user information fields
  if (userInfo.logged_in !== undefined) {
    createInfoRow('Logged In', userInfo.logged_in, 'badge-success');
  }

  if (userInfo.username) {
    createInfoRow('Username', userInfo.username);
  }

  if (userInfo.email) {
    createInfoRow('Email', userInfo.email);
  }

  if (userInfo.name || userInfo.full_name) {
    createInfoRow('Full Name', userInfo.name || userInfo.full_name);
  }

  if (userInfo.roles && Array.isArray(userInfo.roles)) {
    createInfoRow('Roles', userInfo.roles);
  }

  if (userInfo.scopes && Array.isArray(userInfo.scopes)) {
    createInfoRow('Scopes', userInfo.scopes);
  }

  if (userInfo.client_id) {
    createInfoRow('Client ID', userInfo.client_id);
  }

  if (userInfo.session_id) {
    createInfoRow('Session ID', userInfo.session_id);
  }

  if (userInfo.token_type) {
    createInfoRow('Token Type', userInfo.token_type);
  }

  if (userInfo.expires_in) {
    createInfoRow('Token Expires In', `${userInfo.expires_in} seconds`);
  }

  // Display any additional fields that might be present
  Object.keys(userInfo).forEach(key => {
    if (
      ![
        'logged_in',
        'username',
        'email',
        'name',
        'full_name',
        'roles',
        'scopes',
        'client_id',
        'session_id',
        'token_type',
        'expires_in',
      ].includes(key)
    ) {
      createInfoRow(
        key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        userInfo[key]
      );
    }
  });

  // If no specific fields found, show raw JSON
  if (userDetails.children.length === 0) {
    const pre = document.createElement('pre');
    pre.style.background = '#f8f9fa';
    pre.style.padding = '15px';
    pre.style.borderRadius = '4px';
    pre.style.overflow = 'auto';
    pre.style.fontSize = '14px';
    pre.textContent = JSON.stringify(userInfoResponse, null, 2);
    userDetails.appendChild(pre);
  }
}

// Server state refresh function
async function refreshServerState(): Promise<void> {
  const statusElement = document.getElementById('serverStateStatus');
  const urlElement = document.getElementById('serverStateUrl');
  const errorDetailsElement = document.getElementById('serverStateErrorDetails');
  const errorElement = document.getElementById('serverStateError');
  const refreshButton = document.getElementById('refreshServerState') as HTMLButtonElement;

  if (!bodhiExtClient) {
    if (statusElement) statusElement.textContent = 'Extension not available';
    return;
  }

  try {
    if (refreshButton) refreshButton.disabled = true;
    if (statusElement) statusElement.textContent = 'Loading...';
    if (errorDetailsElement) errorDetailsElement.style.display = 'none';

    const serverState: ServerStateInfo = await bodhiExtClient.serverState();

    // Update status
    if (statusElement) {
      let statusText = `Status: ${serverState.status}`;
      if (serverState.version) {
        statusText += ` (v${serverState.version})`;
      }
      statusElement.textContent = statusText;
    }

    // Update URL
    if (urlElement) {
      urlElement.textContent = serverState.url ? `URL: ${serverState.url}` : 'URL: Not available';
    }

    // Handle error details
    if (serverState.error) {
      if (errorDetailsElement) errorDetailsElement.style.display = 'block';
      if (errorElement) {
        errorElement.textContent = `Error: ${serverState.error.message}`;
        if (serverState.error.type) {
          errorElement.textContent += ` (Type: ${serverState.error.type})`;
        }
      }
    } else {
      if (errorDetailsElement) errorDetailsElement.style.display = 'none';
    }
  } catch (error) {
    if (statusElement) statusElement.textContent = 'Status: error';
    if (errorDetailsElement) errorDetailsElement.style.display = 'block';
    if (errorElement) {
      errorElement.textContent = `Error: ${error instanceof Error ? error.message : String(error)}`;
    }
  } finally {
    if (refreshButton) refreshButton.disabled = false;
  }
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializePage);
} else {
  initializePage();
}
