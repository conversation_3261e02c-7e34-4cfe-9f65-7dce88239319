import {
  loadExtensionClient,
  ExtensionClient,
  ExtensionNotFoundError,
  ExtensionTimeoutError,
} from './libbodhiext.js';
import { appCore, LoadingManager, Utils, FormPersistence } from './core.js';

// Form persistence instance
const formPersistence = new FormPersistence('api-test-');

// Global client instance
let bodhiExtClient: ExtensionClient | null = null;

async function initializeApiTestPage(): Promise<void> {
  try {
    appCore.errorManager.clear();

    // Show loading state for better UX
    LoadingManager.showLoadingState('Loading API test interface...');

    // Initialize core application systems
    await appCore.initialize();

    // Set initial extension status
    appCore.statusManager.updateStatus('extension', 'loading', 'Detecting...');
    appCore.statusManager.updateStatus('api', 'ready', 'Ready');

    // Wait for extension to be available using new API
    try {
      bodhiExtClient = await loadExtensionClient({ timeout: 2000 });

      // Show extension detected status
      appCore.statusManager.updateStatus(
        'extension',
        'success',
        `Connected ✓ (ID: ${bodhiExtClient.getExtensionId()})`
      );
      appCore.statusManager.updateStatus('headerExtension', 'ready', 'Ready');
    } catch (error) {
      let errorMessage = 'Extension detection failed';
      if (error instanceof ExtensionNotFoundError) {
        errorMessage = 'Extension not installed. Please install the Bodhi extension.';
      } else if (error instanceof ExtensionTimeoutError) {
        errorMessage = 'Extension timeout. Please try refreshing the page.';
      }

      appCore.statusManager.updateStatus('extension', 'error', `Not found ✗ - ${errorMessage}`);
      appCore.statusManager.updateStatus('headerExtension', 'not-found', 'Not found');
      appCore.errorManager.show(errorMessage);
      return;
    }

    // Setup form persistence
    formPersistence.setupFormPersistence('apiForm');

    // Setup form submission
    const apiForm = document.getElementById('apiForm') as HTMLFormElement;
    if (apiForm) {
      apiForm.addEventListener('submit', handleApiRequest);
    }

    // Setup clear output button
    const clearButton = document.getElementById('clearOutput');
    if (clearButton) {
      clearButton.addEventListener('click', clearOutput);
    }

    // Setup streaming mode toggle
    const streamingCheckbox = document.getElementById('streamingMode') as HTMLInputElement;
    if (streamingCheckbox) {
      streamingCheckbox.addEventListener('change', handleStreamingModeChange);
    }

    // Hide loading state
    LoadingManager.hideLoadingState();

    console.log('API test page initialized successfully');
  } catch (error) {
    LoadingManager.hideLoadingState();
    appCore.errorManager.handleError(error, 'Failed to initialize API test page');
  }
}

function handleStreamingModeChange(): void {
  const streamingCheckbox = document.getElementById('streamingMode') as HTMLInputElement;
  const streamSection = document.getElementById('streamSection');

  if (streamingCheckbox && streamSection) {
    Utils.toggleElement('streamSection', streamingCheckbox.checked);
  }
}

async function handleApiRequest(event: Event): Promise<void> {
  event.preventDefault();

  try {
    appCore.errorManager.clear();

    if (!bodhiExtClient) {
      throw new Error(
        'Extension not available. Please refresh the page and ensure the extension is installed.'
      );
    }

    // Get form values
    const method = (document.getElementById('apiMethod') as HTMLSelectElement).value.trim();
    const endpoint = (document.getElementById('apiEndpoint') as HTMLInputElement).value.trim();
    const bodyText = (document.getElementById('apiBody') as HTMLTextAreaElement).value.trim();
    const headersText = (document.getElementById('apiHeaders') as HTMLTextAreaElement).value.trim();
    const streamingMode = (document.getElementById('streamingMode') as HTMLInputElement).checked;
    const includeAuth = (document.getElementById('includeAuth') as HTMLInputElement).checked;

    // Validate required fields
    if (!method || !endpoint) {
      throw new Error('Method and endpoint are required');
    }

    // Parse body
    let body: any = {};
    if (bodyText) {
      body = Utils.parseJSON(bodyText);
    }

    // Parse headers
    let headers = Utils.parseHeaders(headersText);

    // Add authentication header if requested
    if (includeAuth) {
      const accessToken = appCore.authManager.getAccessToken();
      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      } else {
        appCore.errorManager.show(
          'Authentication requested but no access token found. Please login first.'
        );
        return;
      }
    }

    // Clear previous output
    clearResponseOutput();

    if (streamingMode) {
      await handleStreamingRequest(method, endpoint, body, headers);
    } else {
      await handleStandardRequest(method, endpoint, body, headers);
    }
  } catch (error) {
    appCore.errorManager.handleError(error, 'Request failed');
  }
}

async function handleStandardRequest(
  method: string,
  endpoint: string,
  body: any,
  headers: Record<string, string>
): Promise<void> {
  try {
    if (!bodhiExtClient) {
      throw new Error('Extension not available');
    }

    // Update status to calling
    appCore.statusManager.updateStatus('api', 'calling', 'Calling API...');

    const response = await bodhiExtClient.sendApiRequest(method, endpoint, body, headers);

    // Update status to completed
    appCore.statusManager.updateStatus('api', 'completed', 'Completed');

    // Display response
    displayResponse(response);
  } catch (error) {
    // Update status to error
    appCore.statusManager.updateStatus('api', 'error', 'Error occurred');
    throw error;
  }
}

async function handleStreamingRequest(
  method: string,
  endpoint: string,
  body: any,
  headers: Record<string, string>
): Promise<void> {
  try {
    if (!bodhiExtClient) {
      throw new Error('Extension not available');
    }

    // Update status to calling
    appCore.statusManager.updateStatus('api', 'calling', 'Calling API...');

    // Ensure streaming body has stream: true
    if (typeof body === 'object' && body !== null) {
      body.stream = true;
    }

    const stream = await bodhiExtClient.sendStreamRequest(method, endpoint, body, headers);

    // Update status to streaming
    appCore.statusManager.updateStatus('api', 'streaming', 'Streaming...');

    // Clear stream content
    const streamContent = document.getElementById('streamContent');
    if (streamContent) {
      streamContent.textContent = '';
    }

    // Show stream section
    Utils.toggleElement('streamSection', true);

    // Process stream chunks
    let lastResponse: any = null;
    for await (const chunk of stream) {
      lastResponse = chunk;

      // Update stream content
      if (streamContent && chunk.body?.choices?.[0]?.delta?.content) {
        streamContent.textContent += chunk.body.choices[0].delta.content;
      }
    }

    // Update status to completed
    appCore.statusManager.updateStatus('api', 'completed', 'Completed');

    // Display final response
    if (lastResponse) {
      displayResponse(lastResponse);
    }
  } catch (error) {
    // Update status to error
    appCore.statusManager.updateStatus('api', 'error', 'Error occurred');
    throw error;
  }
}

function displayResponse(response: any): void {
  // Display status
  let statusOutput = '';
  if (response.status !== undefined) {
    statusOutput = response.status.toString();
    if (response.statusText) {
      statusOutput += ` ${response.statusText}`;
    }
  }
  Utils.updateElementText('responseStatus', statusOutput || 'No status received');

  // Display headers
  let headersOutput = '';
  if (response.headers && Object.keys(response.headers).length > 0) {
    Object.entries(response.headers).forEach(([key, value]) => {
      headersOutput += `${key}: ${value}\n`;
    });
  }
  Utils.updateElementText('responseHeaders', headersOutput || 'No headers received');

  // Display body with JSON formatting if possible
  let bodyOutput = '';
  const bodyData = response.body !== undefined ? response.body : response.data;

  if (bodyData !== undefined) {
    bodyOutput = Utils.formatJSON(bodyData);
  } else if (response.error) {
    bodyOutput = response.error;
  }

  Utils.updateElementText('responseBody', bodyOutput || 'No body received');
}

function clearOutput(): void {
  clearResponseOutput();
  appCore.errorManager.clear();

  // Reset API status to ready
  appCore.statusManager.updateStatus('api', 'ready', 'Ready');

  // Clear stream content
  const streamContent = document.getElementById('streamContent');
  if (streamContent) {
    streamContent.textContent = '';
  }

  // Hide stream section
  Utils.toggleElement('streamSection', false);
}

function clearResponseOutput(): void {
  Utils.updateElementText('responseStatus', '(no response yet)');
  Utils.updateElementText('responseHeaders', '(no response yet)');
  Utils.updateElementText('responseBody', '(no response yet)');
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApiTestPage);
} else {
  initializeApiTestPage();
}
