<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OAuth Test App - Home</title>
  <meta name="description" content="OAuth2 integration test application for Bodhi Browser Extension">
  <meta name="theme-color" content="#2563eb">
  <meta name="color-scheme" content="light">
  <!-- Preload critical CSS for performance -->
  <link rel="preload" href="styles.css" as="style">
  <link rel="stylesheet" href="styles.css">
  <!-- DNS prefetch for potential external resources -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com">
</head>

<body>
  <!-- Skip links for keyboard navigation -->
  <a href="#main-content" class="skip-link">Skip to main content</a>
  <a href="#main-navigation" class="skip-link">Skip to navigation</a>

  <div class="app-container">
    <!-- App Header -->
    <header class="app-header" role="banner">
      <div>
        <h1 class="app-title">OAuth Test App</h1>
        <p class="app-subtitle">Bodhi Browser Extension Integration</p>
      </div>
      <div class="app-status-bar" role="status" aria-live="polite">
        <div class="extension-status">
          <span id="headerExtensionStatus" aria-label="Extension status">Detecting...</span>
        </div>
        <div class="auth-status">
          <span id="headerAuthStatus" aria-label="Authentication status">Not authenticated</span>
        </div>
      </div>
    </header>

    <!-- Navigation -->
    <nav id="main-navigation" class="app-navigation" role="navigation" aria-label="Main navigation">
      <ul class="nav-list" role="menubar">
        <li class="nav-item" role="none">
          <a href="/" class="nav-link active" role="menuitem" aria-current="page" data-testid="nav-home">Home</a>
        </li>
        <li class="nav-item" role="none">
          <a href="/api-test.html" class="nav-link" role="menuitem" data-testid="nav-api-test">API Test</a>
        </li>
      </ul>
      <div class="user-info" id="userInfo" data-testid="user-info" role="status" aria-live="polite"
        aria-label="User authentication information">
        <span id="userInfoText">Not authenticated</span>
        <button id="navLogoutButton" class="btn btn-small btn-secondary" style="display: none; margin-left: 10px;"
          data-testid="nav-logout-button" aria-label="Logout from application">Logout</button>
      </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="app-main" role="main">
      <div class="section-container">
        <!-- Introduction Section -->
        <section class="section-header" aria-labelledby="main-heading">
          <h2 id="main-heading" class="section-title">OAuth2 Integration Test</h2>
          <p class="section-description">
            This application demonstrates OAuth2 integration with BodhiApp through the browser extension.
            Test extension detection, OAuth flow, and API functionality.
          </p>
        </section>

        <!-- Error Display -->
        <div id="error" class="error" style="display: none;" role="alert" aria-live="assertive" aria-atomic="true">
        </div>

        <!-- OAuth Flow Section -->
        <section class="card" data-testid="oauth-section" aria-labelledby="oauth-heading">
          <div class="card-header">
            <h3 id="oauth-heading" class="card-title">OAuth Authentication Flow</h3>
          </div>
          <div class="card-body">
            <div class="info" role="note" aria-label="OAuth flow steps">
              <p><strong>Flow:</strong> Extension Detection → Request Access → OAuth Authorization → User Dashboard</p>
            </div>

            <div id="waitingStatus" class="status waiting" role="status" aria-live="polite"
              aria-label="Extension detection status">
              <div class="spinner" aria-hidden="true"></div>
              <p>Waiting for Bodhi Browser Extension...</p>
              <p><small>Please ensure the extension is installed and enabled</small></p>
            </div>

            <div id="readyStatus" class="status ready" style="display: none;" role="status" aria-live="polite">
              <p>✅ Bodhi Browser Extension detected!</p>
              <button id="loginButton" class="btn btn-primary" data-testid="login-button"
                aria-describedby="oauth-heading">Login with BodhiApp</button>
              <button id="logoutButton" class="btn btn-danger" data-testid="logout-button" style="display: none;"
                aria-describedby="oauth-heading">Logout</button>
            </div>
          </div>
        </section>

        <!-- User Information Section (shown after authentication) -->
        <section class="card" id="userInfoSection" style="display: none;" data-testid="user-info-section"
          aria-labelledby="user-info-heading">
          <div class="card-header">
            <h3 id="user-info-heading" class="card-title">User Information</h3>
          </div>
          <div class="card-body">
            <div id="userDetails" data-testid="user-details" role="region" aria-labelledby="user-info-heading"
              aria-live="polite">
              <!-- User details will be populated here after authentication -->
            </div>
          </div>
        </section>

        <!-- Extension Testing Section -->
        <section class="card" data-testid="extension-test-section" aria-labelledby="extension-test-heading">
          <div class="card-header">
            <h3 id="extension-test-heading" class="card-title">Extension Integration Test</h3>
          </div>
          <div class="card-body">
            <p>Test the loadExtensionClient function for detecting and connecting to the extension.</p>

            <div class="form-group">
              <div class="info-row">
                <span class="info-label" id="extension-status-label">Extension Status:</span>
                <span id="extensionStatus" class="status-indicator status-loading extension-status"
                  data-testid="extension-status" role="status" aria-live="polite"
                  aria-labelledby="extension-status-label">Not tested</span>
              </div>
            </div>

            <div class="form-group">
              <div class="info-row">
                <span class="info-label" id="extension-id-label">Extension ID:</span>
                <span id="extensionId" class="extension-id" data-testid="extension-id"
                  aria-labelledby="extension-id-label">Not detected</span>
              </div>
            </div>

            <div class="actions" role="group" aria-labelledby="extension-test-heading">
              <button id="detectExtension" class="btn btn-secondary" data-testid="detect-extension"
                aria-describedby="extension-test-heading">Detect Extension</button>
              <button id="retryDetection" class="btn btn-secondary" style="display: none;" data-testid="retry-detection"
                aria-describedby="extension-test-heading">Retry Detection</button>
            </div>

            <div class="output-section">
              <h4 id="detection-output-heading">Detection Output</h4>
              <div id="detectionOutput" class="output" data-testid="detection-output" role="log"
                aria-labelledby="detection-output-heading" aria-live="polite">
                <pre>Click "Detect Extension" to test the detection functionality...</pre>
              </div>
            </div>
          </div>
        </section>

        <!-- Server State Section -->
        <section class="card server-state" data-testid="server-state-section" aria-labelledby="server-state-heading"
          style="display: none;">
          <div class="card-header">
            <h3 id="server-state-heading" class="card-title status-title">Server State</h3>
          </div>
          <div class="card-body">
            <p>Current server state information from the Bodhi App server.</p>

            <div class="form-group">
              <div class="info-row">
                <span class="info-label">Status:</span>
                <span id="serverStateStatus" class="status-message" data-testid="server-state-status" role="status"
                  aria-live="polite">Loading...</span>
              </div>
            </div>

            <div class="form-group">
              <div class="info-row">
                <span class="info-label">Server URL:</span>
                <span id="serverStateUrl" class="server-url" data-testid="server-state-url">-</span>
              </div>
            </div>

            <div class="form-group error-details" id="serverStateErrorDetails" style="display: none;">
              <div class="info-row">
                <span class="info-label">Error:</span>
                <span id="serverStateError" class="error-message" data-testid="server-state-error">-</span>
              </div>
            </div>

            <div class="actions" role="group" aria-labelledby="server-state-heading">
              <button id="refreshServerState" class="btn btn-secondary" data-testid="refresh-server-state"
                aria-describedby="server-state-heading">Refresh Server State</button>
            </div>
          </div>
        </section>

      </div>
    </main>
  </div>

  <script type="module" src="index.js"></script>
</body>

</html>