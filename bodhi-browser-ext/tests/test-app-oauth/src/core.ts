/**
 * Core module for test-app-oauth application
 *
 * This module consolidates all shared functionality including:
 * - Type definitions and interfaces
 * - Status management system
 * - Error handling system
 * - Common utilities
 * - Centralized state management
 */

import {
  loadExtensionClient,
  ExtensionClient,
  BodhiExtConfig,
  ExtensionNotFoundError,
  ExtensionTimeoutError,
} from './libbodhiext.js';

// Re-export libbodhiext types for convenience
export { ExtensionNotFoundError, ExtensionTimeoutError, ExtensionClient };
export type { BodhiExtConfig };
import { STORAGE_KEYS } from './constants.js';

// ============================================================================
// TYPE DEFINITIONS AND INTERFACES
// ============================================================================

export interface DisplayResult {
  [key: string]: any;
}

export type DisplayStatus = 'success' | 'error' | 'loading' | 'info';

export interface ApiResponse {
  body: any;
  headers: Record<string, string>;
  status: number;
}

export interface UserDisplayData {
  email?: string;
  loggedIn: boolean;
  tokenType?: string;
  role?: string;
  roleSource?: string;
}

export interface StatusState {
  extension: 'detecting' | 'ready' | 'error' | 'not-found';
  api: 'ready' | 'calling' | 'streaming' | 'completed' | 'error';
  auth: 'unauthenticated' | 'authenticating' | 'authenticated' | 'error';
}

export interface StatusMessage {
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  details?: string;
}

// ============================================================================
// CENTRALIZED STATUS MANAGEMENT SYSTEM
// ============================================================================

export class StatusManager {
  private statusElements: Map<string, HTMLElement> = new Map();
  private currentStatus: Partial<StatusState> = {};
  private callbacks: Map<string, ((status: string, message?: string) => void)[]> = new Map();

  constructor() {
    this.initializeElements();
  }

  private initializeElements(): void {
    // Register common status elements
    const extensionStatus = document.getElementById('extensionStatus');
    const apiStatus = document.getElementById('apiStatus');
    const headerExtensionStatus = document.getElementById('headerExtensionStatus');
    const headerAuthStatus = document.getElementById('headerAuthStatus');

    if (extensionStatus) this.statusElements.set('extension', extensionStatus);
    if (apiStatus) this.statusElements.set('api', apiStatus);
    if (headerExtensionStatus) this.statusElements.set('headerExtension', headerExtensionStatus);
    if (headerAuthStatus) this.statusElements.set('headerAuth', headerAuthStatus);
  }

  /**
   * Register a status element for management
   */
  registerElement(key: string, element: HTMLElement): void {
    this.statusElements.set(key, element);
  }

  /**
   * Update status for a specific element
   */
  updateStatus(elementId: string, status: string, message?: string): void {
    const element = this.statusElements.get(elementId);
    if (element) {
      element.className = `status-indicator status-${status}`;
      element.textContent = message || this.getDefaultMessage(status);
    }

    // Update internal state
    if (elementId === 'extension' || elementId === 'headerExtension') {
      this.currentStatus.extension = status as any;
    } else if (elementId === 'api') {
      this.currentStatus.api = status as any;
    }

    // Notify callbacks
    const callbacks = this.callbacks.get(elementId) || [];
    callbacks.forEach(callback => callback(status, message));
  }

  /**
   * Subscribe to status changes for a specific element
   */
  onStatusChange(elementId: string, callback: (status: string, message?: string) => void): void {
    if (!this.callbacks.has(elementId)) {
      this.callbacks.set(elementId, []);
    }
    this.callbacks.get(elementId)!.push(callback);
  }

  /**
   * Get current status for an element
   */
  getCurrentStatus(elementId: string): string | undefined {
    if (elementId === 'extension' || elementId === 'headerExtension') {
      return this.currentStatus.extension;
    } else if (elementId === 'api') {
      return this.currentStatus.api;
    }
    return undefined;
  }

  private getDefaultMessage(status: string): string {
    const messages: Record<string, string> = {
      detecting: 'Detecting...',
      loading: 'Loading...',
      ready: 'Ready',
      calling: 'Calling API...',
      streaming: 'Streaming...',
      completed: 'Completed',
      success: 'Connected ✓',
      error: 'Error occurred',
      'not-found': 'Not found ✗',
    };
    return messages[status] || status;
  }
}

// ============================================================================
// UNIFIED ERROR HANDLING SYSTEM
// ============================================================================

export class ErrorManager {
  private errorElement: HTMLElement | null = null;

  constructor(errorElementId: string = 'error') {
    this.errorElement = document.getElementById(errorElementId);
  }

  /**
   * Display error message
   */
  show(error: string | Error, context?: string): void {
    const message = error instanceof Error ? error.message : error;
    const fullMessage = context ? `${context}: ${message}` : message;

    if (this.errorElement) {
      this.errorElement.textContent = fullMessage;
      this.errorElement.style.display = 'block';
    }
    console.error('App Error:', fullMessage);
  }

  /**
   * Clear error display
   */
  clear(): void {
    if (this.errorElement) {
      this.errorElement.textContent = '';
      this.errorElement.style.display = 'none';
    }
  }

  /**
   * Handle different types of errors with appropriate messaging
   */
  handleError(error: unknown, context?: string): void {
    if (error instanceof ExtensionNotFoundError) {
      this.show(
        'Extension not installed. Please install the Bodhi extension and refresh the page.',
        context
      );
    } else if (error instanceof ExtensionTimeoutError) {
      this.show('Extension timeout. Please try refreshing the page.', context);
    } else if (error instanceof Error) {
      this.show(error.message, context);
    } else {
      this.show(String(error), context);
    }
  }
}

// ============================================================================
// FORM PERSISTENCE SYSTEM
// ============================================================================

export class FormPersistence {
  private storagePrefix: string;

  constructor(storagePrefix: string) {
    this.storagePrefix = storagePrefix;
  }

  saveValue(key: string, value: string): void {
    try {
      localStorage.setItem(`${this.storagePrefix}${key}`, value);
    } catch (error) {
      console.warn('Failed to save form value:', error);
    }
  }

  loadValue(key: string, defaultValue: string = ''): string {
    try {
      return localStorage.getItem(`${this.storagePrefix}${key}`) || defaultValue;
    } catch (error) {
      console.warn('Failed to load form value:', error);
      return defaultValue;
    }
  }

  setupFormPersistence(formId: string): void {
    const form = document.getElementById(formId) as HTMLFormElement;
    if (!form) {
      console.error(`Form with id '${formId}' not found`);
      return;
    }

    // Load saved values
    const elements = form.elements;
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i] as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
      if (element.id) {
        const savedValue = this.loadValue(element.id);
        if (savedValue) {
          if (element.type === 'checkbox') {
            (element as HTMLInputElement).checked = savedValue === 'true';
          } else {
            element.value = savedValue;
          }
        }
      }
    }

    // Save values on change
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i] as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
      if (element.id) {
        const eventType = element.type === 'checkbox' ? 'change' : 'input';
        element.addEventListener(eventType, () => {
          const value =
            element.type === 'checkbox'
              ? (element as HTMLInputElement).checked.toString()
              : element.value;
          this.saveValue(element.id, value);
        });
      }
    }
  }
}

// ============================================================================
// AUTHENTICATION MANAGEMENT SYSTEM
// ============================================================================

export class AuthManager {
  private userDisplayData: UserDisplayData | null = null;
  private bodhiExtClient: ExtensionClient | null = null;

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
  }

  /**
   * Get access token
   */
  getAccessToken(): string | null {
    return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
  }

  /**
   * Get or create ExtensionClient instance
   */
  async getExtensionClient(): Promise<ExtensionClient> {
    if (!this.bodhiExtClient) {
      this.bodhiExtClient = await loadExtensionClient({ timeout: 2000 });
    }
    return this.bodhiExtClient;
  }

  /**
   * Get user info from API
   */
  async getUserInfo(): Promise<any> {
    const accessToken = this.getAccessToken();
    if (!accessToken) {
      throw new Error('No access token found');
    }

    try {
      const client = await this.getExtensionClient();
      const response = await client.sendApiRequest('GET', '/bodhi/v1/user', undefined, {
        Authorization: `Bearer ${accessToken}`,
      });
      return response;
    } catch (error) {
      throw new Error(
        `Failed to get user info: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Extract simplified user display data
   */
  extractUserDisplayData(userInfoResponse: any): UserDisplayData {
    const userData = userInfoResponse.body || userInfoResponse;

    return {
      email: userData.email || userData.username || 'Unknown user',
      loggedIn: true,
      tokenType: userData.token_type || 'Bearer',
      role: this.extractPrimaryRole(userData.roles),
      roleSource: userData.role_source || 'default',
    };
  }

  /**
   * Extract primary role from roles array
   */
  private extractPrimaryRole(roles: string[] | undefined): string | undefined {
    if (!roles || !Array.isArray(roles) || roles.length === 0) {
      return undefined;
    }

    // Prioritize certain roles
    const priorityRoles = ['admin', 'user', 'developer'];
    for (const priority of priorityRoles) {
      const found = roles.find(role => role.toLowerCase().includes(priority));
      if (found) return found;
    }

    // Return first role if no priority match
    return roles[0];
  }

  /**
   * Logout user
   */
  logout(): void {
    // Clear all stored data
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });

    // Reset client
    this.bodhiExtClient = null;
    this.userDisplayData = null;

    // Redirect to home
    window.location.href = '/';
  }

  /**
   * Get current user display data
   */
  getUserDisplayData(): UserDisplayData | null {
    return this.userDisplayData;
  }

  /**
   * Set user display data
   */
  setUserDisplayData(data: UserDisplayData): void {
    this.userDisplayData = data;
  }
}

// ============================================================================
// USER INFO DISPLAY MANAGER
// ============================================================================

export class UserInfoDisplayManager {
  private userInfoElement: HTMLElement | null = null;
  private userInfoTextElement: HTMLElement | null = null;
  private navLogoutButtonElement: HTMLElement | null = null;
  private authManager: AuthManager;
  private statusManager: StatusManager;

  constructor(authManager: AuthManager, statusManager: StatusManager) {
    this.authManager = authManager;
    this.statusManager = statusManager;
    this.initializeElements();
  }

  private initializeElements(): void {
    this.userInfoElement = document.getElementById('userInfo');
    this.userInfoTextElement = document.getElementById('userInfoText');
    this.navLogoutButtonElement = document.getElementById('navLogoutButton');

    // Set up logout button event listener
    if (this.navLogoutButtonElement) {
      this.navLogoutButtonElement.addEventListener('click', () => {
        this.authManager.logout();
      });
    }
  }

  /**
   * Initialize user info display
   */
  async initialize(): Promise<void> {
    try {
      if (this.authManager.isAuthenticated()) {
        await this.loadAndDisplayUserInfo();
      } else {
        this.displayUnauthenticatedState();
      }
    } catch (error) {
      console.error('Failed to initialize user info:', error);
      this.displayErrorState();
    }
  }

  /**
   * Load and display user info
   */
  async loadAndDisplayUserInfo(): Promise<void> {
    try {
      const userInfoResponse = await this.authManager.getUserInfo();
      const displayData = this.authManager.extractUserDisplayData(userInfoResponse);

      this.authManager.setUserDisplayData(displayData);
      this.displayUserInfo(displayData);
    } catch (error) {
      console.error('Failed to load user info:', error);
      this.displayLimitedAuthenticatedState();
    }
  }

  /**
   * Display user info in navigation
   */
  private displayUserInfo(userData: UserDisplayData): void {
    if (!this.userInfoElement || !this.userInfoTextElement) return;

    let displayText = userData.email || 'Authenticated';
    if (userData.role) {
      displayText += ` (${userData.role})`;
    }

    this.userInfoTextElement.textContent = displayText;
    this.userInfoElement.className = 'user-info authenticated';

    if (this.navLogoutButtonElement) {
      this.navLogoutButtonElement.style.display = 'inline-block';
    }

    this.statusManager.updateStatus('headerAuth', 'authenticated', 'Authenticated');
  }

  /**
   * Display unauthenticated state
   */
  private displayUnauthenticatedState(): void {
    if (!this.userInfoElement || !this.userInfoTextElement) return;

    this.userInfoTextElement.textContent = 'Not authenticated';
    this.userInfoElement.className = 'user-info';

    if (this.navLogoutButtonElement) {
      this.navLogoutButtonElement.style.display = 'none';
    }

    this.statusManager.updateStatus('headerAuth', 'unauthenticated', 'Not authenticated');
  }

  /**
   * Display limited authenticated state
   */
  private displayLimitedAuthenticatedState(): void {
    if (!this.userInfoElement || !this.userInfoTextElement) return;

    this.userInfoTextElement.textContent = 'Authenticated (limited info)';
    this.userInfoElement.className = 'user-info authenticated';

    if (this.navLogoutButtonElement) {
      this.navLogoutButtonElement.style.display = 'inline-block';
    }

    this.statusManager.updateStatus('headerAuth', 'authenticated', 'Authenticated');
  }

  /**
   * Display error state
   */
  private displayErrorState(): void {
    if (!this.userInfoElement || !this.userInfoTextElement) return;

    this.userInfoTextElement.textContent = 'Auth status unknown';
    this.userInfoElement.className = 'user-info';

    this.statusManager.updateStatus('headerAuth', 'error', 'Status unknown');
  }

  /**
   * Refresh user info display
   */
  async refresh(): Promise<void> {
    await this.initialize();
  }
}

// ============================================================================
// COMMON UTILITIES
// ============================================================================

export class Utils {
  /**
   * Parse headers from text format to object
   */
  static parseHeaders(headersText: string): Record<string, string> {
    const headers: Record<string, string> = {};

    if (!headersText.trim()) {
      return headers;
    }

    headersText.split(/\r?\n/).forEach(line => {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const name = line.slice(0, colonIndex).trim();
        const value = line.slice(colonIndex + 1).trim();
        if (name) {
          headers[name] = value;
        }
      }
    });

    return headers;
  }

  /**
   * Format headers object as text
   */
  static formatHeaders(headers: Record<string, string>): string {
    return Object.entries(headers)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  }

  /**
   * Parse JSON safely with error handling
   */
  static parseJSON(jsonString: string): any {
    if (!jsonString.trim()) {
      return {};
    }

    try {
      return JSON.parse(jsonString);
    } catch (error) {
      throw new Error(`Invalid JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Format JSON for display
   */
  static formatJSON(obj: any): string {
    try {
      return JSON.stringify(obj, null, 2);
    } catch {
      return String(obj);
    }
  }

  /**
   * Update element text content safely
   */
  static updateElementText(elementId: string, text: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = text;
    } else {
      console.warn(`Element with id '${elementId}' not found`);
    }
  }

  /**
   * Update element class safely
   */
  static updateElementClass(elementId: string, className: string): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.className = className;
    } else {
      console.warn(`Element with id '${elementId}' not found`);
    }
  }

  /**
   * Show/hide element
   */
  static toggleElement(elementId: string, show: boolean): void {
    const element = document.getElementById(elementId);
    if (element) {
      element.style.display = show ? 'block' : 'none';
    } else {
      console.warn(`Element with id '${elementId}' not found`);
    }
  }

  /**
   * Display result in a formatted output element
   */
  static displayResult(
    outputId: string,
    result: DisplayResult | string,
    status: DisplayStatus = 'success'
  ): void {
    const output = document.getElementById(outputId);
    if (!output) {
      console.error(`Output element with id '${outputId}' not found`);
      return;
    }

    const testOutput =
      typeof result === 'string'
        ? JSON.stringify({ message: result })
        : JSON.stringify(result, null, 2);

    // Add animation for better UX
    output.style.opacity = '0';
    output.innerHTML = `<pre>${testOutput}</pre>`;
    output.className = `output ${status}`;

    // Animate in the result
    requestAnimationFrame(() => {
      output.style.transition = 'opacity 0.3s ease-in-out';
      output.style.opacity = '1';
    });

    if (status === 'error') {
      console.error('Display Error:', testOutput);
    }
  }

  /**
   * Generate random string for PKCE
   */
  static generateRandomString(length: number): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => String.fromCharCode((byte % 26) + 97)).join('');
  }

  /**
   * Generate PKCE challenge
   */
  static async generatePKCEChallenge(verifier: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(verifier);
    const digest = await crypto.subtle.digest('SHA-256', data);
    return btoa(String.fromCharCode(...new Uint8Array(digest)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Wait for element to be available in DOM
   */
  static waitForElement(elementId: string, timeout: number = 5000): Promise<HTMLElement> {
    return new Promise((resolve, reject) => {
      const element = document.getElementById(elementId);
      if (element) {
        resolve(element);
        return;
      }

      const timeoutId = setTimeout(() => {
        reject(new Error(`Element with id '${elementId}' not found within ${timeout}ms`));
      }, timeout);

      const observer = new MutationObserver(() => {
        const element = document.getElementById(elementId);
        if (element) {
          clearTimeout(timeoutId);
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });
    });
  }

  /**
   * Debounce function calls
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: ReturnType<typeof setTimeout>;

    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  /**
   * Get current timestamp
   */
  static getCurrentTimestamp(): number {
    return Date.now();
  }

  /**
   * Format timestamp for display
   */
  static formatTimestamp(timestamp: number): string {
    return new Date(timestamp).toISOString();
  }
}

// ============================================================================
// LOADING STATE MANAGEMENT
// ============================================================================

export class LoadingManager {
  /**
   * Show loading state overlay
   */
  static showLoadingState(message: string = 'Loading application...'): void {
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loadingOverlay';
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
      <div>
        <div class="spinner" aria-hidden="true"></div>
        <p>${message}</p>
      </div>
    `;
    loadingOverlay.setAttribute('aria-label', message);
    document.body.appendChild(loadingOverlay);
  }

  /**
   * Hide loading state overlay
   */
  static hideLoadingState(): void {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
      loadingOverlay.style.opacity = '0';
      setTimeout(() => {
        loadingOverlay.remove();
      }, 200);
    }
  }
}

// ============================================================================
// GLOBAL APPLICATION STATE
// ============================================================================

export class AppCore {
  public statusManager: StatusManager;
  public errorManager: ErrorManager;
  public authManager: AuthManager;
  public userInfoDisplayManager: UserInfoDisplayManager;

  constructor() {
    this.statusManager = new StatusManager();
    this.errorManager = new ErrorManager();
    this.authManager = new AuthManager();
    this.userInfoDisplayManager = new UserInfoDisplayManager(this.authManager, this.statusManager);
  }

  /**
   * Initialize core application systems
   */
  async initialize(): Promise<void> {
    try {
      this.errorManager.clear();

      // Initialize user info display
      await this.userInfoDisplayManager.initialize();
    } catch (error) {
      this.errorManager.handleError(error, 'Failed to initialize application');
    }
  }
}

// Export a global instance for easy access
export const appCore = new AppCore();
