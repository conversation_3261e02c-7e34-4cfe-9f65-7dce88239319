/**
 * OAuth utilities module for test-app-oauth application
 *
 * This module contains all OAuth-related functionality including:
 * - PKCE utilities
 * - OAuth flow functions
 * - Token management
 */

import {
  APP_CLIENT_ID,
  BODHI_AUTH_URL,
  AUTH_REALM,
  REDIRECT_URI,
  STORAGE_KEYS,
} from './constants.js';
import { Utils } from './core.js';

// ============================================================================
// OAUTH FLOW FUNCTIONS
// ============================================================================

export class OAuthManager {
  /**
   * Request resource access from BodhiApp
   */
  async requestResourceAccess(bodhiExtClient: any): Promise<string> {
    try {
      const response = await bodhiExtClient.sendApiRequest(
        'POST',
        '/bodhi/v1/auth/request-access',
        {
          app_client_id: APP_CLIENT_ID,
        }
      );

      if (!response.body.scope) {
        throw new Error('No scope returned from request-access');
      }

      localStorage.setItem(STORAGE_KEYS.RESOURCE_SCOPE, response.body.scope);
      return response.body.scope;
    } catch (error) {
      throw new Error(
        `Failed to request resource access: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Build OAuth authorization URL
   */
  async buildAuthUrl(): Promise<string> {
    const resourceScope = localStorage.getItem(STORAGE_KEYS.RESOURCE_SCOPE);
    if (!resourceScope) {
      throw new Error('Resource scope not found. Call requestResourceAccess first.');
    }

    const state = Utils.generateRandomString(32);
    const codeVerifier = Utils.generateRandomString(128);
    const codeChallenge = await Utils.generatePKCEChallenge(codeVerifier);

    // Store for later use
    localStorage.setItem(STORAGE_KEYS.STATE, state);
    localStorage.setItem(STORAGE_KEYS.CODE_VERIFIER, codeVerifier);

    const scopes = ['openid', 'email', 'profile', 'roles', 'scope_user_user', resourceScope];

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: APP_CLIENT_ID,
      redirect_uri: REDIRECT_URI,
      scope: scopes.join(' '),
      state: state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
    });

    return `${BODHI_AUTH_URL}/realms/${AUTH_REALM}/protocol/openid-connect/auth?${params}`;
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code: string, state: string): Promise<void> {
    const storedState = localStorage.getItem(STORAGE_KEYS.STATE);
    const codeVerifier = localStorage.getItem(STORAGE_KEYS.CODE_VERIFIER);

    if (!storedState || storedState !== state) {
      throw new Error('Invalid state parameter');
    }

    if (!codeVerifier) {
      throw new Error('Code verifier not found');
    }

    const tokenUrl = `${BODHI_AUTH_URL}/realms/${AUTH_REALM}/protocol/openid-connect/token`;

    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: APP_CLIENT_ID,
      code: code,
      redirect_uri: REDIRECT_URI,
      code_verifier: codeVerifier,
    });

    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
    }

    const tokenData = await response.json();

    if (!tokenData.access_token) {
      throw new Error('No access token received');
    }

    // Store tokens
    localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokenData.access_token);
    if (tokenData.refresh_token) {
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokenData.refresh_token);
    }

    // Clean up temporary storage
    localStorage.removeItem(STORAGE_KEYS.STATE);
    localStorage.removeItem(STORAGE_KEYS.CODE_VERIFIER);
  }
}

// Export a global instance for easy access
export const oauthManager = new OAuthManager();
