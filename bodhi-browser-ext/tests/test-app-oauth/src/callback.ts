import { oauthManager } from './oauth.js';
import { appCore } from './core.js';

function updateProgressStep(stepId: string, status: 'completed' | 'active' | 'pending'): void {
  const stepElement = document.getElementById(stepId);
  if (stepElement) {
    stepElement.className = `step-indicator ${status}`;
  }
}

function updateProgressConnector(connectorId: string, completed: boolean): void {
  const connectorElement = document.getElementById(connectorId);
  if (connectorElement) {
    if (completed) {
      connectorElement.classList.add('completed');
    } else {
      connectorElement.classList.remove('completed');
    }
  }
}

async function processCallback(): Promise<void> {
  try {
    appCore.errorManager.clear();

    // Add visual feedback for processing
    addProcessingAnimation();

    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');
    const errorDescription = urlParams.get('error_description');

    // Check for OAuth errors
    if (error) {
      // Update progress to show error state
      updateProgressStep('tokenStep', 'pending');
      updateProgressStep('completeStep', 'pending');
      throw new Error(`OAuth error: ${error}${errorDescription ? ` - ${errorDescription}` : ''}`);
    }

    // Validate required parameters
    if (!code) {
      updateProgressStep('tokenStep', 'pending');
      updateProgressStep('completeStep', 'pending');
      throw new Error('Authorization code not found in callback URL');
    }

    if (!state) {
      updateProgressStep('tokenStep', 'pending');
      updateProgressStep('completeStep', 'pending');
      throw new Error('State parameter not found in callback URL');
    }

    // Exchange code for tokens
    await oauthManager.exchangeCodeForTokens(code, state);

    // Update progress indicators for successful token exchange
    updateProgressStep('tokenStep', 'completed');
    updateProgressConnector('finalConnector', true);
    updateProgressStep('completeStep', 'active');

    // Show success status
    const processingStatus = document.getElementById('processingStatus');
    const successStatus = document.getElementById('successStatus');
    const continueButton = document.getElementById('continueButton');

    if (processingStatus) processingStatus.style.display = 'none';
    if (successStatus) successStatus.style.display = 'block';

    // Brief delay to show success state, then redirect immediately
    setTimeout(() => {
      updateProgressStep('completeStep', 'completed');

      // Redirect immediately after showing completion
      setTimeout(() => {
        window.location.href = '/';
      }, 300);
    }, 500);

    // Show continue button as fallback if redirect fails
    if (continueButton) {
      setTimeout(() => {
        continueButton.style.display = 'block';
      }, 2000);
    }
  } catch (error) {
    // Update progress indicators to show error state
    updateProgressStep('tokenStep', 'pending');
    updateProgressStep('completeStep', 'pending');
    updateProgressConnector('finalConnector', false);

    appCore.errorManager.handleError(error, 'Callback processing failed');

    // Hide processing status
    const processingStatus = document.getElementById('processingStatus');
    if (processingStatus) processingStatus.style.display = 'none';

    // Show manual continue button after error with shorter delay
    setTimeout(() => {
      const continueButton = document.getElementById('continueButton');
      if (continueButton) {
        continueButton.style.display = 'block';
        continueButton.textContent = 'Return to Home';
        continueButton.onclick = () => (window.location.href = '/');
      }
    }, 1000);
  }
}

// Add visual feedback for processing
function addProcessingAnimation(): void {
  const processingStatus = document.getElementById('processingStatus');
  if (processingStatus) {
    processingStatus.style.animation = 'fadeIn 0.3s ease-out';
  }

  const tokenStep = document.getElementById('tokenStep');
  if (tokenStep) {
    tokenStep.style.animation = 'pulse 2s infinite';
  }
}

// Process callback when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', processCallback);
} else {
  processCallback();
}
