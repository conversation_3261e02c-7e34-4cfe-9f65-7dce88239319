/**
 * Bodhi Test Extension content script
 * Injected into pages to test extension-to-extension communication
 */

'use strict';

// Use console namespace to better identify logs
const logger = {
  info: message => console.info(`[BodhiTest/content.js] ${message}`),
  error: (message, error) => console.error(`[BodhiTest/content.js] ${message}`, error),
};

// Track discovery state
let bodhiExtensionDiscovered = false;

// Inject our interface script into the page
const injectScript = () => {
  try {
    // Only inject if document is fully loaded
    if (document.readyState !== 'complete') {
      document.addEventListener('readystatechange', () => {
        if (document.readyState === 'complete') {
          injectScript();
        }
      });
      return;
    }

    // Create and inject the script element
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('inject.js');
    script.onload = function () {
      this.remove(); // Clean up after loading
    };
    (document.head || document.documentElement).appendChild(script);

    logger.info('Interface script injected');

    // Send extension ID to the page so it can be displayed
    window.postMessage(
      {
        type: 'BODHI_TEST_INIT',
        extensionId: chrome.runtime.id,
      },
      '*'
    );
  } catch (error) {
    logger.error('Failed to inject interface script', error);
  }
};

// Handle messages from the page
const handlePageMessage = event => {
  // Ensure the message is from the page we're injected into
  if (event.source !== window) return;

  // Handle various message types
  const { type } = event.data || {};

  if (!type) return;

  // Handle Bodhi extension discovery
  if (type === 'BODHI_EXT_DISCOVERED' && event.data.extensionId) {
    // Skip if already discovered
    if (bodhiExtensionDiscovered) {
      logger.info('Ignoring duplicate Bodhi extension discovery event');
      return;
    }

    logger.info('Discovered Bodhi extension ID from page', { extensionId: event.data.extensionId });
    // Forward this to our background script
    chrome.runtime.sendMessage(
      {
        type: 'BODHI_DISCOVERY_RESULT',
        extensionId: event.data.extensionId,
      },
      response => {
        if (chrome.runtime.lastError) {
          logger.error('Error sending discovery result to background', chrome.runtime.lastError);
        } else {
          logger.info('Successfully notified background of Bodhi extension discovery', response);
          // Mark as discovered after successful notification
          bodhiExtensionDiscovered = true;
        }
      }
    );
    return;
  }

  if (!type.startsWith('BODHI_TEST_') && !type.endsWith('_RESPONSE')) return;

  logger.info('Received message from page', { type, data: event.data });

  // Different actions that can be performed
  switch (type) {
    case 'BODHI_TEST_PING':
      handlePingRequest(event.data);
      break;

    case 'BODHI_TEST_CHAT':
      handleChatRequest(event.data);
      break;

    case 'BODHI_TEST_STREAM':
      handleStreamRequest(event.data);
      break;

    case 'BODHI_TEST_INVALID_ACTION':
      handleInvalidActionRequest(event.data);
      break;

    case 'BODHI_TEST_INVALID_PARAMS':
      handleInvalidParamsRequest(event.data);
      break;

    case 'BODHI_TEST_CHECK_STATUS':
      handleStatusCheck(event.data);
      break;

    // Unified API request (new format)
    case 'BODHI_TEST_API_REQUEST':
      handleUnifiedApiRequest(event.data);
      break;

    // Unified streaming request (new format)
    case 'BODHI_TEST_STREAM_REQUEST':
      handleUnifiedStreamRequest(event.data);
      break;
  }
};

// Handle a ping request
const handlePingRequest = ({ requestId }) => {
  // Forward the ping request to the background script
  chrome.runtime.sendMessage(
    {
      type: 'BODHI_TEST_PING',
      requestId,
    },
    response => {
      // Forward the response back to the page
      window.postMessage(
        {
          ...response,
          type: 'BODHI_TEST_PING_RESPONSE',
        },
        '*'
      );
    }
  );
};

// Handle a chat completion request
const handleChatRequest = ({ requestId, data }) => {
  // Forward the chat request to the background script
  chrome.runtime.sendMessage(
    {
      type: 'BODHI_TEST_CHAT',
      requestId,
      data,
    },
    response => {
      // Forward the response back to the page
      window.postMessage(
        {
          ...response,
          type: 'BODHI_TEST_CHAT_RESPONSE',
        },
        '*'
      );
    }
  );
};

// Handle a streaming request
const handleStreamRequest = ({ requestId, prompt }) => {
  // Create a connection to the background script
  const port = chrome.runtime.connect({ name: 'BODHI_TEST_STREAM_PORT' });

  // Send the streaming request
  port.postMessage({
    type: 'BODHI_TEST_STREAM',
    requestId,
    prompt,
  });

  // Listen for messages from background script
  port.onMessage.addListener(message => {
    // Forward stream chunks to the page
    window.postMessage(
      {
        ...message,
        type: 'BODHI_TEST_STREAM_CHUNK',
      },
      '*'
    );

    // If this is the final chunk, clean up
    if (message.chunk && message.chunk.done) {
      port.disconnect();
    }
  });

  // Handle disconnection
  port.onDisconnect.addListener(() => {
    logger.info('Stream connection closed');
  });
};

// Handle an invalid action request
const handleInvalidActionRequest = ({ requestId }) => {
  // Forward the invalid action request to the background script
  chrome.runtime.sendMessage(
    {
      type: 'BODHI_TEST_INVALID_ACTION',
      requestId,
    },
    response => {
      // Forward the response back to the page
      window.postMessage(
        {
          ...response,
          type: 'BODHI_TEST_INVALID_ACTION_RESPONSE',
        },
        '*'
      );
    }
  );
};

// Handle an invalid parameters request
const handleInvalidParamsRequest = ({ requestId }) => {
  // Forward the invalid params request to the background script
  chrome.runtime.sendMessage(
    {
      type: 'BODHI_TEST_INVALID_PARAMS',
      requestId,
    },
    response => {
      // Forward the response back to the page
      window.postMessage(
        {
          ...response,
          type: 'BODHI_TEST_INVALID_PARAMS_RESPONSE',
        },
        '*'
      );
    }
  );
};

// Handle a status check
const handleStatusCheck = ({ requestId, checkBodhi }) => {
  // If we're checking Bodhi extension status, forward to background
  if (checkBodhi) {
    chrome.runtime.sendMessage(
      {
        type: 'BODHI_TEST_CHECK_BODHI',
        requestId,
      },
      response => {
        // Forward the response back to the page
        window.postMessage(
          {
            ...response,
            type: 'BODHI_TEST_CHECK_BODHI_RESPONSE',
          },
          '*'
        );
      }
    );
    return;
  }

  // If we're checking our own status, respond directly
  window.postMessage(
    {
      type: 'BODHI_TEST_CHECK_STATUS_RESPONSE',
      requestId,
      response: {
        status: 'active',
        extension: 'Bodhi Test Extension',
        id: chrome.runtime.id,
        timestamp: Date.now(),
      },
    },
    '*'
  );
};

// Handle unified API request (new format)
const handleUnifiedApiRequest = ({ requestId, request }) => {
  chrome.runtime.sendMessage(
    {
      type: 'BODHI_TEST_API_REQUEST',
      requestId,
      request,
    },
    response => {
      window.postMessage(
        {
          ...response,
          type: 'BODHI_TEST_API_RESPONSE',
          requestId,
        },
        '*'
      );
    }
  );
};

// Handle unified streaming request (new format)
const handleUnifiedStreamRequest = message => {
  logger.info('Received streaming request', { message });
  const port = chrome.runtime.connect({ name: 'BODHI_TEST_STREAM_PORT' });
  const requestId = message.requestId;
  port.onMessage.addListener(message => {
    logger.info('Forwarding streaming chunk', { message });
    window.postMessage(
      {
        ...message,
        type: 'BODHI_TEST_STREAM_CHUNK',
        requestId,
      },
      '*'
    );
    if (message.response?.body?.done) {
      port.disconnect();
    }
  });
  port.onDisconnect.addListener(() => {
    logger.info('Unified stream connection closed');
  });
  port.postMessage(message);
};

// Set up listeners when content script is loaded
window.addEventListener('message', handlePageMessage);
injectScript();
