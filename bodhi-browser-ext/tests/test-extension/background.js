/**
 * Bodhi Test Extension background script
 * Manages communication with the Bodhi Browser extension
 */

'use strict';

// Use console namespace to better identify logs
const logger = {
  info: message => console.info(`[BodhiTest/background.js] ${message}`),
  error: (message, error) => console.error(`[BodhiTest/background.js] ${message}`, error),
};

// Constants for message types when communicating with Bodhi extension
const MESSAGE_TYPES = {
  API_REQUEST: 'BODHI_API_REQUEST',
  API_RESPONSE: 'BODHI_API_RESPONSE',
  STREAM_REQUEST: 'BODHI_STREAM_REQUEST',
  STREAM_CHUNK: 'BODHI_STREAM_CHUNK',
  STREAM_ERROR: 'BODHI_STREAM_ERROR',
  ERROR: 'BODHI_ERROR',
};

// Discover the Bodhi Browser extension
let bodhiExtensionId = null;

// Listen for the discovery result from content script
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.type === 'BODHI_DISCOVERY_RESULT' && message.extensionId) {
    bodhiExtensionId = message.extensionId;
    logger.info('Received Bodhi extension ID', { bodhiExtensionId });
    sendResponse({ success: true });
  }
});

// Send a generic API request to the Bodhi extension (unified)
const sendGenericApiRequest = (requestId, req) => {
  return new Promise((resolve, reject) => {
    if (!bodhiExtensionId) {
      logger.error('Bodhi extension not found', { requestId });
      reject(new Error('Bodhi extension not found'));
      return;
    }
    chrome.runtime.sendMessage(
      bodhiExtensionId,
      {
        type: MESSAGE_TYPES.API_REQUEST,
        requestId,
        request: req,
      },
      response => {
        if (chrome.runtime.lastError) {
          logger.error('Error sending generic API request', {
            error: chrome.runtime.lastError.message,
            requestId,
          });
          reject(chrome.runtime.lastError);
          return;
        }
        logger.info('Generic API response', { response });
        resolve(response);
      }
    );
  });
};

// Send a generic streaming request to the Bodhi extension (unified)
// Now supports real-time chunk forwarding via sendResponse callback
const sendGenericStreamRequest = (sendResponse, message) => {
  if (!bodhiExtensionId) {
    logger.error('Bodhi extension not found', { requestId: message.requestId });
    sendResponse({ error: 'Bodhi extension not found' });
    return;
  }
  const port = chrome.runtime.connect(bodhiExtensionId);
  port.postMessage({
    ...message,
    type: MESSAGE_TYPES.STREAM_REQUEST,
  });
  port.onMessage.addListener(msg => {
    if (msg.type === MESSAGE_TYPES.STREAM_CHUNK) {
      // Forward each chunk to the sender as soon as it arrives
      sendResponse({ chunk: msg });
      // If done, disconnect
      if (msg.response && msg.response.body && msg.response.body.done) {
        port.disconnect();
      }
    } else if (msg.type === MESSAGE_TYPES.STREAM_ERROR || msg.type === MESSAGE_TYPES.ERROR) {
      sendResponse({ error: msg });
      port.disconnect();
    }
  });
  port.onDisconnect.addListener(() => {
    logger.info('Disconnected from Bodhi extension streaming port');
  });
  // Indicate async response
  return true;
};

// Send an invalid action request to the Bodhi extension
const sendInvalidActionRequest = requestId => {
  return new Promise((resolve, reject) => {
    if (!bodhiExtensionId) {
      logger.error('Bodhi extension not found', { requestId });
      reject(new Error('Bodhi extension not found'));
      return;
    }
    chrome.runtime.sendMessage(
      bodhiExtensionId,
      {
        type: MESSAGE_TYPES.API_REQUEST,
        requestId,
        request: {
          method: 'GET',
          endpoint: '/invalid',
          headers: {},
        },
      },
      response => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
          return;
        }
        resolve(response);
      }
    );
  });
};

// Send an invalid parameters request to the Bodhi extension
const sendInvalidParamsRequest = requestId => {
  return new Promise((resolve, reject) => {
    if (!bodhiExtensionId) {
      logger.error('Bodhi extension not found', { requestId });
      reject(new Error('Bodhi extension not found'));
      return;
    }

    chrome.runtime.sendMessage(
      bodhiExtensionId,
      {
        type: MESSAGE_TYPES.API_REQUEST,
        requestId,
        request: {
          endpoint: '/v1/chat/completions',
          method: 'POST',
          headers: {},
          body: {
            invalid: true,
          },
        },
      },
      response => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
          return;
        }

        resolve(response);
      }
    );
  });
};

// Check the status of the Bodhi extension
const checkBodhiStatus = requestId => {
  return new Promise((resolve, _reject) => {
    if (!bodhiExtensionId) {
      logger.error('Bodhi extension not found', { requestId });
      resolve({
        requestId,
        status: 'not_found',
        extensionId: null,
        timestamp: Date.now(),
      });
      return;
    }
    resolve({
      requestId,
      status: 'active',
      extensionId: bodhiExtensionId,
      timestamp: Date.now(),
    });
  });
};

// Start a streaming chat completion
const startStreamingChat = (port, message) => {
  const requestId = message.requestId;
  if (!bodhiExtensionId) {
    logger.error('Bodhi extension not found', { requestId: message.requestId });
    port.postMessage({
      type: 'BODHI_TEST_STREAM_ERROR',
      error: 'Bodhi extension not found',
      requestId,
    });
    return;
  }
  try {
    // Connect to Bodhi extension for streaming
    const bodhiPort = chrome.runtime.connect(bodhiExtensionId, { name: 'BODHI_CHANNEL' });

    // Listen for stream chunks from Bodhi
    bodhiPort.onMessage.addListener(message => {
      logger.info('Received stream chunk response', { message });
      // Forward the chunk to our content script
      port.postMessage({
        ...message,
        type: 'BODHI_TEST_STREAM_CHUNK',
        requestId,
      });

      // Close the connection when done
      if (message.response?.body?.done) {
        bodhiPort.disconnect();
        return;
      }
    });

    // Handle disconnection
    bodhiPort.onDisconnect.addListener(() => {
      logger.info('Bodhi extension streaming connection closed');
    });

    // Send the streaming request
    const request = {
      ...message,
      type: MESSAGE_TYPES.STREAM_REQUEST,
    };
    logger.info('Forwarding stream request to bodhiext', { request });
    bodhiPort.postMessage(request);
  } catch (error) {
    logger.error('Error starting streaming chat', { error: error.message, requestId });
    port.postMessage({
      type: 'BODHI_TEST_STREAM_ERROR',
      error: error.message,
      requestId,
    });
  }
};

// Listen for one-time messages from content script
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  logger.info('Received message from content script', {
    type: message.type,
    requestId: message.requestId,
  });

  // Dispatch based on presence of 'type' and 'request' (unified) or legacy fields
  if (message.type === 'BODHI_TEST_API_REQUEST' && message.request) {
    sendGenericApiRequest(message.requestId, message.request)
      .then(response => sendResponse(response))
      .catch(error => sendResponse({ error: error.message }));
    return true;
  }
  if (message.type === 'BODHI_TEST_STREAM_REQUEST' && message.request) {
    // For streaming, relay each chunk to the sender as soon as it arrives
    sendGenericStreamRequest(sendResponse, message);
    return true;
  }

  // Handle different types of messages
  switch (message.type) {
    case 'BODHI_DISCOVERY_RESULT':
      if (message.extensionId) {
        bodhiExtensionId = message.extensionId;
        logger.info('Received Bodhi extension ID', { bodhiExtensionId });
        sendResponse({ success: true });
      }
      return true;

    case 'BODHI_TEST_INVALID_ACTION':
      sendInvalidActionRequest(message.requestId)
        .then(response => sendResponse(response))
        .catch(error => sendResponse({ error: error.message }));
      return true; // Will respond asynchronously

    case 'BODHI_TEST_INVALID_PARAMS':
      sendInvalidParamsRequest(message.requestId)
        .then(response => sendResponse(response))
        .catch(error => sendResponse({ error: error.message }));
      return true; // Will respond asynchronously

    case 'BODHI_TEST_CHECK_BODHI':
      checkBodhiStatus(message.requestId)
        .then(response => sendResponse(response))
        .catch(error => sendResponse({ error: error.message }));
      return true; // Will respond asynchronously
  }
});

// Listen for streaming connections from content script
chrome.runtime.onConnect.addListener(port => {
  logger.info('Received connection from content script', { portName: port.name });
  if (port.name === 'BODHI_TEST_STREAM_PORT') {
    port.onMessage.addListener(message => {
      if (message.type === 'BODHI_TEST_STREAM_REQUEST') {
        startStreamingChat(port, message);
      }
    });
  }
});
