{"manifest_version": 3, "name": "Bodhi Test Extension", "version": "0.0.1", "description": "Test extension for testing extension-to-extension communication with Bodhi Browser extension", "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["*://*/*"], "js": ["content.js"], "run_at": "document_idle"}], "web_accessible_resources": [{"resources": ["inject.js"], "matches": ["*://*/*"]}], "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "permissions": ["storage", "scripting", "tabs"], "host_permissions": ["*://*/*"], "externally_connectable": {"matches": ["*://*/*"], "accepts_tls_channel_id": false, "ids": ["*"]}}