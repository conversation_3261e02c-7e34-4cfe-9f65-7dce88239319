/**
 * <PERSON><PERSON> inject script
 * This script is injected into web pages to provide a JavaScript API
 * for testing extension-to-extension communication
 */

(function () {
  'use strict';

  const logger = {
    info: message => console.info(`[BodhiTest/inject.js] ${message}`),
    error: (message, error) => console.error(`[BodhiTest/inject.js] ${message}`, error),
  };

  // Store active stream controllers
  const activeStreams = new Map();

  // Generate unique IDs for requests
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  };

  // Create a promise with external resolve/reject functions
  const createPromiseController = () => {
    let resolve, reject;
    const promise = new Promise((res, rej) => {
      resolve = res;
      reject = rej;
    });
    return { promise, resolve, reject };
  };

  // The bodhiTestExtension API we expose to the page
  const bodhiTestExtension = {
    // Extension information
    extensionId: null,
    bodhiExtensionId: null,

    // Check if <PERSON>dhi extension is available on the page
    discoverBodhiExtension: async () => {
      if (window.bodhiext) {
        try {
          bodhiTestExtension.bodhiExtensionId = await window.bodhiext.getExtensionId();

          // Notify our content script
          window.postMessage(
            {
              type: 'BODHI_EXT_DISCOVERED',
              extensionId: bodhiTestExtension.bodhiExtensionId,
            },
            '*'
          );

          return true;
        } catch (error) {
          logger.error('Error getting Bodhi extension ID:', error);
          return false;
        }
      }
      return false;
    },

    // Check the status of the test extension
    checkStatus: () => {
      const requestId = generateId();
      const controller = createPromiseController();

      window.postMessage(
        {
          type: 'BODHI_TEST_CHECK_STATUS',
          requestId,
        },
        '*'
      );

      // Wait for response
      listenForResponse(requestId, 'BODHI_TEST_CHECK_STATUS_RESPONSE', controller);

      return controller.promise;
    },

    // Check the status of the Bodhi extension
    checkBodhiStatus: () => {
      const requestId = generateId();
      const controller = createPromiseController();
      listenForResponse(requestId, 'BODHI_TEST_CHECK_BODHI_RESPONSE', controller);
      window.postMessage(
        {
          type: 'BODHI_TEST_CHECK_STATUS',
          requestId,
          checkBodhi: true,
        },
        '*'
      );
      return controller.promise;
    },

    // Send a ping request to test basic communication
    sendPing: () => {
      const requestId = generateId();
      const controller = createPromiseController();

      window.postMessage(
        {
          type: 'BODHI_TEST_PING',
          requestId,
        },
        '*'
      );

      // Wait for response
      listenForResponse(requestId, 'BODHI_TEST_PING_RESPONSE', controller);

      return controller.promise;
    },

    // Send a chat completion request (legacy)
    sendChatCompletion: data => {
      const requestId = generateId();
      const controller = createPromiseController();

      window.postMessage(
        {
          type: 'BODHI_TEST_CHAT',
          requestId,
          data,
        },
        '*'
      );

      // Wait for response
      listenForResponse(requestId, 'BODHI_TEST_CHAT_RESPONSE', controller);

      return controller.promise;
    },

    // Send a generic API request using the unified format
    sendApiRequest: (method, endpoint, body, headers) => {
      const requestId = generateId();
      const controller = createPromiseController();
      window.postMessage(
        {
          type: 'BODHI_TEST_API_REQUEST',
          requestId,
          request: { method, endpoint, body, headers },
        },
        '*'
      );
      listenForResponse(requestId, 'BODHI_TEST_API_RESPONSE', controller);
      return controller.promise;
    },

    // Start a streaming chat completion (legacy)
    startStreamRequest: (params, callbacks) => {
      logger.info('Sending streaming request', { params });
      const requestId = generateId();
      const onChunk = callbacks.onChunk || (() => {});
      const onError = callbacks.onError || (() => {});
      activeStreams.set(requestId, { onChunk, onError });
      window.postMessage(
        {
          type: 'BODHI_TEST_STREAM_REQUEST',
          requestId,
          ...params,
        },
        '*'
      );
      const listener = event => {
        // Ensure message is from our page
        if (event.source !== window) return;

        const { type } = event.data || {};

        // Check if this is the response we're waiting for
        if (type === 'BODHI_TEST_STREAM_CHUNK' && event.data.requestId === requestId) {
          logger.info('Received chunk for request', { requestId, data: event.data });
          if (event.data.response.status < 200 || event.data.response.status >= 300) {
            activeStreams.delete(requestId);
            window.removeEventListener('message', listener);
            callbacks.onError(new Error(event.data.response.error));
          } else {
            callbacks.onChunk(event.data);
          }
        }
        if (event.data.response?.body?.done) {
          activeStreams.delete(requestId);
          window.removeEventListener('message', listener);
          callbacks.onComplete();
        }
      };
      // Add listener
      window.addEventListener('message', listener);
      // Set a timeout
      setTimeout(() => {
        window.removeEventListener('message', listener);
        callbacks.onError(new Error('Timeout waiting for response'));
      }, 60000); // 60 second timeout
      return {
        requestId,
        cancel: () => {
          activeStreams.delete(requestId);
        },
      };
    },

    // Send an invalid action to test error handling
    sendInvalidAction: () => {
      const requestId = generateId();
      const controller = createPromiseController();

      window.postMessage(
        {
          type: 'BODHI_TEST_INVALID_ACTION',
          requestId,
        },
        '*'
      );

      // Wait for response
      listenForResponse(requestId, 'BODHI_TEST_INVALID_ACTION_RESPONSE', controller);

      return controller.promise;
    },

    // Send invalid parameters to test error handling
    sendInvalidParams: () => {
      const requestId = generateId();
      const controller = createPromiseController();

      window.postMessage(
        {
          type: 'BODHI_TEST_INVALID_PARAMS',
          requestId,
        },
        '*'
      );

      // Wait for response
      listenForResponse(requestId, 'BODHI_TEST_INVALID_PARAMS_RESPONSE', controller);

      return controller.promise;
    },
  };

  // Listen for a response to a specific request
  const listenForResponse = (requestId, responseType, controller) => {
    const listener = event => {
      // Ensure message is from our page
      if (event.source !== window) return;

      const { type, error } = event.data || {};

      // Check if this is the response we're waiting for
      if (type === responseType && event.data.requestId === requestId) {
        logger.info('Received response for request', { requestId, data: event.data });
        // Remove the listener
        window.removeEventListener('message', listener);

        if (error) {
          controller.reject(new Error(error));
        } else {
          controller.resolve(event.data);
        }
      }
    };

    // Add listener
    window.addEventListener('message', listener);

    // Set a timeout
    setTimeout(() => {
      window.removeEventListener('message', listener);
      controller.reject(new Error('Timeout waiting for response'));
    }, 60000); // 60 second timeout
  };

  // Handle incoming messages
  const handleMessage = event => {
    // Ensure message is from our page
    if (event.source !== window) return;

    const { type, extensionId } = event.data || {};

    // Handle extension initialization
    if (type === 'BODHI_TEST_INIT' && extensionId) {
      bodhiTestExtension.extensionId = extensionId;
      logger.info('Bodhi Test Extension initialized', { extensionId });

      // Dispatch an initialization event similar to what bodhiext does
      window.dispatchEvent(
        new CustomEvent('bodhitest:initialized', {
          detail: { extensionId: extensionId },
        })
      );

      // Try to discover bodhiext immediately
      bodhiTestExtension.discoverBodhiExtension();
    }
  };

  // Listen for messages
  window.addEventListener('message', handleMessage);

  // Expose the API to the page
  window.bodhiTestExtension = bodhiTestExtension;

  // Signal that we're loaded
  logger.info('Bodhi Test Extension API injected');

  // Track if Bodhi extension has been discovered
  let bodhiExtDiscovered = false;

  // Function to attempt discovery of the Bodhi extension
  const attemptDiscovery = async () => {
    // If already discovered, no need to continue
    if (bodhiExtDiscovered) {
      clearInterval(discoveryInterval);
      return;
    }

    try {
      // Check if bodhiext exists
      if (window.bodhiext) {
        try {
          const extensionId = await window.bodhiext.getExtensionId();
          logger.info('Bodhi Test Extension found Bodhi extension with polling', { extensionId });

          // Store the ID in our interface
          bodhiTestExtension.bodhiExtensionId = extensionId;

          // Send the extension ID to our content script
          window.postMessage(
            {
              type: 'BODHI_EXT_DISCOVERED',
              extensionId: extensionId,
            },
            '*'
          );

          // Mark as discovered
          bodhiExtDiscovered = true;

          // We've found it, no need to keep polling
          clearInterval(discoveryInterval);
        } catch (error) {
          logger.error('Error getting Bodhi extension ID:', error);
        }
      }
    } catch (error) {
      logger.error('Error during Bodhi extension discovery', error);
    }
  };

  // Try discovery immediately
  attemptDiscovery();

  // Keep trying periodically but with a longer interval (every 5 seconds)
  const discoveryInterval = setInterval(attemptDiscovery, 5000);

  // Add an event listener for the bodhiext:initialized event
  window.addEventListener('bodhiext:initialized', event => {
    // If already discovered, no need to handle
    if (bodhiExtDiscovered) return;

    logger.info('Received bodhiext:initialized event');
    const extensionId = event.detail.extensionId;

    // Forward this to the window for the content script to pick up
    window.postMessage(
      {
        type: 'BODHI_EXT_DISCOVERED',
        extensionId: extensionId,
      },
      '*'
    );

    // Mark as discovered
    bodhiExtDiscovered = true;

    // Clear the polling interval
    clearInterval(discoveryInterval);
  });
})();
