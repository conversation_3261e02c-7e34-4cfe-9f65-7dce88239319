import { describe, test, expect, beforeAll, beforeEach, afterEach, afterAll } from 'vitest';
import {
  waitForBodhiExtInfo,
  waitForTestOutput,
  getTestResult,
  createBrowserManager,
  BrowserManager,
  startTestEnvironment,
  stopTestEnvironment,
  TestEnvironment,
} from './test-helpers';
import { Page } from 'playwright';

describe('Extension-to-Extension', () => {
  let browserManager: BrowserManager;
  let page: Page;
  let staticPort: number;
  let mockPort: number;
  let testEnv: TestEnvironment;

  beforeAll(async () => {
    // Start test servers for this file
    testEnv = await startTestEnvironment();
    staticPort = testEnv.staticPort;
    mockPort = testEnv.mockPort!;

    // Launch browser with test extension
    browserManager = await createBrowserManager({
      loadTestExtension: true,
      staticPort,
      mockPort,
    });
  });

  afterAll(async () => {
    // Clean up browser and servers
    if (browserManager) await browserManager.cleanup();
    if (testEnv) await stopTestEnvironment(testEnv);
  });

  beforeEach(async () => {
    // Create a fresh page for each test to ensure isolation
    page = await browserManager.createPage();

    // Ensure we have access to the required objects
    expect(page).toBeDefined();
    expect(staticPort).toBeDefined();
    expect(mockPort).toBeDefined();
  });

  afterEach(async () => {
    // Clean up the page after each test
    if (page) {
      await page.close().catch(() => {});
    }
  });

  test('Test extension should be loaded and able to detect Bodhi extension', async () => {
    // Navigate to the extension status page
    await page.goto(`http://localhost:${staticPort}/ext2ext-id.html`);
    await waitForBodhiExtInfo(page);

    // Now check if the test extension can detect it
    await page.click('#checkBodhiExtension');
    await waitForTestOutput(page, 'testOutput', 3000);

    // Get the result from the page
    const result = await getTestResult(page, 'testOutput');
    expect(result).toBeDefined();
    expect(result.result.status).toBe('active');
  });

  const unifiedApiTestCases = [
    {
      name: 'should receive pong from /ping',
      apiMethod: 'GET',
      apiEndpoint: '/ping',
      apiBody: '',
      apiHeaders: '',
      expected: { message: 'pong' },
    },
    {
      name: 'should receive chat response from /v1/chat/completions',
      apiMethod: 'POST',
      apiEndpoint: '/v1/chat/completions',
      apiBody: JSON.stringify({
        model: 'test-model',
        messages: [{ role: 'user', content: 'Test message' }],
      }),
      apiHeaders: '',
      expectedFn: async (result: any) => {
        expect(result.data.response.body.choices[0].message.content).toEqual(
          'This is a mock response to: Test message'
        );
      },
    },
    {
      name: 'should handle error responses for invalid action',
      apiMethod: 'POST',
      apiEndpoint: '/invalid-action',
      apiBody: '{}',
      apiHeaders: '{}',
      expectedFn: async (result: any) => {
        expect(result).toBeDefined();
        expect(result.data.response.body).toContain('Cannot POST /invalid-action');
        expect(result.data.response.status).toBe(404);
      },
    },
  ];

  test.each(unifiedApiTestCases)(
    '$name',
    async ({ apiMethod, apiEndpoint, apiBody = '', apiHeaders = '', expected, expectedFn }) => {
      await page.goto(`http://localhost:${staticPort}/ext2ext-api-test.html`);
      await waitForBodhiExtInfo(page);
      await page.locator('#apiMethod').fill(apiMethod);
      await page.locator('#apiEndpoint').fill(apiEndpoint);
      await page.locator('#apiBody').fill(apiBody);
      await page.locator('#apiHeaders').fill(apiHeaders);
      await page.click('#submitApiRequest');
      await waitForTestOutput(page, 'apiPingOutput');
      const result = await getTestResult(page, 'apiPingOutput');
      if (expected) {
        expect(result.data.response.body).toEqual(expected);
      }
      if (expectedFn) {
        await expectedFn(result);
      }
    }
  );

  const unifiedApiStreamingTestCases = [
    {
      name: 'should receive streaming chat completion',
      streamMethod: 'POST',
      streamEndpoint: '/v1/chat/completions',
      streamBody: JSON.stringify({
        stream: true,
        model: 'test-model',
        messages: [{ role: 'user', content: 'Stream test from extension-to-extension test' }],
      }),
      streamHeaders: '{}',
      expectedFn: async (output: any, content: string | null) => {
        expect(output).toBeDefined();
        expect(content).toContain('Stream test from extension-to-extension test');
        expect(output.completed).toBe(true);
      },
    },
    // Add more streaming cases here as needed
  ];

  test.each(unifiedApiStreamingTestCases)(
    '$name',
    async ({ streamMethod, streamEndpoint, streamBody, streamHeaders, expectedFn }) => {
      await page.goto(`http://localhost:${staticPort}/ext2ext-streaming-test.html`);
      await waitForBodhiExtInfo(page);
      await page.locator('#streamMethod').fill(streamMethod);
      await page.locator('#streamEndpoint').fill(streamEndpoint);
      await page.locator('#streamBody').fill(streamBody);
      await page.locator('#streamHeaders').fill(streamHeaders);
      await page.click('#submitStreamRequest');
      await waitForTestOutput(page, 'streamOutput', 3000);
      const output = await getTestResult(page, 'streamOutput');
      const content = await page.locator('#streamContent').textContent();
      await expectedFn(output, content);
    }
  );
});
