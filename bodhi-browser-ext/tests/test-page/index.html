<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bodhi Browser Extension Tests</title>
  <link rel="stylesheet" href="css/styles.css">
  <script src="js/common.js"></script>
</head>

<body>
  <h1>Bodhi Browser Extension Tests</h1>
  <ul>
    <li><strong>Bodhi Extension ID:</strong> <span id="bodhiExtensionId">Detecting...</span></li>
  </ul>
  <div class="test-section">
    <h2>Available Tests</h2>
    <p>Choose a test area to run:</p>

    <div class="test-links">
      <h3>Webpage to Extension Tests</h3>
      <a href="web2ext-id.html" class="test-link">Extension ID Test</a>
      <a href="web2ext-exists.html" class="test-link">bodhiext Object Test</a>
      <a href="web2ext-api-test.html" class="test-link">API Test</a>
      <a href="web2ext-streaming-test.html" class="test-link">Streaming Test</a>

      <h3>Extension to Extension Tests</h3>
      <a href="ext2ext-id.html" class="test-link">Extension ID Test</a>
      <a href="ext2ext-api-test.html" class="test-link">API Ping Test</a>
      <a href="ext2ext-streaming-test.html" class="test-link">API Streaming Test</a>
    </div>

    <div class="info-block">
      <p><strong>Note:</strong> Make sure both extensions are loaded before running tests.</p>
    </div>
  </div>

  <script>
    // Start checking for extension IDs
    window.addEventListener('load', updateExtensionIds);
  </script>
</body>

</html>