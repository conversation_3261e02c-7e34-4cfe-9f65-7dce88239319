<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Extension to Extension - Status Detection Test</title>
  <link rel="stylesheet" href="css/styles.css">
  <script src="js/common.js"></script>
</head>

<body>
  <h1>Extension to Extension - Status Detection Test</h1>

  <div class="setup-info">
    <h3>Test Environment</h3>
    <p>This page tests if one extension can detect the presence and status of the Bodhi extension through extension
      messaging</p>
    <ul>
      <li><strong>Bodhi Extension ID:</strong> <span id="bodhiExtensionId">Detecting...</span></li>
    </ul>
  </div>

  <div class="test-section">
    <h2>Extension Status</h2>
    <p>Check the status of both extensions.</p>
    <button id="checkTestExtension">Check Test Extension</button>
    <button id="checkBodhiExtension">Check Bodhi Extension</button>
    <div id="testOutput" class="output">
      <pre>Test results will appear here...</pre>
    </div>
  </div>

  <script>
    // Get the actual extension ID from the page and inject it into the test
    async function updateExtensionStatus() {
      let extensionId = 'unknown';
      
      if (window.bodhiext) {
        try {
          extensionId = await window.bodhiext.getExtensionId();
        } catch (error) {
          console.error('Error getting extension ID:', error);
        }
      }
      
      // Update the initial output with the actual extension ID
      displayResult('testOutput', {
        test: 'extension status',
        status: 'active',
        extensionId: extensionId,
        timestamp: Date.now()
      });
    }
    
    // Initialize with the extension status
    updateExtensionStatus();

    // Additional modification: fetch the test extension ID
    document.getElementById('checkBodhiExtension').addEventListener('click', async function () {
      let extensionId;
      
      // First try to update the extension ID on this page
      if (window.bodhiext) {
        try {
          extensionId = await window.bodhiext.getExtensionId();
          
          displayResult(
            'testOutput',
            {
              test: 'bodhi extension check',
              message: 'Checking Bodhi extension status...',
              timestamp: Date.now()
            },
            'loading',
          );
        } catch (error) {
          displayResult('testOutput', {
            test: 'bodhi extension check',
            error: 'Failed to get Bodhi extension ID: ' + error.message,
            timestamp: Date.now()
          }, 'error');
          return;
        }
      } else {
        displayResult('testOutput', {
          test: 'bodhi extension check',
          error: 'Bodhi extension not found',
          timestamp: Date.now()
        }, 'error');
        return;
      }
      try {
        const result = await window.bodhiTestExtension.checkBodhiStatus();
        displayResult('testOutput', {
          test: 'bodhi extension check',
          result,
          timestamp: Date.now()
        });
      } catch (error) {
        displayResult('testOutput', {
          test: 'bodhi extension check',
          error: error.message,
          timestamp: Date.now()
        }, 'error');
      }
    });

    // The rest of the event listeners remain the same
    document.getElementById('checkTestExtension').addEventListener('click', async function () {
      displayResult(
        'testOutput',
        {
          test: 'test extension check',
          message: 'Checking test extension status...',
          timestamp: Date.now()
        },
        'loading',
      );
      try {
        const result = await window.bodhiTestExtension.checkStatus();
        displayResult('testOutput', {
          test: 'test extension check',
          status: result.status,
          extensionId: result.extensionId,
          timestamp: Date.now()
        });
      } catch (error) {
        displayResult('testOutput', {
          test: 'test extension check',
          error: error.message,
          timestamp: Date.now()
        }, 'error');
      }
    });
  </script>
</body>

</html>