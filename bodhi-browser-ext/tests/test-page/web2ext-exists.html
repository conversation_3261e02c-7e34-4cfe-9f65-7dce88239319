<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Webpage to Extension - Extension Detection Test</title>
  <link rel="stylesheet" href="css/styles.css">
  <script src="js/common.js"></script>
  <script type="module" src="../../test-app-oauth/src/libbodhiext.js"></script>
</head>

<body>
  <h1>Webpage to Extension - Extension Detection Test</h1>

  <div class="setup-info">
    <h3>Test Environment</h3>
    <p>This page tests the new loadExtensionClient function for detecting and connecting to the Bodhi extension</p>
    <ul>
      <li><strong>Extension Status:</strong> <span id="extensionStatus">Detecting...</span></li>
      <li><strong>Bodhi Extension ID:</strong> <span id="bodhiExtensionId">Not detected</span></li>
    </ul>
  </div>

  <div class="test-section">
    <h2>Extension Detection Test</h2>
    <p>Test the new loadExtensionClient function to detect and connect to the extension.</p>
    <button id="detectExtension">Detect Extension</button>
    <button id="retryDetection" style="display: none;">Retry Detection</button>
    <div id="testOutput" class="output">
      <pre>Click "Detect Extension" to test the new detection functionality...</pre>
    </div>
  </div>

  <script type="module">
    let bodhiExtClient = null;

    // Import the new loadExtensionClient function
    import { loadExtensionClient, ExtensionNotFoundError, ExtensionTimeoutError } from '../../test-app-oauth/src/libbodhiext.js';

    async function detectExtension() {
      setLoading('testOutput', 'Detecting Bodhi extension...');
      document.getElementById('extensionStatus').textContent = 'Detecting...';
      document.getElementById('bodhiExtensionId').textContent = 'Not detected';

      try {
        bodhiExtClient = await loadExtensionClient();

        // Get the extension ID
        const extensionId = bodhiExtClient.getExtensionId();

        // Update UI with success
        document.getElementById('extensionStatus').textContent = 'Connected ✓';
        document.getElementById('extensionStatus').style.color = '#28a745';
        document.getElementById('bodhiExtensionId').textContent = extensionId;
        document.getElementById('retryDetection').style.display = 'none';

        displayResult(
          'testOutput',
          {
            test: 'Extension Detection',
            status: 'success',
            extensionId: extensionId,
            clientMethods: {
              getExtensionId: typeof bodhiExtClient.getExtensionId,
              sendApiRequest: typeof bodhiExtClient.sendApiRequest,
              sendStreamRequest: typeof bodhiExtClient.sendStreamRequest,
              ping: typeof bodhiExtClient.ping
            },
            timestamp: Date.now(),
          },
          'success',
        );

      } catch (error) {
        // Update UI with error
        document.getElementById('extensionStatus').textContent = 'Not found ✗';
        document.getElementById('extensionStatus').style.color = '#dc3545';
        document.getElementById('bodhiExtensionId').textContent = 'Extension not detected';
        document.getElementById('retryDetection').style.display = 'inline-block';

        let errorType = 'Unknown Error';
        let errorMessage = error.message;

        if (error instanceof ExtensionNotFoundError) {
          errorType = 'Extension Not Installed';
          errorMessage = 'The Bodhi extension is not installed or not enabled. Please install the extension and refresh the page.';
        } else if (error instanceof ExtensionTimeoutError) {
          errorType = 'Extension Timeout';
          errorMessage = 'The extension was found but did not respond in time. Please try again.';
        }

        displayResult(
          'testOutput',
          {
            test: 'Extension Detection',
            status: 'failed',
            errorType: errorType,
            errorMessage: errorMessage,
            originalError: error.message,
            timestamp: Date.now(),
          },
          'error',
        );
      }
    }

    // Auto-detect extension on page load
    document.addEventListener('DOMContentLoaded', () => {
      detectExtension();
    });

    // Manual detection button
    document.getElementById('detectExtension').addEventListener('click', detectExtension);

    // Retry detection button
    document.getElementById('retryDetection').addEventListener('click', detectExtension);
  </script>
</body>

</html>