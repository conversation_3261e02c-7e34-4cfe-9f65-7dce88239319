/* Reset margin and padding for html and body */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  display: flex;
  flex-direction: column;
  padding: 0;
  max-width: none;
  margin: 0;
  box-sizing: border-box;
  overflow-y: auto; /* Let body handle the scrolling */
}

h1 {
  margin: 1rem;
  text-align: center;
}

.test-section {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 0 1rem 1.5rem 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: visible; /* Change from auto to visible to remove scrolling */
}

.test-section h2 {
  margin-top: 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
  color: #333;
}

.setup-info {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  margin: 0 1rem 1.5rem 1rem;
  border-left: 4px solid #4b5563;
}

button {
  padding: 0.5rem 1.2rem;
  background-color: #4b5563;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 0.5rem 0.5rem 0.5rem 0;
  font-weight: 500;
  transition: background-color 0.2s;
}

button:hover {
  background-color: #6b7280;
}

button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.output {
  background-color: #f6f8fa;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 0.5rem;
  min-height: 3rem;
  color: #333;
  word-break: break-word;
  position: relative;
  overflow: visible; /* Change from auto to visible to remove scrolling */
  flex: 1;
}

.success {
  border-left: 4px solid #34d399;
}

.error {
  border-left: 4px solid #ef4444;
}

.loading {
  border-left: 4px solid #f59e0b;
}

pre {
  white-space: pre-wrap;
  margin: 0;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 0.9rem;
}

.status-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: #e5e7eb;
  color: #1f2937;
  border-radius: 2px;
  padding: 0.2rem 0.4rem;
  font-size: 0.75rem;
}

textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-family: inherit;
  margin-bottom: 0.75rem;
  resize: vertical;
  min-height: 100px;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 0.75rem;
  font-size: 0.875rem;
}

.status-dot {
  height: 8px;
  width: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-dot.loading {
  background-color: #f59e0b;
  animation: pulse 1.5s infinite;
}

.status-dot.success {
  background-color: #34d399;
}

.status-dot.error {
  background-color: #ef4444;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.info-block {
  background-color: #e0f2fe;
  border-left: 4px solid #0ea5e9;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 4px;
}

.stream-container {
  position: relative;
  margin-top: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

#streamContent {
  flex: 1;
  min-height: 200px;
  overflow-y: visible; /* Change from auto to visible to remove scrolling */
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 0.9rem;
  white-space: pre-wrap;
}

#streamStatus {
  margin-bottom: 0.5rem;
  font-style: italic;
  color: #4b5563;
}

.navigation, .test-links {
  margin: 0 1rem 2rem 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.test-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.test-links h3 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  font-size: 1.1rem;
}

.test-links h3:first-child {
  margin-top: 0;
}

.navigation a, .test-link {
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  color: #4b5563;
  text-decoration: none;
  border-radius: 4px;
  font-size: 0.9rem;
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.navigation a:hover, .test-link:hover {
  background-color: #e5e7eb;
}

.navigation a.active, .test-link.active {
  background-color: #4b5563;
  color: white;
} 