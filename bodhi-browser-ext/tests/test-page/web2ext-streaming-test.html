<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Webpage to Extension - Unified API Streaming Test</title>
  <link rel="stylesheet" href="css/styles.css" />
  <script src="js/common.js"></script>
</head>

<body>
  <h1>Webpage to Extension - Unified API Streaming Test</h1>
  <div class="setup-info">
    <h3>Test Environment</h3>
    <p>This page tests the generic web-to-extension streaming API format using the Bodhi extension.</p>
    <ul>
      <li><strong>Bodhi Extension ID:</strong> <span id="bodhiExtensionId">Detecting...</span></li>
    </ul>
  </div>
  <div class="test-section">
    <h2>Unified API Streaming Test</h2>
    <p>Test generic API streaming by sending a request via the Bodhi extension. Fill in the parameters below and submit.
    </p>
    <form id="streamForm" style="margin-bottom: 1em;">
      <label for="streamMethod">HTTP Method:</label>
      <input id="streamMethod" type="text" value="POST" style="width: 70px; margin-right: 1em;" required />
      <label for="streamEndpoint">Endpoint:</label>
      <input id="streamEndpoint" type="text" value="/v1/chat/completions" style="width: 180px; margin-right: 1em;"
        required />
      <br><br>
      <label for="streamBody">Request Body (JSON):</label><br>
      <textarea id="streamBody" rows="4"
        style="width: 350px; margin-bottom: 1em;">{"stream": true, "model":"test-model","messages":[{"role":"user","content":"Write a short poem about coding."}]}</textarea><br>
      <label for="streamHeaders">Headers (JSON):</label><br>
      <textarea id="streamHeaders" rows="2" style="width: 350px; margin-bottom: 1em;">{}</textarea><br>
      <button type="submit" id="submitStreamRequest">Start Streaming API Request</button>
    </form>
    <div id="streamStatus">Ready to stream</div>
    <div class="stream-container">
      <div id="streamContent"></div>
    </div>
    <div id="streamOutput" class="output">
      <pre>Test results will appear here...</pre>
    </div>
  </div>
  <script>
    function updateStatus(elementId, message, status = '') {
      const element = document.getElementById(elementId);
      if (element) {
        element.textContent = message;
        if (status) {
          element.className = status;
        }
      }
    }
    document.getElementById('streamForm').addEventListener('submit', async function (e) {
      e.preventDefault();
      const method = document.getElementById('streamMethod').value.trim();
      const endpoint = document.getElementById('streamEndpoint').value.trim();
      let body = document.getElementById('streamBody').value.trim();
      let headers = document.getElementById('streamHeaders').value.trim();
      body = body ? JSON.parse(body) : {};
      headers = headers ? JSON.parse(headers) : {};
      updateStatus('streamStatus', 'Starting stream...', 'loading');
      document.getElementById('streamContent').textContent = '';
      try {
        if (!window.bodhiext?.sendStreamRequest) {
          throw new Error('window.bodhiext.sendStreamRequest is not available');
        }
        const stream = await window.bodhiext.sendStreamRequest(method, endpoint, body, headers);
        if (!stream || typeof stream[Symbol.asyncIterator] !== 'function') {
          throw new Error('Streaming API did not return an async iterator');
        }
        updateStatus('streamStatus', 'Streaming...', 'success');
        const chunks = [];
        for await (const chunk of stream) {
          document.getElementById('streamContent').textContent += (chunk.body.choices[0].delta.content || '');
          chunks.push(chunk);
        }
        updateStatus('streamStatus', 'Stream complete!', 'success');
        displayResult('streamOutput', {
          test: 'web2ext unified api streaming',
          result: 'Stream complete',
          timestamp: Date.now(),
          chunks
        });
      } catch (error) {
        updateStatus('streamStatus', 'Stream failed to start!', 'error');
        displayResult('streamOutput', {
          test: 'web2ext unified api streaming',
          error: error.message,
          timestamp: Date.now()
        }, 'error');
      }
    });
  </script>
</body>

</html>