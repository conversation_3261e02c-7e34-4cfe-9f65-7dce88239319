<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Webpage to Extension - Extension ID Test</title>
  <link rel="stylesheet" href="css/styles.css">
  <script src="js/common.js"></script>
</head>

<body>
  <h1>Webpage to Extension - Extension ID Test</h1>

  <div class="setup-info">
    <h3>Test Environment</h3>
    <p>This page tests if a webpage can access the Bodhi extension ID through the window.bodhiext.extension_id property
    </p>
    <ul>
      <li><strong>Bodhi Extension ID:</strong> <span id="bodhiExtensionId">Detecting...</span></li>
    </ul>
  </div>

  <div class="test-section">
    <h2>Extension ID Test</h2>
    <p>Test the window.bodhiext.extension_id property.</p>
    <button id="checkExtensionId">Check Extension ID</button>
    <div id="testOutput" class="output">
      <pre>Extension ID will appear here...</pre>
    </div>
  </div>

  <script>
    function checkExtensionId(outputId = 'testOutput') {
      setLoading(outputId, 'Checking extension ID...');

      try {
        if (!window.bodhiext) {
          displayResult(outputId, {
            error: 'bodhiext is not available',
            timestamp: Date.now(),
          },
            'error',
          );
          throw new Error('bodhiext is not available');
        }

        window.bodhiext.getExtensionId()
          .then(extensionId => {
            displayResult(outputId, {
              extension_id: extensionId,
              timestamp: Date.now(),
            });
          })
          .catch(error => {
            displayResult(outputId, {
              error: error.message || 'Failed to get extension ID',
              timestamp: Date.now(),
            },
              'error',
            );
          });
      } catch (error) {
        displayResult(
          outputId,
          {
            error: error.message,
            timestamp: Date.now(),
          },
          'error'
        );
      }
    }

    document.getElementById('checkExtensionId').addEventListener('click', () => {
      checkExtensionId();
    });
  </script>
</body>

</html>