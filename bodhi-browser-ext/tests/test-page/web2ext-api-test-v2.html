<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bodhi Extension API Tester v2</title>
  <script src="js/common.js"></script>
  <style>
    body {
      font-family: sans-serif;
      max-width: 900px;
      margin: 2rem auto;
      line-height: 1.6;
      padding: 0 1rem;
    }

    .setup-info {
      background: #f0f8ff;
      padding: 1rem;
      border-left: 4px solid #007acc;
      margin-bottom: 2rem;
      border-radius: 4px;
    }

    .setup-info h3 {
      margin-top: 0;
      color: #005c99;
    }

    .setup-info ul {
      margin-bottom: 0;
    }

    .test-section {
      background: #fafafa;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 2rem;
    }

    .test-section h2 {
      margin-top: 0;
      color: #333;
    }

    label {
      display: block;
      margin-top: 1rem;
      font-weight: 600;
      color: #333;
    }

    input[type="text"],
    textarea,
    select {
      width: 100%;
      padding: 0.75rem;
      font-family: monospace;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }

    input[type="text"]:focus,
    textarea:focus,
    select:focus {
      outline: none;
      border-color: #007acc;
      box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
    }

    textarea {
      min-height: 100px;
      resize: vertical;
    }

    .form-row {
      display: flex;
      gap: 1rem;
      align-items: end;
    }

    .form-row>div {
      flex: 1;
    }

    .form-row label {
      margin-top: 0;
    }

    button {
      margin-top: 1rem;
      padding: 0.75rem 1.5rem;
      background: #007acc;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
    }

    button:hover {
      background: #005c99;
    }

    button:active {
      background: #004080;
    }

    button.secondary {
      background: #6c757d;
      margin-left: 0.5rem;
    }

    button.secondary:hover {
      background: #545b62;
    }

    .output {
      margin-top: 1.5rem;
    }

    .output h3 {
      margin-bottom: 0.5rem;
      color: #333;
    }

    pre {
      background: #f8f9fa;
      padding: 1rem;
      overflow-x: auto;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      font-size: 13px;
      line-height: 1.4;
      margin: 0;
    }

    .status-loading {
      color: #ffc107;
    }

    .status-success {
      color: #28a745;
    }

    .status-error {
      color: #dc3545;
    }

    .help-text {
      font-size: 12px;
      color: #666;
      margin-top: 0.25rem;
      font-weight: normal;
    }
  </style>
</head>

<body>
  <h1>Bodhi Extension API Tester v2</h1>

  <div class="setup-info">
    <h3>Test Environment</h3>
    <p>This page tests the Bodhi extension's web-to-extension API communication. Ensure the Bodhi extension is installed
      and enabled.</p>
    <ul>
      <li><strong>Bodhi Extension ID:</strong> <span id="bodhiExtensionId">Detecting...</span></li>
      <li><strong>Extension API Available:</strong> <span id="extensionApiStatus">Checking...</span></li>
    </ul>
  </div>

  <div class="test-section">
    <h2>API Request Configuration</h2>
    <p>Configure and send API requests through the Bodhi extension. <span id="persistenceInfo">Add ?mode=manual to URL
        to enable form value persistence.</span></p>

    <form id="apiForm">
      <div class="form-row">
        <div>
          <label>HTTP Method
            <select id="apiMethod">
              <option value="GET" selected>GET</option>
              <option value="POST">POST</option>
              <option value="PUT">PUT</option>
              <option value="PATCH">PATCH</option>
              <option value="DELETE">DELETE</option>
            </select>
          </label>
        </div>
        <div style="flex: 2;">
          <label>API Endpoint
            <input type="text" id="apiEndpoint" placeholder="/ping" />
          </label>
        </div>
      </div>

      <label>Request Body (JSON)
        <textarea id="apiBody" placeholder='{"message": "Hello, world!"}'></textarea>
        <div class="help-text">JSON object or string data for the request body.</div>
      </label>

      <label>Custom Headers (one per line)
        <textarea id="apiHeaders"
          placeholder="Authorization: Bearer token123&#10;X-Custom-Header: value&#10;Content-Type: application/json"></textarea>
        <div class="help-text">Format: "Header-Name: value" (one per line). Content-Type is set automatically for JSON
          requests.</div>
      </label>

      <button type="submit" id="submitApiRequest">Send API Request</button>
      <button type="button" id="clearOutput" class="secondary">Clear Output</button>
    </form>

    <div class="output">
      <h3>Status</h3>
      <pre id="responseStatus">(no response yet)</pre>
      <h3>Headers</h3>
      <pre id="responseHeaders">(no response yet)</pre>
      <h3>Body</h3>
      <pre id="responseBody">(no response yet)</pre>
    </div>
  </div>

  <script>
    const $ = id => document.getElementById(id);

    // Check if manual mode is enabled via URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const isManualMode = urlParams.get('mode') === 'manual';

    // LocalStorage helpers (only used in manual mode)
    const STORAGE_PREFIX = 'bodhi-ext-api-test-v2-';

    function saveValue(key, value) {
      if (isManualMode) {
        localStorage.setItem(STORAGE_PREFIX + key, value);
      }
    }

    function loadValue(key, defaultValue = '') {
      if (isManualMode) {
        return localStorage.getItem(STORAGE_PREFIX + key) || defaultValue;
      }
      return defaultValue;
    }

    function setupPersistence() {
      if (!isManualMode) return;

      // Load saved values
      $('apiMethod').value = loadValue('method', 'GET');
      $('apiEndpoint').value = loadValue('endpoint', '/ping');
      $('apiBody').value = loadValue('body');
      $('apiHeaders').value = loadValue('headers');

      // Save values on change
      $('apiMethod').addEventListener('change', e => saveValue('method', e.target.value));
      $('apiEndpoint').addEventListener('input', e => saveValue('endpoint', e.target.value));
      $('apiBody').addEventListener('input', e => saveValue('body', e.target.value));
      $('apiHeaders').addEventListener('input', e => saveValue('headers', e.target.value));
    }

    function parseHeaders(str) {
      const hdrs = {};
      str.split(/\r?\n/).forEach(line => {
        const idx = line.indexOf(':');
        if (idx > 0) {
          const name = line.slice(0, idx).trim();
          const value = line.slice(idx + 1).trim();
          if (name) hdrs[name] = value;
        }
      });
      return hdrs;
    }

    function displayExtensionResponse(response) {
      const statusElement = $('responseStatus');
      const headersElement = $('responseHeaders');
      const bodyElement = $('responseBody');

      if (!response) {
        statusElement.textContent = 'No response received';
        headersElement.textContent = '';
        bodyElement.textContent = '';
        return;
      }

      // Display status
      let statusOutput = '';
      if (response.status !== undefined) {
        statusOutput = response.status.toString();
        if (response.statusText) {
          statusOutput += ` ${response.statusText}`;
        }
      }
      statusElement.textContent = statusOutput || 'No status received';

      // Display headers
      let headersOutput = '';
      if (response.headers && Object.keys(response.headers).length > 0) {
        Object.entries(response.headers).forEach(([key, value]) => {
          headersOutput += `${key}: ${value}\n`;
        });
      }
      headersElement.textContent = headersOutput || 'No headers received';

      // Display body with JSON formatting if possible
      let bodyOutput = '';
      let bodyData = response.body !== undefined ? response.body : response.data;

      if (bodyData !== undefined) {
        if (typeof bodyData === 'string') {
          // Try to parse as JSON for pretty formatting
          try {
            const parsed = JSON.parse(bodyData);
            bodyOutput = JSON.stringify(parsed, null, 2);
          } catch (e) {
            // Not JSON, display as text
            bodyOutput = bodyData;
          }
        } else {
          // Already an object, stringify it
          bodyOutput = JSON.stringify(bodyData, null, 2);
        }
      } else if (response.error) {
        bodyOutput = response.error;
      }

      bodyElement.textContent = bodyOutput || 'No body received';
    }

    function updateStatus(message, type = 'info') {
      const statusElement = $('responseStatus');
      const headersElement = $('responseHeaders');
      const bodyElement = $('responseBody');
      const className = type === 'error' ? 'status-error' :
        type === 'success' ? 'status-success' :
          type === 'loading' ? 'status-loading' : '';

      statusElement.textContent = message;
      statusElement.className = className;
      headersElement.textContent = '';
      headersElement.className = '';
      bodyElement.textContent = '';
      bodyElement.className = '';
    }

    function updateExtensionInfo() {
      // Check extension availability
      if (window.bodhiext) {
        $('extensionApiStatus').textContent = 'Available ✓';
        $('extensionApiStatus').style.color = '#28a745';

        // Try to get extension ID if available
        if (window.bodhiext.extensionId) {
          $('bodhiExtensionId').textContent = window.bodhiext.extensionId;
        } else {
          $('bodhiExtensionId').textContent = 'Available (ID not exposed)';
        }
      } else {
        $('extensionApiStatus').textContent = 'Not Available ✗';
        $('extensionApiStatus').style.color = '#dc3545';
        $('bodhiExtensionId').textContent = 'Extension not loaded';
      }
    }

    // Listen for extension initialization event
    const bodhiExtListener = (event) => {
      console.log('Bodhi extension initialized:', event.detail);
      updateExtensionInfo();
      window.removeEventListener('bodhiext:initialized', bodhiExtListener);
    };
    window.addEventListener('bodhiext:initialized', bodhiExtListener);

    // Initialize extension detection and persistence
    document.addEventListener('DOMContentLoaded', () => {
      setupPersistence();

      // Update persistence info text
      if (isManualMode) {
        $('persistenceInfo').textContent = 'Manual mode enabled - form values are persisted.';
        $('persistenceInfo').style.color = '#28a745';
      }

      // Initial extension check
      updateExtensionInfo();
    });

    // Form submission handler
    $('apiForm').addEventListener('submit', async function (e) {
      e.preventDefault();

      updateStatus('Sending API request...', 'loading');

      try {
        const method = $('apiMethod').value.trim();
        const endpoint = $('apiEndpoint').value.trim();
        let body = $('apiBody').value.trim();
        const headersText = $('apiHeaders').value.trim();

        // Parse body - let user send whatever they want
        let bodyObj = {};
        if (body) {
          try {
            bodyObj = JSON.parse(body);
          } catch (e) {
            // If it's not valid JSON, send as string
            bodyObj = body;
          }
        }

        // Parse headers
        const headers = parseHeaders(headersText);

        // Check if extension API is available
        if (!window.bodhiext?.sendApiRequest) {
          throw new Error('Bodhi extension is not available. Please ensure the extension is installed and this page is loaded properly.');
        }

        // Send request
        const response = await window.bodhiext.sendApiRequest(method, endpoint, bodyObj, headers);

        // Display response
        displayExtensionResponse(response);
        $('responseStatus').className = 'status-success';
        $('responseHeaders').className = 'status-success';
        $('responseBody').className = 'status-success';

      } catch (error) {
        const errorMessage = `Error: ${error.message}\n\nTimestamp: ${new Date().toISOString()}`;
        $('responseStatus').textContent = errorMessage;
        $('responseStatus').className = 'status-error';
        $('responseHeaders').textContent = '';
        $('responseHeaders').className = '';
        $('responseBody').textContent = '';
        $('responseBody').className = '';
      }
    });

    // Clear output handler
    $('clearOutput').addEventListener('click', () => {
      $('responseStatus').textContent = '(no response yet)';
      $('responseStatus').className = '';
      $('responseHeaders').textContent = '(no response yet)';
      $('responseHeaders').className = '';
      $('responseBody').textContent = '(no response yet)';
      $('responseBody').className = '';
    });
  </script>
</body>

</html>