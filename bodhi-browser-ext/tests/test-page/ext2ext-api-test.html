<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Extension to Extension - Unified API Ping Test</title>
  <link rel="stylesheet" href="css/styles.css" />
  <script src="js/common.js"></script>
</head>
<body>
  <h1>Extension to Extension - Unified API Ping Test</h1>
  <div class="setup-info">
    <h3>Test Environment</h3>
    <p>This page tests the new unified extension-to-extension API format for ping requests using the test extension.</p>
    <ul>
      <li><strong>Bodhi Extension ID:</strong> <span id="bodhiExtensionId">Detecting...</span></li>
    </ul>
  </div>
  <div class="test-section">
    <h2>Unified API Request Test</h2>
    <p>Test unified API communication by sending a request via the test extension's new method. Fill in the parameters below and submit.</p>
    <form id="apiForm" style="margin-bottom: 1em;">
      <label for="apiMethod">HTTP Method:</label>
      <input id="apiMethod" type="text" value="GET" style="width: 70px; margin-right: 1em;" required />
      <label for="apiEndpoint">Endpoint:</label>
      <input id="apiEndpoint" type="text" value="/ping" style="width: 140px; margin-right: 1em;" required />
      <br><br>
      <label for="apiBody">Request Body (JSON):</label><br>
      <textarea id="apiBody" rows="3" style="width: 350px; margin-bottom: 1em;">{}</textarea><br>
      <label for="apiHeaders">Headers (JSON):</label><br>
      <textarea id="apiHeaders" rows="2" style="width: 350px; margin-bottom: 1em;">{}</textarea><br>
      <button type="submit" id="submitApiRequest">Send API Request</button>
    </form>
    <div id="apiPingOutput" class="output">
      <pre>Test results will appear here...</pre>
    </div>
  </div>

  <script>
    document.getElementById('apiForm').addEventListener('submit', async function (e) {
      e.preventDefault();
      displayResult('apiPingOutput', {
        test: 'unified api request',
        message: 'Sending unified API request...',
        timestamp: Date.now()
      }, 'loading');
      try {
        const method = document.getElementById('apiMethod').value.trim();
        const endpoint = document.getElementById('apiEndpoint').value.trim();
        let body = document.getElementById('apiBody').value.trim();
        let headers = document.getElementById('apiHeaders').value.trim();
        body = body ? JSON.parse(body) : {};
        headers = headers ? JSON.parse(headers) : {};
        const data = await window.bodhiTestExtension.sendApiRequest(method, endpoint, body, headers);
        displayResult('apiPingOutput', {
          test: 'unified api request',
          data,
          timestamp: Date.now()
        });
      } catch (error) {
        displayResult('apiPingOutput', {
          test: 'unified api request',
          error: error.message,
          timestamp: Date.now()
        }, 'error');
      }
    });
  </script>
</body>
</html>
