/**
 * Common utilities for Bodhi Extension test pages
 */

// Helper function to display results
function displayResult(outputId, result, status = 'success') {
  const output = document.getElementById(outputId);

  // Ensure result is properly stringified as valid JSON
  const testOutput =
    typeof result === 'string'
      ? JSON.stringify({ message: result })
      : JSON.stringify(result, null, 2);

  output.textContent = `${testOutput}`;
  output.className = `output ${status}`;
  if (status === 'error') {
    console.error(testOutput);
  }
}

// Helper function to show loading state
function setLoading(outputId, message = 'Loading...') {
  const output = document.getElementById(outputId);
  // Ensure even loading messages are valid JSON
  output.innerHTML = `<pre>${JSON.stringify({ status: 'loading', message }, null, 2)}</pre>`;
  output.className = 'output loading';
}

// Update extension IDs in the UI
function updateExtensionIds() {
  let needMoreChecks = false;
  // Track if extension IDs have been updated
  const bodhiIdElement = document.getElementById('bodhiExtensionId');
  const testIdElement = document.getElementById('testExtensionId');

  // Update Bodhi Extension ID if available
  if (window.bodhiext && bodhiIdElement && bodhiIdElement.textContent === 'Detecting...') {
    // Use the async method to get the ID
    window.bodhiext
      .getExtensionId()
      .then(extensionId => {
        bodhiIdElement.textContent = extensionId;
      })
      .catch(error => {
        console.error('Error getting extension ID:', error);
        // Still check again if there was an error
        needMoreChecks = true;
      });
  } else if (bodhiIdElement && bodhiIdElement.textContent === 'Detecting...') {
    // Need to check again for Bodhi extension only if we haven't found it yet
    needMoreChecks = true;
  }

  // Update Test Extension ID if available and not already set
  if (window.bodhiTestExtension && testIdElement && testIdElement.textContent === 'Detecting...') {
    // For test extension, we might need to keep using the old way if it hasn't been updated
    if (window.bodhiTestExtension.getExtensionId) {
      window.bodhiTestExtension
        .getExtensionId()
        .then(extensionId => {
          testIdElement.textContent = extensionId;
        })
        .catch(() => {
          needMoreChecks = true;
        });
    } else if (window.bodhiTestExtension.extension_id) {
      testIdElement.textContent = window.bodhiTestExtension.extension_id;
    } else {
      needMoreChecks = true;
    }
  } else if (testIdElement && testIdElement.textContent === 'Detecting...') {
    // Need to check again for Test extension only if we haven't found it yet
    needMoreChecks = true;
  }

  // Check again only if we haven't found one of the extensions
  if (needMoreChecks) {
    setTimeout(updateExtensionIds, 50);
  }
}

// Initialize the page with common elements
document.addEventListener('DOMContentLoaded', () => {
  // Use one-time listeners for extension initialization events
  // to avoid duplicate event handling
  const handleBodhiExtInitialized = event => {
    console.log('Received bodhiext:initialized event with ID:', event.detail.extensionId);
    const bodhiIdElement = document.getElementById('bodhiExtensionId');
    if (bodhiIdElement) {
      bodhiIdElement.textContent = event.detail.extensionId;
    }
    // Remove the listener after the first event
    window.removeEventListener('bodhiext:initialized', handleBodhiExtInitialized);
  };

  window.addEventListener('bodhiext:initialized', handleBodhiExtInitialized);

  // Do the same for the test extension event
  const handleBodhiTestInitialized = event => {
    console.log('Received bodhitest:initialized event with ID:', event.detail.extensionId);
    const testIdElement = document.getElementById('testExtensionId');
    if (testIdElement) {
      testIdElement.textContent = 'Bodhi Test Extension ID initialized';
    }
    // Remove the listener after the first event
    window.removeEventListener('bodhitest:initialized', handleBodhiTestInitialized);
  };

  window.addEventListener('bodhitest:initialized', handleBodhiTestInitialized);
});

// Expose functions to the global scope to prevent ESLint no-unused-vars error
window.displayResult = displayResult;
window.setLoading = setLoading;
window.updateExtensionIds = updateExtensionIds;
