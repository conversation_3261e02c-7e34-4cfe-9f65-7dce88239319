import { expect as playwrightExpect } from '@playwright/test';
import { Page } from 'playwright';
import { afterAll, afterEach, beforeAll, beforeEach, describe, it } from 'vitest';
import { BodhiServerOptions, createBodhiServer } from './bodhi-server-config';
import {
  BrowserManager,
  createBrowserManager,
  OAuthAppServerInfo,
  setBackendUrl,
  startOAuthAppServer,
  stopOAuthAppServer,
} from './test-helpers';

describe('Server State Detection with Running Server', () => {
  let browserManager: BrowserManager;
  let oauthAppServer: OAuthAppServerInfo | null = null;
  let page: Page;
  let testAppUrl: string | null;
  const oauthAppPort = 12347;

  beforeAll(async () => {
    // Start the OAuth app server
    oauthAppServer = await startOAuthAppServer(oauthAppPort);
    testAppUrl = `http://localhost:${oauthAppPort}`;
    // Start browser with extension (using dummy mock port initially)
    browserManager = await createBrowserManager({
      loadTestExtension: false,
      staticPort: oauthAppPort,
      mockPort: 11135, // Dummy port, will be reconfigured per test
    });
  });

  beforeEach(async () => {
    page = await browserManager.createPage();
  });

  afterEach(async () => {
    if (page) {
      await page.close();
    }
  });

  afterAll(async () => {
    if (browserManager) {
      await browserManager.cleanup();
    }
    if (oauthAppServer) {
      stopOAuthAppServer(oauthAppServer);
    }
  });

  describe('appStatus=ready', () => {
    let serverResult: any;

    beforeEach(async () => {
      // Start Bodhi App server with ready status
      const serverConfig: BodhiServerOptions = {
        authUrl: process.env.INTEG_TEST_AUTH_URL!,
        authRealm: process.env.INTEG_TEST_AUTH_REALM!,
        clientId: process.env.INTEG_TEST_CLIENT_ID!,
        clientSecret: process.env.INTEG_TEST_CLIENT_SECRET!,
        appStatus: 'ready',
      };

      serverResult = await createBodhiServer(serverConfig);
      await serverResult.server.start();

      // Configure extension settings to use this server
      await setBackendUrl(
        browserManager.setup.context,
        browserManager.setup.extensionId!,
        serverResult.config.port
      );
    });

    afterEach(async () => {
      if (serverResult?.server) {
        await serverResult.server.stop();
      }
    });

    it('should display ready server state and support refresh', async () => {
      await page.goto(testAppUrl!);
      await page.waitForLoadState('networkidle');
      await page.waitForSelector('.extension-status.detected');

      // Verify server state displays ready status with version
      await playwrightExpect(page.locator('.server-state')).toBeVisible();
      await playwrightExpect(page.locator('.status-message')).toContainText(
        'Status: ready (v1.0.0-test)'
      );
      await playwrightExpect(page.locator('.error-details')).not.toBeVisible();

      // Test refresh functionality maintains ready state
      await page.locator('.server-state button').click();
      await page.waitForTimeout(500);
      await playwrightExpect(page.locator('.status-message')).toContainText('Status: ready');
    });
  });

  describe('appStatus=setup', () => {
    let serverResult: any;

    beforeEach(async () => {
      // Start Bodhi App server with setup status
      const serverConfig: BodhiServerOptions = {
        authUrl: process.env.INTEG_TEST_AUTH_URL!,
        authRealm: process.env.INTEG_TEST_AUTH_REALM!,
        clientId: process.env.INTEG_TEST_CLIENT_ID!,
        clientSecret: process.env.INTEG_TEST_CLIENT_SECRET!,
        appStatus: 'setup',
      };

      serverResult = await createBodhiServer(serverConfig);
      await serverResult.server.start();

      // Configure extension settings to use this server
      await setBackendUrl(
        browserManager.setup.context,
        browserManager.setup.extensionId!,
        serverResult.config.port
      );
    });

    afterEach(async () => {
      if (serverResult?.server) {
        await serverResult.server.stop();
      }
    });

    it('should display setup server state', async () => {
      await page.goto(testAppUrl!);
      await page.waitForLoadState('networkidle');
      await page.waitForSelector('.extension-status.detected');

      await playwrightExpect(page.locator('.status-message')).toContainText(
        'Status: setup (v1.0.0-test)'
      );
    });
  });

  describe('appStatus=resource-admin', () => {
    let serverResult: any;

    beforeEach(async () => {
      // Start Bodhi App server with resource-admin status
      const serverConfig: BodhiServerOptions = {
        authUrl: process.env.INTEG_TEST_AUTH_URL!,
        authRealm: process.env.INTEG_TEST_AUTH_REALM!,
        clientId: process.env.INTEG_TEST_CLIENT_ID!,
        clientSecret: process.env.INTEG_TEST_CLIENT_SECRET!,
        appStatus: 'resource-admin',
      };

      serverResult = await createBodhiServer(serverConfig);
      await serverResult.server.start();

      // Configure extension settings to use this server
      await setBackendUrl(
        browserManager.setup.context,
        browserManager.setup.extensionId!,
        serverResult.config.port
      );
    });

    afterEach(async () => {
      if (serverResult?.server) {
        await serverResult.server.stop();
      }
    });

    it('should display resource-admin server state', async () => {
      await page.goto(testAppUrl!);
      await page.waitForLoadState('networkidle');
      await page.waitForSelector('.extension-status.detected');

      await playwrightExpect(page.locator('.status-message')).toContainText(
        'Status: resource-admin (v1.0.0-test)'
      );
    });
  });
});
