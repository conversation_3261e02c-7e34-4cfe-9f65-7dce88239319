import { describe, test, expect, beforeAll, afterAll } from 'vitest';
import { chromium, <PERSON><PERSON><PERSON>, BrowserContext, Page } from 'playwright';
import { exec as execCallback } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import { promisify } from 'util';
import { startOAuthAppServer, stopOAuthAppServer, OAuthAppServerInfo } from './test-helpers';

const exec = promisify(execCallback);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const oauthAppPath = path.resolve(__dirname, 'test-app-oauth');

// Add Chrome extension API types for testing
declare global {
  interface Window {
    chrome: {
      storage: {
        local: {
          get: (keys: string[], callback: (result: Record<string, any>) => void) => void;
        };
      };
    };
  }
}

describe('Settings', () => {
  let browser: Browser;
  let context: BrowserContext;
  let page: Page;
  let extensionId: string;
  let oauthAppServer: OAuthAppServerInfo | null = null;
  const oauthAppPort = 12346; // Use a different port to avoid conflicts

  beforeAll(async () => {
    // Build and start the OAuth app server
    const appBuild = await exec('npm run build', { cwd: oauthAppPath });
    if (appBuild.stdout) process.stdout.write(appBuild.stdout);
    if (appBuild.stderr) process.stderr.write(appBuild.stderr);

    // Start the OAuth app server using the utility
    oauthAppServer = await startOAuthAppServer(oauthAppPort);

    // Launch browser with extension loaded using launchPersistentContext
    const extensionPath = path.resolve(__dirname, '../dist');

    context = await chromium.launchPersistentContext('', {
      headless: false,
      args: [
        `--disable-extensions-except=${extensionPath}`,
        `--load-extension=${extensionPath}`,
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--disable-gpu',
      ],
    });

    browser = context.browser()!;

    // Create a page and get extension ID
    page = await context.newPage();

    // Navigate to the OAuth app to inject the extension
    await page.goto(`http://localhost:${oauthAppPort}`);

    // Wait for extension to be injected and get the extension ID
    await page.waitForFunction(() => typeof (window as any).bodhiext !== 'undefined', {
      timeout: 10000,
    });
    extensionId = await page.evaluate(async () => await (window as any).bodhiext.getExtensionId());

    expect(extensionId).toBeDefined();
  }, 20000);

  afterAll(async () => {
    if (browser) await browser.close();
    stopOAuthAppServer(oauthAppServer);
  });

  test('Should save and restore backend URL settings', async () => {
    // Use the same context that has the extension loaded
    const popupPage = await context.newPage();
    const popupUrl = `chrome-extension://${extensionId}/index.html`;

    try {
      await popupPage.goto(popupUrl);

      // Wait for settings form to appear directly
      await popupPage.waitForSelector('form');

      // First read the current value to restore later
      const originalUrl = await popupPage.locator('#backendUrl').inputValue();
      console.log(`Original backend URL: ${originalUrl}`);

      // Test URL to set
      const testUrl = 'http://localhost:8080';

      // Clear input and set new URL
      await popupPage.locator('#backendUrl').fill('');
      await popupPage.locator('#backendUrl').type(testUrl, { delay: 1 });

      // Submit form
      await popupPage.click('button[type="submit"]');

      // Wait for success message
      await popupPage.waitForSelector('.message-container.success', { timeout: 5000 });

      // Verify the URL was saved in chrome.storage
      const savedUrl = await popupPage.evaluate(() => {
        return new Promise<string>(resolve => {
          chrome.storage.local.get(['backendUrl'], result => {
            resolve(result.backendUrl);
          });
        });
      });

      expect(savedUrl).toBe(testUrl);

      // Now restore the original URL (or set a default if none existed)
      const restoreUrl = originalUrl || 'http://localhost:3000';
      await popupPage.locator('#backendUrl').fill('');
      await popupPage.locator('#backendUrl').type(restoreUrl, { delay: 1 });
      await popupPage.click('button[type="submit"]');

      // Wait for success message again
      await popupPage.waitForSelector('.message-container.success', { timeout: 5000 });

      // Verify the URL was restored
      const restoredUrl = await popupPage.evaluate(() => {
        return new Promise<string>(resolve => {
          chrome.storage.local.get(['backendUrl'], result => {
            resolve(result.backendUrl);
          });
        });
      });

      expect(restoredUrl).toBe(restoreUrl);
    } finally {
      await popupPage.close();
    }
  }, 20000);
});
