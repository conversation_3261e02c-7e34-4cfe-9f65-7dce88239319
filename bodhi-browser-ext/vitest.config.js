import { defineConfig } from 'vitest/config';
import { config } from 'dotenv';

// Load .env.test file for test environment variables
config({ path: '.env.test' });

export default defineConfig({
  test: {
    include: ['**/*.test.{js,ts}'],
    exclude: ['node_modules/**/*', 'dist/**/*'],
    environment: 'node',

    // Standardized timeout configuration
    testTimeout: 30000, // 30 seconds for individual tests
    hookTimeout: 15000, // 15 seconds for setup/teardown hooks

    sequence: {
      hooks: 'list', // Run hooks in order to maintain compatibility with Jest
      setupFiles: 'list', // Execute setup files in sequence
    },

    globals: true,

    // Run only one file at a time for browser tests
    maxThreads: 1,
    minThreads: 1,
    fileParallelism: false, // Ensure files run sequentially
  },
});
