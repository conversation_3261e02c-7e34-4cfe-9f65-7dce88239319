import type { NextConfig } from 'next';
import WebpackObfuscator from 'webpack-obfuscator';

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: true,
  output: 'export',
  distDir: './dist',
  webpack(config, { isServer, dev }) {
    // Only obfuscate client-side production builds
    if (!dev && !isServer) {
      config.plugins.push(
        new WebpackObfuscator(
          {
            compact: true,
            controlFlowFlattening: true,
            controlFlowFlatteningThreshold: 0.75,
            deadCodeInjection: true,
            deadCodeInjectionThreshold: 0.4,
            stringArray: true,
            stringArrayEncoding: ['base64'],
            stringArrayThreshold: 0.75,
            selfDefending: false,
            debugProtection: false,
            domainLock: [],
          },
          [
            'static/chunks/pages/_app*.js',
            'static/chunks/pages/_document*.js',
            'static/chunks/pages/_error*.js',
            'static/runtime/*.js',
          ]
        )
      );
    }
    return config;
  },
};

export default nextConfig;
