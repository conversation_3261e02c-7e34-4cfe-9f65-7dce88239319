/**
 * <PERSON><PERSON> Browser Plugin inject script
 * Creates the interface that is exposed to web pages
 */

'use strict';

// Use IIFE to avoid polluting global namespace
/* global EventListener */
/// <reference lib="dom" />
import {
  DEFAULT_API_BASE_URL,
  DEFAULT_REQUEST_TIMEOUT,
  MESSAGE_TYPES,
  ORIGIN_WILDCARD,
  EVENT_INITIALIZED,
  HTTP_METHOD_GET,
  HTTP_METHOD_POST,
  ENDPOINT_PING,
  ENDPOINT_CHAT_COMPLETIONS,
} from './shared/constants.js';

import {
  ApiResponse,
  ApiRequestMessage,
  GetExtensionIdMessage,
  ServerStateInfo,
} from './shared/types.js';

import {
  createErrorWithDetails,
  getErrorMessageFromResponse,
  isSuccessResponse,
  createLogger,
} from './shared/utils.js';

(function () {
  //-----------------------------------------------------------------------------------
  // CONSTANTS AND CONFIGURATION
  //-----------------------------------------------------------------------------------

  // Stream timeout is longer than regular request timeout
  const DEFAULT_STREAM_TIMEOUT = 60000; // 60 seconds

  // Error messages
  const ERROR_REQUEST_TIMEOUT = 'Request timed out';
  const ERROR_STREAM_ERROR = 'Stream error';
  const ERROR_UNKNOWN_ERROR = 'Unknown error';

  // Type definitions
  interface StreamController {
    enqueue: (chunk: any) => void;
    error: (err: Error) => void;
    complete: () => void;
  }

  interface RequestCallbacks {
    resolve: (value: any) => void;
    reject: (reason?: any) => void;
  }

  interface BodhiExtInterface {
    _private: {
      requests: Map<string, RequestCallbacks>;
      streams: Map<string, StreamController>;
      baseUrl: string;
      generateId: () => string;
      handleApiResponse: (event: MessageEvent) => void;
      sendApiRequest: (
        method: string,
        endpoint: string,
        body?: any,
        headers?: Record<string, string>
      ) => Promise<{ body: any; headers: Record<string, string>; status: number }>;
      sendStreamRequest: (
        method: string,
        endpoint: string,
        body?: any,
        headers?: Record<string, string>
      ) => ReadableStream;
      requestExtensionId: () => void;
      _getExtensionId: () => Promise<string>;
    };
    extension_id: string | null;
    ping: () => Promise<any>;
    serverState: () => Promise<ServerStateInfo>;
    chat: {
      completions: {
        create: (params: any) => Promise<any> | AsyncIterable<any>;
      };
    };
  }

  // Logger for consistent message formatting
  const logger = createLogger('inject.js');

  logger.info('Interface initializing');

  //-----------------------------------------------------------------------------------
  // INTERFACE IMPLEMENTATION
  //-----------------------------------------------------------------------------------

  /**
   * Creates the bodhiext interface and attaches it to the window object
   */
  const createInterface = (): void => {
    // Check if the interface already exists
    if (typeof (window as any).bodhiext !== 'undefined') {
      logger.info('Interface already exists, skipping initialization');
      return;
    }

    // Create the interface object with better encapsulation
    const bodhiextInterface: BodhiExtInterface = {
      // Private properties - not directly accessible
      _private: {
        // Keep track of pending requests
        requests: new Map<string, RequestCallbacks>(),

        // Keep track of active streams
        streams: new Map<string, StreamController>(),

        // Base URL for creating path references
        baseUrl: DEFAULT_API_BASE_URL,

        // Generate a random ID for each request - more secure with crypto
        generateId: (): string => {
          try {
            // Use crypto for secure random values if available
            if (window.crypto && window.crypto.randomUUID) {
              return window.crypto.randomUUID();
            }

            // Fallback to less secure but still functional method
            return (
              Math.random().toString(36).substring(2, 15) +
              Math.random().toString(36).substring(2, 15)
            );
          } catch (error) {
            logger.error('Error generating request ID', { error });
            // Last resort fallback
            return `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
          }
        },

        /**
         * Handles API response messages
         */
        handleApiResponse: (event: MessageEvent): void => {
          if (!event.data || !event.data.type) {
            logger.info('Ignoring API response with no type', { data: event.data });
            return;
          }

          try {
            const { type, requestId, response } = event.data;

            // Use switch statement based on message type
            switch (type) {
              case MESSAGE_TYPES.API_RESPONSE:
                handleRegularResponse(requestId, response);
                break;

              case MESSAGE_TYPES.STREAM_CHUNK:
                handleStreamChunk(requestId, response);
                break;

              case MESSAGE_TYPES.STREAM_ERROR:
                handleStreamError(requestId, response);
                break;

              case MESSAGE_TYPES.ERROR:
                handleGenericError(requestId, response);
                break;

              case MESSAGE_TYPES.SET_EXTENSION_ID:
                handleSetExtensionId(event.data.extension_id);
                break;
            }
          } catch (error) {
            logger.error('Error handling API response', { error });
          }
        },

        // Generic request method
        sendApiRequest: function (
          method: string,
          endpoint: string,
          body: any = null,
          headers: Record<string, string> = {}
        ): Promise<{ body: any; headers: Record<string, string>; status: number }> {
          return new Promise((resolve, reject) => {
            try {
              const requestId = this.generateId();

              // Store both resolve and reject functions for when we get a response
              this.requests.set(requestId, { resolve, reject });

              // Send the API request message to the content script
              window.postMessage(
                {
                  type: MESSAGE_TYPES.API_REQUEST,
                  requestId,
                  request: {
                    method,
                    endpoint,
                    body,
                    headers,
                  },
                } as ApiRequestMessage,
                window.origin || ORIGIN_WILDCARD
              );

              // Set a timeout to clean up if no response is received
              setTimeout(() => {
                if (this.requests.has(requestId)) {
                  this.requests.delete(requestId);
                  reject(new Error(`${ERROR_REQUEST_TIMEOUT}: ${endpoint}`));
                }
              }, DEFAULT_REQUEST_TIMEOUT);
            } catch (error) {
              reject(error);
            }
          });
        },

        // Streaming request method
        sendStreamRequest: function (
          method: string,
          endpoint: string,
          body: any = null,
          headers: Record<string, string> = {}
        ): ReadableStream {
          // Create an ID for this streaming request
          const requestId = this.generateId();

          // Create an async iterator using ReadableStream
          const stream = new ReadableStream({
            start: (controller: ReadableStreamDefaultController) => {
              // Store the controller for this stream
              this.streams.set(requestId, {
                enqueue: (chunk: any) => controller.enqueue(chunk),
                error: (err: Error) => controller.error(err),
                complete: () => controller.close(),
              });

              // Send the stream request
              window.postMessage(
                {
                  type: MESSAGE_TYPES.STREAM_REQUEST,
                  requestId,
                  request: {
                    method,
                    endpoint,
                    body,
                    headers,
                  },
                } as ApiRequestMessage,
                window.origin || ORIGIN_WILDCARD
              );

              // Set a timeout to clean up if no response is received
              setTimeout(() => {
                if (this.streams.has(requestId)) {
                  const streamController = this.streams.get(requestId)!;
                  streamController.error(new Error(`${ERROR_REQUEST_TIMEOUT}: ${endpoint}`));
                  this.streams.delete(requestId);
                }
              }, DEFAULT_STREAM_TIMEOUT);
            },
            cancel: (reason: any) => {
              // Clean up if the consumer cancels the stream
              if (this.streams.has(requestId)) {
                this.streams.delete(requestId);
                logger.info('Stream was cancelled', { requestId, reason });
              }
            },
          });

          // Return a reader that will yield chunks as they arrive
          return stream;
        },

        // Request the extension ID from content script
        requestExtensionId: function (): void {
          window.postMessage(
            {
              type: MESSAGE_TYPES.GET_EXTENSION_ID,
            } as GetExtensionIdMessage,
            window.origin || ORIGIN_WILDCARD
          );
        },

        // Internal method to get extension ID
        _getExtensionId: function (): Promise<string> {
          // If we already have the extension_id, return it
          if (bodhiextInterface.extension_id) {
            return Promise.resolve(bodhiextInterface.extension_id);
          }

          // Otherwise, request it and wait for it to be set
          return new Promise(resolve => {
            // Listen for the event that's dispatched when extension_id is set
            const onInitialized = (event: CustomEvent) => {
              window.removeEventListener(EVENT_INITIALIZED, onInitialized as EventListener);
              resolve(event.detail.extensionId);
            };

            window.addEventListener(EVENT_INITIALIZED, onInitialized as EventListener);

            // Request the extension ID
            this.requestExtensionId();
          });
        },
      },

      // Extension ID property will be set when content script provides it
      extension_id: null,

      // Public API methods
      // Ping implemented using the generic request mechanism
      ping: function (): Promise<any> {
        return this._private
          .sendApiRequest(HTTP_METHOD_GET, ENDPOINT_PING)
          .then(response => response.body);
      },

      // Server state method to get server status information
      serverState: function (): Promise<ServerStateInfo> {
        return this._private
          .sendApiRequest(HTTP_METHOD_GET, '/bodhi/v1/info')
          .then(response => {
            // Handle network errors (status 0 means network failure)
            if (response.status === 0) {
              return {
                status: 'unreachable' as const,
                url: this._private.baseUrl,
                error: {
                  message: response.body?.error?.message || 'Failed to connect to server',
                  type: 'network_error',
                },
              };
            }

            // Handle successful response (status 200)
            if (response.status === 200 && response.body) {
              return {
                status: response.body.status,
                version: response.body.version,
                url: this._private.baseUrl,
              };
            }

            // Handle server error response (status 500 or other error codes)
            if (response.status >= 400) {
              return {
                status: 'error' as const,
                url: this._private.baseUrl,
                error: response.body?.error || {
                  message: `Server returned status ${response.status}`,
                  type: 'server_error',
                },
              };
            }

            // Handle unexpected response format
            return {
              status: 'error' as const,
              url: this._private.baseUrl,
              error: {
                message: 'Unexpected response format from server',
                type: 'response_error',
              },
            };
          })
          .catch(error => {
            // Handle any other errors that might occur
            return {
              status: 'unreachable' as const,
              url: this._private.baseUrl,
              error: {
                message: error.message || 'Failed to connect to server',
                type: 'network_error',
              },
            };
          });
      },

      // Chat API
      chat: {
        completions: {
          // Create a chat completion
          create: function (params: any): Promise<any> | AsyncIterable<any> {
            // Check if this is a streaming request
            if (params && params.stream === true) {
              // Get a reference to the private streaming request method
              const streamRequestMethod = bodhiextInterface._private.sendStreamRequest.bind(
                bodhiextInterface._private
              );

              // Make the streaming request
              const readableStream = streamRequestMethod(
                HTTP_METHOD_POST,
                ENDPOINT_CHAT_COMPLETIONS,
                params
              );

              // Create an async iterator wrapper compatible with OpenAI SDK pattern
              return {
                [Symbol.asyncIterator]: async function* () {
                  const reader = readableStream.getReader();
                  try {
                    while (true) {
                      const { done, value } = await reader.read();
                      if (done) {
                        break;
                      }
                      yield value.body;
                    }
                  } finally {
                    reader.releaseLock();
                  }
                },
              };
            }

            // Non-streaming request - use the regular API
            return bodhiextInterface._private
              .sendApiRequest(HTTP_METHOD_POST, ENDPOINT_CHAT_COMPLETIONS, params)
              .then(response => response.body);
          },
        },
      },
    };

    //-----------------------------------------------------------------------------------
    // RESPONSE HANDLERS
    //-----------------------------------------------------------------------------------

    /**
     * Handle regular API responses
     */
    const handleRegularResponse = (requestId: string, response: ApiResponse): void => {
      if (!requestId || !bodhiextInterface._private.requests.has(requestId)) {
        logger.info('No matching request found for API response', { requestId });
        return;
      }
      // Get the resolve function
      const { resolve } = bodhiextInterface._private.requests.get(requestId)!;
      // Always resolve with the response object, regardless of HTTP status code
      // This allows the client to handle HTTP errors appropriately by checking the status
      resolve(response);

      bodhiextInterface._private.requests.delete(requestId);
    };

    /**
     * Handle streaming chunks
     */
    const handleStreamChunk = (requestId: string, response: ApiResponse): void => {
      if (!requestId || !bodhiextInterface._private.streams.has(requestId)) {
        return;
      }

      const streamController = bodhiextInterface._private.streams.get(requestId)!;

      if (!isSuccessResponse(response.status)) {
        const errorMessage = getErrorMessageFromResponse(response, ERROR_STREAM_ERROR);
        streamController.error(
          createErrorWithDetails(errorMessage, response.status, response.body)
        );
        bodhiextInterface._private.streams.delete(requestId);
        return;
      }

      if (response.body && response.body.done) {
        streamController.complete();
        bodhiextInterface._private.streams.delete(requestId);
        return;
      }

      streamController.enqueue(response);
    };

    /**
     * Handle stream errors
     */
    const handleStreamError = (requestId: string, response: ApiResponse): void => {
      if (!requestId || !bodhiextInterface._private.streams.has(requestId)) {
        logger.info('No matching stream found for STREAM_ERROR', { requestId });
        return;
      }

      const streamController = bodhiextInterface._private.streams.get(requestId)!;
      const errorMessage = getErrorMessageFromResponse(response, ERROR_STREAM_ERROR);

      streamController.error(createErrorWithDetails(errorMessage, response.status, response.body));

      bodhiextInterface._private.streams.delete(requestId);
    };

    /**
     * Handle generic errors
     */
    const handleGenericError = (requestId: string, response: ApiResponse): void => {
      if (!requestId) return;

      // Check if this is for a stream request
      if (bodhiextInterface._private.streams.has(requestId)) {
        const streamController = bodhiextInterface._private.streams.get(requestId)!;
        const errorMessage = getErrorMessageFromResponse(response, ERROR_STREAM_ERROR);

        streamController.error(
          createErrorWithDetails(errorMessage, response.status, response.body)
        );

        // Clean up the stream
        bodhiextInterface._private.streams.delete(requestId);
      }
      // Otherwise it might be for a regular request that wasn't properly handled
      else if (bodhiextInterface._private.requests.has(requestId)) {
        const { reject } = bodhiextInterface._private.requests.get(requestId)!;
        const errorMessage = getErrorMessageFromResponse(response, ERROR_UNKNOWN_ERROR);

        reject(createErrorWithDetails(errorMessage, response.status, response.body));

        // Clean up
        bodhiextInterface._private.requests.delete(requestId);
      }
    };

    /**
     * Handle setting the extension ID
     */
    const handleSetExtensionId = (extensionId: string): void => {
      // Only set the ID and dispatch the event if not already set
      if (!bodhiextInterface.extension_id) {
        bodhiextInterface.extension_id = extensionId;
        logger.info('Extension ID set', { extension_id: bodhiextInterface.extension_id });

        // Dispatch a custom event to notify that bodhiext is initialized
        window.dispatchEvent(
          new CustomEvent(EVENT_INITIALIZED, {
            detail: {
              extensionId: bodhiextInterface.extension_id,
            },
          })
        );
        logger.info('Dispatched event', { event: EVENT_INITIALIZED });
      }
    };

    // Create the public interface by exposing only the public methods
    const publicInterface: PublicBodhiExtInterface = {
      sendApiRequest: bodhiextInterface._private.sendApiRequest.bind(bodhiextInterface._private),
      sendStreamRequest: bodhiextInterface._private.sendStreamRequest.bind(
        bodhiextInterface._private
      ),
      ping: bodhiextInterface.ping.bind(bodhiextInterface),
      serverState: bodhiextInterface.serverState.bind(bodhiextInterface),
      chat: bodhiextInterface.chat,
      getExtensionId() {
        return bodhiextInterface._private._getExtensionId();
      },
    };

    // Freeze the interface to prevent modifications
    Object.freeze(publicInterface);

    // Define a non-configurable, non-writable property on window
    Object.defineProperty(window, 'bodhiext', {
      value: publicInterface,
      writable: false,
      configurable: false,
    });

    // Listen for API responses
    window.addEventListener('message', bodhiextInterface._private.handleApiResponse);

    // Request the extension ID right away
    bodhiextInterface._private.requestExtensionId();

    logger.info('Interface successfully created');
  };
  createInterface();
})();

// Public interface for window.bodhiext
interface PublicBodhiExtInterface {
  /**
   * Send a generic API request to the extension backend.
   * @param method HTTP method (GET, POST, etc)
   * @param endpoint API endpoint (e.g. '/v1/chat/completions')
   * @param body Optional request body
   * @param headers Optional headers
   * @returns Promise resolving to { body, headers, status }
   */
  sendApiRequest: (
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ) => Promise<{ body: any; headers: Record<string, string>; status: number }>;

  /**
   * Send a streaming API request to the extension backend.
   * @param method HTTP method (GET, POST, etc)
   * @param endpoint API endpoint (e.g. '/v1/chat/completions')
   * @param body Optional request body
   * @param headers Optional headers
   * @returns ReadableStream yielding streamed response chunks
   */
  sendStreamRequest: (
    method: string,
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ) => ReadableStream;

  ping: () => Promise<any>;

  /**
   * Get server state information from /bodhi/v1/info endpoint.
   * @returns Promise resolving to server state information
   */
  serverState: () => Promise<{
    status: 'setup' | 'ready' | 'resource-admin' | 'error' | 'unreachable';
    version?: string;
    url?: string;
    error?: {
      message: string;
      type?: string;
      code?: string;
      param?: string;
    };
  }>;

  chat: {
    completions: {
      create: (params: any) => Promise<any> | AsyncIterable<any>;
    };
  };
  getExtensionId: () => Promise<string>;
}
