import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

import webpack from 'webpack';
import WebpackObfuscator from 'webpack-obfuscator';

const webpackConfig = (_env, argv) => {
  const isProd = argv.mode === 'production';
  const obfuscatorOptions = {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 0.75,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.4,
    stringArray: true,
    stringArrayEncoding: ['base64'],
    stringArrayThreshold: 0.75,
    // Chrome extensions: avoid self-defending, domain lock, or debug protection (can break extension)
    selfDefending: false,
    debugProtection: false,
    domainLock: [],
  };

  return {
    mode: isProd ? 'production' : 'development',
    devtool: isProd ? 'source-map' : 'inline-source-map',
    entry: {
      background: path.resolve(__dirname, 'background.ts'),
      content: path.resolve(__dirname, 'content.ts'),
      inject: path.resolve(__dirname, 'inject.ts'),
    },
    output: {
      path: path.resolve(__dirname, '../dist'),
      filename: '[name].js',
      clean: false, // Don't clear the dist folder
    },
    resolve: {
      extensions: ['.ts', '.js'],
      extensionAlias: {
        '.js': ['.js', '.ts'],
      },
    },
    module: {
      rules: [
        {
          test: /\.ts$/,
          use: [
            {
              loader: 'ts-loader',
              options: {
                // Use the Chrome-specific tsconfig
                configFile: path.resolve(__dirname, 'tsconfig.json'),
                // Preserve file extensions in import statements
                compilerOptions: {
                  module: 'esnext',
                  moduleResolution: 'node',
                  noImplicitAny: true,
                  preserveConstEnums: true,
                  sourceMap: true,
                  target: 'es2020',
                },
              },
            },
          ],
          exclude: /node_modules/,
        },
      ],
    },
    optimization: {
      splitChunks: false,
      runtimeChunk: false,
    },
    plugins: [
      ...(isProd
        ? [
            new WebpackObfuscator(obfuscatorOptions, [
              'background.js',
              'content.js',
              'inject.js',
              // Exclude manifest and non-JS files
              '!manifest.json',
              '!*.map',
            ]),
          ]
        : []),
      new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(
          argv.mode || (isProd ? 'production' : 'development')
        ),
      }),
    ],
    // Explicitly disable all code splitting
    experiments: {
      outputModule: true,
    },
  };
};

export default webpackConfig;
