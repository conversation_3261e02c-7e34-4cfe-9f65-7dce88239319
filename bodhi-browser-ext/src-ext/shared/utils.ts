/* global RequestInit */
/// <reference lib="dom" />
/// Shared utility functions for Bodhi Browser Extension
import {
  MESSAGE_TYPES,
  CONTENT_TYPE_HEADER,
  CONTENT_TYPE_JSON,
  HTTP_METHOD_GET,
  SSE_DONE_MARKER,
  SSE_DATA_PREFIX,
} from './constants.js';
import { ApiResponse, ApiResponseMessage, SSEChunk } from './types.js';

// Status code ranges
const HTTP_STATUS_OK_MIN = 200;
const HTTP_STATUS_OK_MAX = 299;

// Create a shared logger instance
const isProd = process.env.NODE_ENV === 'production';

function parseLoggerArgs(args: any): any[] {
  if (typeof args === 'function') {
    const result = args();
    if (Array.isArray(result)) {
      return result;
    } else if (typeof result === 'object') {
      return [JSON.stringify(result)];
    } else {
      return [result];
    }
  } else if (Array.isArray(args)) {
    return args;
  } else if (args && typeof args === 'object') {
    return [JSON.stringify(args)];
  } else if (args !== undefined) {
    return [args];
  }
  return [];
}

export const createLogger = (tag: string) => ({
  /**
   * Enhanced info logger for dev/prod optimization.
   * Usage:
   *   logger.info('msg', [val1, val2])
   *   logger.info('msg', () => [val1, val2])
   *   logger.info('msg', () => ({foo: bar}))
   *   logger.info('msg', {foo: bar})
   */
  info: (message: string, args?: any): void => {
    if (isProd) return;
    const values = parseLoggerArgs(args);
    console.info(`[Bodhi/${tag}] ${message}`, ...values);
  },
  /**
   * Enhanced error logger for dev/prod optimization.
   * Usage:
   *   logger.error('msg', [val1, val2])
   *   logger.error('msg', () => [val1, val2])
   *   logger.error('msg', () => ({foo: bar}))
   *   logger.error('msg', {foo: bar})
   *   logger.error('msg', error)
   */
  error: (message: string, args?: any): void => {
    const values = parseLoggerArgs(args);
    console.error(`[Bodhi/${tag}] ${message}`, ...values);
  },
});

const logger = createLogger('shared');

/**
 * Centralized error handler to standardize error management across all components
 */
export const handleError = (
  error: Error | string,
  tag: string = 'unknown',
  context: Record<string, any> = {}
): Error => {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const errorObj = typeof error === 'string' ? new Error(error) : error;

  // Add context to the error object for debugging
  Object.assign(errorObj, { context, location: tag });

  // Log the error with contextual information
  logger.error('Error', { tag, errorMessage, context, stack: errorObj.stack, error: errorObj });
  return errorObj;
};

/**
 * Creates an error object with additional properties
 */
export const createErrorWithDetails = (message: string, status: number, body: any): Error => {
  const error: any = new Error(message);
  error.status = status;
  error.body = body;
  return error;
};

/**
 * Extracts error message from response body if available
 */
export const getErrorMessageFromResponse = (
  response: ApiResponse | undefined,
  defaultMessage: string
): string => {
  if (response?.body?.error?.message) {
    return response.body.error.message;
  }
  return defaultMessage;
};

/**
 * Checks if a response status indicates success
 */
export const isSuccessResponse = (status: number): boolean => {
  return status >= HTTP_STATUS_OK_MIN && status < HTTP_STATUS_OK_MAX;
};

export const createRequestOptions = (
  method: string,
  headers: Record<string, string> = {},
  body: any = null
): RequestInit => {
  const options: RequestInit = {
    method,
    headers: {
      [CONTENT_TYPE_HEADER]: CONTENT_TYPE_JSON,
      ...headers,
    },
  };
  if (method !== HTTP_METHOD_GET && body) {
    options.body = JSON.stringify(body);
  }
  return options;
};

export const createErrorResponse = (
  message: string,
  requestId: string,
  isStream: boolean = false
): ApiResponseMessage => {
  return {
    type: isStream ? MESSAGE_TYPES.STREAM_ERROR : MESSAGE_TYPES.API_RESPONSE,
    requestId,
    response: {
      body: {
        error: {
          message,
        },
      },
      status: 0,
      headers: {},
    },
  };
};

export const parseResponse = async (response: Response): Promise<ApiResponse> => {
  const contentType = response.headers.get(CONTENT_TYPE_HEADER) || '';
  const isJson = contentType.includes(CONTENT_TYPE_JSON);
  const body = isJson ? await response.json() : await response.text();
  const headers: Record<string, string> = {};
  response.headers.forEach((value, key) => {
    headers[key] = value;
  });
  return {
    body,
    status: response.status,
    headers,
  };
};

export const handleFetchError = (
  error: Error,
  responseHandler: (response: ApiResponseMessage) => void,
  requestId: string,
  isStream: boolean = false
): void => {
  logger.error('Error fetching from API', { error });
  responseHandler(createErrorResponse(error.message, requestId, isStream));
};

export const processSSEChunk = (chunk: string): SSEChunk | null => {
  if (!chunk || chunk.trim() === '') return null;

  // Check for the done marker
  if (chunk.includes(SSE_DONE_MARKER)) {
    return { done: true };
  }

  try {
    // Check if the chunk starts with the expected data prefix
    if (!chunk.startsWith(SSE_DATA_PREFIX)) {
      // If it's a keep-alive comment or other non-data line, ignore it
      if (chunk.startsWith(':')) {
        logger.info('Received SSE keep-alive comment');
        return null;
      }
      // Handle malformed chunks
      logger.info(`Received malformed SSE chunk: ${chunk}`);
      return null;
    }

    // Remove "data: " prefix from the chunk and parse JSON
    const jsonStr = chunk.replace(new RegExp(`^${SSE_DATA_PREFIX}`), '');
    const parsedData = JSON.parse(jsonStr);

    // Validate the parsed data has the expected structure
    if (parsedData && typeof parsedData === 'object') {
      return parsedData as SSEChunk;
    } else {
      logger.error('Received unexpected SSE data structure', { jsonStr });
      // Return a typed object instead of raw string
      return { data: jsonStr };
    }
  } catch (error) {
    const err = error as Error;
    logger.error('Error processing SSE chunk', { chunk, error: err.message });
    // Return a typed object instead of raw string
    return { data: chunk, error: err.message };
  }
};

/**
 * Makes an HTTP request with standardized error handling.
 * Can be used in both background and inject scripts.
 */
export const makeHttpRequest = async (
  url: string,
  options: RequestInit,
  timeout: number = 30000
): Promise<Response> => {
  try {
    // Create an AbortController for timeout handling
    const controller = new AbortController();
    const id = setTimeout(() => controller.abort(), timeout);

    // Add the signal to the options
    const requestOptions = {
      ...options,
      signal: controller.signal,
    };

    // Make the request
    const response = await fetch(url, requestOptions);
    clearTimeout(id);

    return response;
  } catch (error) {
    if ((error as any)?.name === 'AbortError') {
      throw new Error(`Request timeout after ${timeout}ms: ${url}`);
    }
    throw error;
  }
};
