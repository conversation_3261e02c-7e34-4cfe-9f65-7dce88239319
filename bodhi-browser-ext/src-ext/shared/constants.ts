// Shared constants for Bodhi Browser Extension

export const CONTENT_TYPE_JSON = 'application/json';
export const CONTENT_TYPE_HEADER = 'Content-Type';
export const DEFAULT_API_BASE_URL = 'http://localhost:1135';
export const DEFAULT_REQUEST_TIMEOUT = 30000; // 30 seconds
export const BODHI_STREAM_PORT = 'BODHI_STREAM_PORT';
export const STORAGE_KEY_BACKEND_URL = 'backendUrl';
export const HTTP_METHOD_GET = 'GET';
export const HTTP_METHOD_POST = 'POST';
export const ENDPOINT_PING = '/ping';
export const ENDPOINT_CHAT_COMPLETIONS = '/v1/chat/completions';
export const SSE_DONE_MARKER = '[DONE]';
export const SSE_DATA_PREFIX = 'data: ';
export const SSE_CHUNK_DELIMITER = '\n\n';

// Document states
export const DOCUMENT_STATE_COMPLETE = 'complete';

// Event names
export const EVENT_INITIALIZED = 'bodhiext:initialized';

// Origin fallback
export const ORIGIN_WILDCARD = '*';

// Error messages
export const ERROR_MISSING_REQUEST_ID = 'Invalid message format: missing requestId or request';
export const ERROR_CONNECTION_CLOSED = 'Connection closed unexpectedly';

export const MESSAGE_TYPES = {
  API_REQUEST: 'BODHI_API_REQUEST',
  API_RESPONSE: 'BODHI_API_RESPONSE',
  STREAM_REQUEST: 'BODHI_STREAM_REQUEST',
  STREAM_CHUNK: 'BODHI_STREAM_CHUNK',
  STREAM_ERROR: 'BODHI_STREAM_ERROR',
  ERROR: 'BODHI_ERROR',
  GET_EXTENSION_ID: 'BODHI_GET_EXTENSION_ID',
  SET_EXTENSION_ID: 'BODHI_SET_EXTENSION_ID',
};
