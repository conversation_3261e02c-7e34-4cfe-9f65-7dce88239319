// Shared types and interfaces for Bodhi Browser Extension

export interface ApiResponse {
  body: any;
  status: number;
  headers: Record<string, string>;
}

export interface ErrorBody {
  error: {
    message: string;
  };
}

export interface ApiRequest {
  method: string;
  endpoint: string;
  body?: any;
  headers?: Record<string, string>;
}

export interface ApiResponseMessage {
  type: string;
  requestId: string;
  response: ApiResponse;
}

export interface ApiRequestMessage {
  type: string;
  requestId: string;
  request: ApiRequest;
}

export interface StreamChunkMessage {
  type: string;
  requestId: string;
  response: ApiResponse;
}

export interface ErrorMessage {
  type: string;
  requestId: string;
  response: {
    body: ErrorBody;
    status: number;
    headers: Record<string, string>;
  };
}

export interface GetExtensionIdMessage {
  type: string;
  requestId: string;
}

export interface SetExtensionIdMessage {
  type: string;
  requestId: string;
  extension_id: string;
}

/**
 * Interface for stream controller to handle SSE responses
 */
export interface StreamController {
  enqueue: (chunk: any) => void;
  error: (err: Error) => void;
  complete: () => void;
}

/**
 * Interface for SSE data chunk
 */
export interface SSEChunk {
  done?: boolean;
  [key: string]: any;
}

/**
 * Server state information returned by /bodhi/v1/info endpoint
 */
export interface ServerStateInfo {
  /** Current application status */
  status: 'setup' | 'ready' | 'resource-admin' | 'error' | 'unreachable';
  /** Application version */
  version?: string;
  /** Server URL (added by extension) */
  url?: string;
  /** Error details if status is 'error' or 'unreachable' */
  error?: {
    message: string;
    type?: string;
    code?: string;
    param?: string;
  };
}

/**
 * AppInfo response from /bodhi/v1/info endpoint (matches OpenAPI spec)
 */
export interface AppInfo {
  /** Current application status */
  status: 'setup' | 'ready' | 'resource-admin' | 'error';
  /** Application version */
  version: string;
}

/**
 * OpenAI API Error format (matches OpenAPI spec)
 */
export interface OpenAIApiError {
  error: {
    message: string;
    type: string;
    code?: string;
    param?: string;
  };
}
