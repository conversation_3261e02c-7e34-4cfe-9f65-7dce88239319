/**
 * <PERSON><PERSON> Browser Plugin content script
 * Injected into web pages to facilitate communication between page and extension
 */

'use strict';

//-----------------------------------------------------------------------------------
// CONSTANTS AND CONFIGURATION
//-----------------------------------------------------------------------------------

import {
  BODHI_STREAM_PORT,
  MESSAGE_TYPES,
  DOCUMENT_STATE_COMPLETE,
  ORIGIN_WILDCARD,
  ERROR_MISSING_REQUEST_ID,
  ERROR_CONNECTION_CLOSED,
} from './shared/constants.js';

import {
  ApiRequestMessage,
  ApiResponseMessage,
  Stream<PERSON>hunkMessage,
  ErrorMessage,
  GetExtensionIdMessage,
  SetExtensionIdMessage,
} from './shared/types.js';

import { createLogger, createErrorResponse, handleError } from './shared/utils.js';

// Use console namespace to better identify logs
const logger = createLogger('content.js');

// Store active stream connections
const activeStreams = new Map<string, chrome.runtime.Port>();

//-----------------------------------------------------------------------------------
// SCRIPT INJECTION
//-----------------------------------------------------------------------------------

/**
 * Inject our interface script into the page
 */
const injectScript = (): void => {
  try {
    // Only inject if document is fully loaded
    if (document.readyState !== DOCUMENT_STATE_COMPLETE) {
      document.addEventListener('readystatechange', () => {
        if (document.readyState === DOCUMENT_STATE_COMPLETE) {
          injectScript();
        }
      });
      return;
    }

    // Create and inject the script element
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('inject.js');

    // Use a safe approach to handle onload without 'this' typing issues
    script.onload = () => {
      script.remove(); // Clean up after loading
    };

    (document.head || document.documentElement).appendChild(script);
    logger.info('Interface script injected');
  } catch (error) {
    handleError(error as Error, 'content.js:injectScript');
  }
};

//-----------------------------------------------------------------------------------
// MESSAGE HANDLERS
//-----------------------------------------------------------------------------------

/**
 * Send the extension ID to the page
 */
const sendExtensionId = (): void => {
  try {
    const extensionId = chrome.runtime.id;
    logger.info('Sending extension ID to page', { extensionId });

    window.postMessage(
      {
        type: MESSAGE_TYPES.SET_EXTENSION_ID,
        extension_id: extensionId,
      } as SetExtensionIdMessage,
      window.origin || ORIGIN_WILDCARD
    );
  } catch (error) {
    handleError(error as Error, 'content.js:sendExtensionId');
  }
};

/**
 * Handle generic API message from the page
 */
const handleApiMessage = (event: MessageEvent<ApiRequestMessage>): void => {
  if (!event.data || !event.data.type || event.data.type !== MESSAGE_TYPES.API_REQUEST) {
    return;
  }
  logger.info('Received api request from page', { type: event.data.type });

  try {
    // Validate request
    if (!event.data.requestId || !event.data.request) {
      throw new Error(ERROR_MISSING_REQUEST_ID);
    }

    // Forward the request to the background script as-is
    chrome.runtime.sendMessage(event.data, (response: ApiResponseMessage) => {
      // Forward the response back to the page as-is
      window.postMessage(response, event.origin || ORIGIN_WILDCARD);
    });
  } catch (error) {
    const err = handleError(error as Error, 'content.js:handleApiMessage');

    // Send error response back to the page
    if (event.data && event.data.requestId) {
      window.postMessage(
        createErrorResponse(err.message, event.data.requestId),
        event.origin || ORIGIN_WILDCARD
      );
    }
  }
};

/**
 * Handle get extension ID message
 */
const handleGetExtensionId = (event: MessageEvent<GetExtensionIdMessage>): void => {
  if (!event.data || !event.data.type || event.data.type !== MESSAGE_TYPES.GET_EXTENSION_ID) {
    return;
  }
  logger.info('Received get extension ID request from page', { type: event.data.type });
  sendExtensionId();
};

/**
 * Handle streaming API message from the page
 */
const handleStreamingMessage = (event: MessageEvent<ApiRequestMessage>): void => {
  if (!event.data || !event.data.type || event.data.type !== MESSAGE_TYPES.STREAM_REQUEST) {
    return;
  }
  logger.info('Received streaming request from page', { type: event.data.type });

  try {
    const port = chrome.runtime.connect({ name: BODHI_STREAM_PORT });
    const requestId = event.data.requestId;

    if (!requestId) {
      throw new Error(ERROR_MISSING_REQUEST_ID);
    }

    activeStreams.set(requestId, port);

    // Handle incoming messages from background
    port.onMessage.addListener((message: StreamChunkMessage | ErrorMessage) => {
      window.postMessage(message, event.origin || ORIGIN_WILDCARD);

      // Close connection when stream is done or has error
      if (
        (message.type === MESSAGE_TYPES.STREAM_CHUNK && message.response.body.done) ||
        message.type === MESSAGE_TYPES.STREAM_ERROR
      ) {
        activeStreams.delete(requestId);
        port.disconnect();
      }
    });

    // Handle disconnection
    port.onDisconnect.addListener(() => {
      if (activeStreams.has(requestId)) {
        // Send an error if the port was closed unexpectedly
        window.postMessage(
          createErrorResponse(ERROR_CONNECTION_CLOSED, requestId, true),
          event.origin || ORIGIN_WILDCARD
        );

        activeStreams.delete(requestId);
      }
    });

    // Send the streaming request to the background script
    port.postMessage(event.data);
  } catch (error) {
    const err = handleError(error as Error, 'content.js:handleStreamingMessage');

    // Send error response back to the page
    if (event.data && event.data.requestId) {
      window.postMessage(
        createErrorResponse(err.message, event.data.requestId, true),
        event.origin || ORIGIN_WILDCARD
      );
    }
  }
};

//-----------------------------------------------------------------------------------
// EVENT LISTENERS
//-----------------------------------------------------------------------------------

/**
 * Type guard to check if a message is an API request
 */
function isApiRequestMessage(message: any): message is ApiRequestMessage {
  return message?.type === MESSAGE_TYPES.API_REQUEST;
}

/**
 * Type guard to check if a message is a stream request
 */
function isStreamRequestMessage(message: any): message is ApiRequestMessage {
  return message?.type === MESSAGE_TYPES.STREAM_REQUEST;
}

/**
 * Type guard to check if a message is a get extension ID request
 */
function isGetExtensionIdMessage(message: any): message is GetExtensionIdMessage {
  return message?.type === MESSAGE_TYPES.GET_EXTENSION_ID;
}

/**
 * Register message listeners to handle communication with the page
 */
function registerMessageListeners(): void {
  if (document.readyState !== DOCUMENT_STATE_COMPLETE) {
    document.addEventListener('readystatechange', () => {
      if (document.readyState === DOCUMENT_STATE_COMPLETE) {
        registerMessageListeners();
      }
    });
    return;
  }

  // Listen for messages from the page
  window.addEventListener('message', (event: MessageEvent) => {
    // Make sure the message is from the page we're injected into
    if (event.source !== window) return;

    // Route message to appropriate handler based on type
    if (isApiRequestMessage(event.data)) {
      handleApiMessage(event as MessageEvent<ApiRequestMessage>);
    } else if (isStreamRequestMessage(event.data)) {
      handleStreamingMessage(event as MessageEvent<ApiRequestMessage>);
    } else if (isGetExtensionIdMessage(event.data)) {
      handleGetExtensionId(event as MessageEvent<GetExtensionIdMessage>);
    }
  });

  logger.info('Message listeners registered');
}

//-----------------------------------------------------------------------------------
// INITIALIZATION
//-----------------------------------------------------------------------------------

/**
 * Initialize the content script
 * Sets up script injection and message listeners
 */
const initContentScript = (): void => {
  try {
    logger.info('Content script initializing');
    injectScript();
    registerMessageListeners();
    logger.info('Content script initialized');
  } catch (error) {
    handleError(error as Error, 'content.js:initContentScript');
  }
};

// Start the content script
initContentScript();
