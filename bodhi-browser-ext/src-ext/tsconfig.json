{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "sourceMap": true, "outDir": "../dist", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"*": ["*", "*.js", "*.ts"]}}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "../dist"]}