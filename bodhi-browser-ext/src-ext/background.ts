/* global RequestInit */
/// <reference lib="dom" />
/**
 * <PERSON>dhi Browser Plugin background script
 * Responsible for initializing extension services and handling HTTP requests
 */

// Use strict mode for better error catching and performance
'use strict';

//-----------------------------------------------------------------------------------
// CONSTANTS AND CONFIGURATION
//-----------------------------------------------------------------------------------

import {
  DEFAULT_API_BASE_URL,
  BODHI_STREAM_PORT,
  STORAGE_KEY_BACKEND_URL,
  SSE_CHUNK_DELIMITER,
  MESSAGE_TYPES,
} from './shared/constants.js';
import { ApiRequest, ApiResponseMessage, ApiRequestMessage } from './shared/types.js';
import {
  createLogger,
  createRequestOptions,
  createErrorResponse,
  parseResponse,
  handleFetchError,
  processSSEChunk,
  makeHttpRequest,
  handleError,
} from './shared/utils.js';

let API_BASE_URL = DEFAULT_API_BASE_URL;

const logger = createLogger('background.js');

//-----------------------------------------------------------------------------------
// SHARED API UTILITY FUNCTIONS (now imported from shared/utils)
//-----------------------------------------------------------------------------------

/**
 * Handle all streaming responses with consistent format
 * Manages reading the stream and sending chunks as they arrive
 */
const handleStreamingResponse = (
  response: Response,
  responseHandler: (response: ApiResponseMessage) => void,
  requestId: string
): void => {
  const reader = response.body!.getReader();
  const decoder = new TextDecoder();
  let buffer = '';

  const read = async (): Promise<void> => {
    try {
      const { done, value } = await reader.read();
      if (done) {
        // If buffer has unprocessed data, emit it as a chunk before sending done: true
        if (buffer && buffer.trim() !== '') {
          const data = processSSEChunk(buffer);
          if (data) {
            responseHandler({
              type: MESSAGE_TYPES.STREAM_CHUNK,
              requestId,
              response: {
                body: data,
                status: response.status,
                headers: {},
              },
            });
          }
          buffer = '';
        }
        // Send the [DONE] message if the stream is complete
        responseHandler({
          type: MESSAGE_TYPES.STREAM_CHUNK,
          requestId,
          response: {
            body: { done: true },
            status: response.status,
            headers: {},
          },
        });
        return;
      }

      // Decode the value and add to buffer
      buffer += decoder.decode(value, { stream: true });

      // Process any complete SSE events
      const chunks = buffer.split(SSE_CHUNK_DELIMITER);
      buffer = chunks.pop() || ''; // Keep the last incomplete chunk (if any)

      // Process all complete chunks
      for (const chunk of chunks) {
        if (!chunk || chunk.trim() === '') continue;

        const data = processSSEChunk(chunk);
        if (data) {
          // Send chunk to client
          responseHandler({
            type: MESSAGE_TYPES.STREAM_CHUNK,
            requestId,
            response: {
              body: data,
              status: response.status,
              headers: {},
            },
          });
          // If we see the done signal, emit and stop
          if (data.done) {
            return;
          }
        }
      }

      // Continue reading
      read();
    } catch (error) {
      const err = error as Error;
      logger.error('Error processing stream chunk', { error: err });
      responseHandler(createErrorResponse(err.message, requestId, true));

      // Ensure reader is released in case of error
      try {
        reader.cancel();
      } catch (error) {
        logger.error('Error cancelling reader', { error });
      }
    }
  };

  read();
};

/**
 * Make API request with proper error handling
 * Centralized method for all fetch calls
 */
const makeApiRequest = (
  url: string,
  options: RequestInit,
  responseHandler: (response: ApiResponseMessage) => void,
  requestId: string,
  isStream: boolean = false
): void => {
  makeHttpRequest(url, options)
    .then(async response => {
      if (!response.ok) {
        const parsedResponse = await parseResponse(response);
        responseHandler({
          type: isStream ? MESSAGE_TYPES.STREAM_CHUNK : MESSAGE_TYPES.API_RESPONSE,
          requestId,
          response: parsedResponse,
        });
        return;
      }

      if (isStream) {
        // Handle streaming response
        handleStreamingResponse(response, responseHandler, requestId);
      } else {
        // Handle regular response
        const parsedResponse = await parseResponse(response);
        responseHandler({
          type: MESSAGE_TYPES.API_RESPONSE,
          requestId,
          response: parsedResponse,
        });
      }
    })
    .catch(error => handleFetchError(error as Error, responseHandler, requestId, isStream));
};

//-----------------------------------------------------------------------------------
// INITIALIZATION AND LIFECYCLE HANDLERS
//-----------------------------------------------------------------------------------

/**
 * Initialize the extension's background service
 * Sets up backend URL configuration and listeners
 */
const initExtension = (): void => {
  try {
    logger.info('Background service worker initialized');

    // Load the configured backend URL
    chrome.storage.local.get([STORAGE_KEY_BACKEND_URL], result => {
      if (result[STORAGE_KEY_BACKEND_URL]) {
        API_BASE_URL = result[STORAGE_KEY_BACKEND_URL];
        logger.info('Using configured backend URL', { API_BASE_URL });
      } else {
        logger.info('Using default backend URL', { API_BASE_URL });
      }
    });

    // Listen for changes to the backend URL
    chrome.storage.onChanged.addListener((changes, area) => {
      if (area === 'local' && changes[STORAGE_KEY_BACKEND_URL]) {
        API_BASE_URL = changes[STORAGE_KEY_BACKEND_URL].newValue || DEFAULT_API_BASE_URL;
        logger.info('Backend URL updated', { API_BASE_URL });
      }
    });
  } catch (error) {
    handleError(error as Error, 'background.js:initExtension');
  }
};

/**
 * Handle extension installation or update events
 * Triggered when the extension is first installed or updated to a new version
 */
chrome.runtime.onInstalled.addListener(details => {
  try {
    logger.info('Extension event', { reason: details.reason });

    // Handle first installation vs update differently if needed
    if (details.reason === 'install') {
      // First time installation logic
    } else if (details.reason === 'update') {
      // Update logic
    }
  } catch (error) {
    handleError(error as Error, 'background.js:onInstalled', { details });
  }
});

//-----------------------------------------------------------------------------------
// CONTENT SCRIPT STREAMING COMMUNICATION (LONG-LIVED CONNECTIONS)
//-----------------------------------------------------------------------------------

/**
 * Handle long-lived connections from content scripts
 * Used for streaming responses where multiple messages are sent over time
 * Invoked when a content script calls chrome.runtime.connect()
 */
chrome.runtime.onConnect.addListener(port => {
  if (port.name !== BODHI_STREAM_PORT) {
    logger.error('Invalid port name', { portName: port.name });
    return;
  }

  port.onMessage.addListener(async (message: ApiRequestMessage) => {
    logger.info('Received onConnect request', { type: message.type });
    try {
      const requestId = message?.requestId || '';
      if (message.type !== MESSAGE_TYPES.STREAM_REQUEST) {
        logger.error('Invalid message type for onConnect request', { type: message.type });
        port.postMessage(createErrorResponse('Invalid message type', requestId, true));
        return;
      }

      const requestConfig = parseApiRequestMessage(message);

      if ((requestConfig as ApiResponseMessage).type === MESSAGE_TYPES.ERROR) {
        logger.error('Malformed streaming request', { requestId });
        port.postMessage(requestConfig);
        return;
      }

      logger.info('Processing onConnect request', { requestId });
      const { method, endpoint, body, headers = {} } = requestConfig as ApiRequest;
      const requestOptions = createRequestOptions(method, headers, body);
      const url = `${API_BASE_URL}${endpoint}`;

      makeApiRequest(url, requestOptions, response => port.postMessage(response), requestId, true);
    } catch (error) {
      const requestId = (message as any).data?.requestId || '';
      handleError(error as Error, 'background.js:onConnect', {
        requestId,
        messageType: message.type,
      });
      port.postMessage(createErrorResponse((error as Error).message, requestId, true));
    }
  });
});

//-----------------------------------------------------------------------------------
// CONTENT SCRIPT ONE-TIME MESSAGING
//-----------------------------------------------------------------------------------

/**
 * Handle one-time messages from content scripts
 * Used for regular API requests that expect a single response
 * Invoked when a content script calls chrome.runtime.sendMessage()
 */
chrome.runtime.onMessage.addListener((message: ApiRequestMessage, sender, sendResponse) => {
  logger.info('Received onMessage request', { senderId: sender.id });

  try {
    const requestId = message?.requestId || '';
    if (message.type !== MESSAGE_TYPES.API_REQUEST) {
      logger.error('Invalid message type for onMessage request', { type: message.type, requestId });
      sendResponse(createErrorResponse('Invalid message type', requestId));
      return;
    }

    const requestConfig = parseApiRequestMessage(message);

    if ((requestConfig as ApiResponseMessage).type === MESSAGE_TYPES.ERROR) {
      logger.error('Malformed request in onMessage', { requestId });
      sendResponse(requestConfig);
      return;
    }

    const { method, endpoint, body, headers = {} } = requestConfig as ApiRequest;
    logger.info('Processing onMessage request', { method, endpoint, requestId });

    const requestOptions = createRequestOptions(method, headers, body);
    const url = `${API_BASE_URL}${endpoint}`;

    makeHttpRequest(url, requestOptions)
      .then(async response => {
        const parsedResponse = await parseResponse(response);
        sendResponse({
          type: MESSAGE_TYPES.API_RESPONSE,
          requestId,
          response: parsedResponse,
        });
      })
      .catch(error => {
        const err = error as Error;
        logger.error('Error fetching from API', { error: err.message, requestId });
        sendResponse(createErrorResponse(err.message, requestId));
      });

    return true; // Indicate async response
  } catch (error) {
    const requestId = message?.requestId || '';
    const err = error as Error;
    logger.error('Error handling onMessage request', { error: err.message, requestId });
    sendResponse(createErrorResponse(err.message, requestId));
  }
});

//-----------------------------------------------------------------------------------
// EXTERNAL EXTENSION ONE-TIME MESSAGING
//-----------------------------------------------------------------------------------

/**
 * Process external extension API requests
 * Maps actions like 'ping' and 'chat.completions.create' to appropriate endpoints
 */
/**
 * Process external extension API requests
 * Supports both legacy (action/payload) and unified (type/request/requestId) formats
 */
const parseApiRequestMessage = (message: ApiRequestMessage): ApiRequest | ApiResponseMessage => {
  // Unified format: { type, requestId, request: { method, endpoint, body, headers } }
  const { type, requestId, request } = message as any;
  const method = request?.method;
  const endpoint = request?.endpoint;

  let missingField = '';
  if (!type) {
    missingField = 'type';
  } else if (!requestId) {
    missingField = 'requestId';
  } else if (!request) {
    missingField = 'request';
  } else if (!method) {
    missingField = 'method';
  } else if (!endpoint) {
    missingField = 'endpoint';
  }

  if (missingField) {
    return {
      type: MESSAGE_TYPES.ERROR,
      requestId: requestId || '',
      response: {
        status: 0,
        headers: {},
        body: {
          error: {
            message: `Malformed request: missing ${missingField}`,
          },
        },
      },
    };
  }

  const fixedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return { endpoint: fixedEndpoint, method, body: request.body, headers: request.headers || {} };
};

/**
 * Handle one-time messages from other extensions
 * Used when external extensions need to make API requests through this extension
 * Invoked when another extension calls chrome.runtime.sendMessage(extensionId, ...)
 */
chrome.runtime.onMessageExternal.addListener((message: ApiRequestMessage, sender, sendResponse) => {
  logger.info('Received onMessageExternal request', { senderId: sender.id });

  try {
    // message is always ApiRequestMessage, so we don't need type checks
    const requestConfig = parseApiRequestMessage(message);
    const requestId = message.requestId;

    // If there was an error in the request, return it
    if ((requestConfig as ApiResponseMessage).type === MESSAGE_TYPES.ERROR) {
      logger.info('Error response onMessageExternal', { requestId });
      sendResponse(requestConfig);
      return;
    }

    const { method, endpoint, body, headers = {} } = requestConfig as ApiRequest;
    const requestOptions = createRequestOptions(method, headers, body);
    const url = `${API_BASE_URL}${endpoint}`;

    makeHttpRequest(url, requestOptions)
      .then(async response => {
        const parsedResponse = await parseResponse(response);
        sendResponse({
          type: MESSAGE_TYPES.API_RESPONSE,
          requestId,
          response: parsedResponse,
        });
      })
      .catch(error => {
        const err = error as Error;
        handleError(err, 'background.js:onMessageExternal', {
          requestId,
          senderId: sender.id,
        });
        sendResponse(createErrorResponse(err.message, requestId || ''));
      });

    return true; // Indicate async response
  } catch (error) {
    const err = error as Error;
    handleError(err, 'background.js:onMessageExternal', {
      messageType: message?.type,
      requestId: message?.requestId,
      senderId: sender.id,
    });
    sendResponse(createErrorResponse(err.message, message?.requestId || ''));
  }
});

//-----------------------------------------------------------------------------------
// EXTERNAL EXTENSION STREAMING COMMUNICATION
//-----------------------------------------------------------------------------------

/**
 * Handle long-lived connections from other extensions
 * Used for streaming responses to external extensions
 * Invoked when another extension calls chrome.runtime.connect(extensionId, ...)
 */
chrome.runtime.onConnectExternal.addListener(port => {
  logger.info('Received onConnectExternal connection request', {
    senderId: port.sender?.id || 'unknown',
  });

  try {
    // Handle messages on this port
    port.onMessage.addListener(async (request: ApiRequestMessage) => {
      // message is always ApiRequestMessage, so we don't need type checks
      const requestConfig = parseApiRequestMessage(request);
      const requestId = request.requestId;

      try {
        // If there was an error in the request, return it
        if ((requestConfig as ApiResponseMessage).type === MESSAGE_TYPES.ERROR) {
          port.postMessage(requestConfig);
          return;
        }

        const { endpoint, method, body, headers = {} } = requestConfig as ApiRequest;
        const requestOptions = createRequestOptions(method, headers, body);
        const url = `${API_BASE_URL}${endpoint}`;

        makeApiRequest(
          url,
          requestOptions,
          response => port.postMessage(response),
          requestId || '',
          true
        );
      } catch (error) {
        const err = error as Error;
        handleError(err, 'background.js:onConnectExternal.onMessage', {
          requestId,
          senderId: port.sender?.id,
        });
        port.postMessage(createErrorResponse(err.message, requestId || '', true));
      }
    });

    // Handle disconnection
    port.onDisconnect.addListener(() => {
      logger.info('Extension disconnected', { senderId: port.sender?.id || 'unknown' });
      // Any cleanup needed when an extension disconnects
    });
  } catch (error) {
    handleError(error as Error, 'background.js:onConnectExternal', {
      senderId: port.sender?.id,
    });
  }
});

//-----------------------------------------------------------------------------------
// INITIALIZE EXTENSION
//-----------------------------------------------------------------------------------

// Immediately Invoked Function Expression (IIFE) to start the extension
(function () {
  initExtension();
})();
