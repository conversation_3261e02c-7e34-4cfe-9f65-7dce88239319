/* Although we're using Tailwind CSS inline classes in our components,
   you can also use CSS modules if preferred */

/* .container {
  min-height: 100vh;
  padding: 0 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.main {
  padding: 5rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
} */

.container {
  width: 350px;
  height: 485px;
  background: linear-gradient(153deg, rgb(116, 56, 216), rgb(18, 150, 170) 74%);
  padding: 5px;
  color: white;
}

.main {
  width: 340px;
  height: 475px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
} 

.title {
  margin: 0;
  line-height: 1.15;
  font-size: 2rem;
  text-align: center;
}

.description {
  text-align: center;
  line-height: 1.5;
  font-size: 1.5rem;
  max-width: 300px;
  margin: 1rem 0;
}

.code {
  background: #fafafa;
  border-radius: 5px;
  padding: 0.75rem;
  font-size: 1.1rem;
  font-family: <PERSON>, Monaco, <PERSON><PERSON>, Liberation Mono,
    <PERSON><PERSON><PERSON>, Bitstream Vera Sans Mono, Courier New, monospace;
} 