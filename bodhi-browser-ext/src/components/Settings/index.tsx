import React, { useEffect, useState } from 'react';

declare global {
  interface Window {
    chrome: typeof chrome;
  }
}

export default function Settings() {
  const [backendUrl, setBackendUrl] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  useEffect(() => {
    // Load the saved backend URL when component mounts
    chrome.storage.local.get(['backendUrl'], result => {
      if (result.backendUrl) {
        setBackendUrl(result.backendUrl);
      }
    });
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage({ type: '', text: '' });

    try {
      // Validate URL format
      new URL(backendUrl);

      // Save to chrome.storage
      await chrome.storage.local.set({ backendUrl });
      setMessage({ type: 'success', text: 'Settings saved successfully!' });
    } catch {
      setMessage({
        type: 'error',
        text: `Input url '${backendUrl}' is not a valid URL. Please enter a valid URL`,
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="w-[350px] h-[400px] overflow-auto bg-gradient-to-br from-purple-600 to-teal-500 p-4">
      <main className="flex flex-col items-center gap-4 bg-white rounded-lg shadow-lg p-5 w-full">
        <h1 className="text-xl font-bold text-gray-800">Bodhi Browser Settings</h1>
        <form onSubmit={handleSubmit} className="w-full space-y-4">
          <div className="space-y-2">
            <label htmlFor="backendUrl" className="block text-sm font-medium text-gray-700">
              Backend Server URL
            </label>
            <input
              type="text"
              id="backendUrl"
              value={backendUrl}
              onChange={e => setBackendUrl(e.target.value)}
              placeholder="http://localhost:1135"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>

          {message.text && (
            <div
              id="message-container"
              className={`message-container ${message.type} p-3 rounded-md ${
                message.type === 'success'
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
              }`}
            >
              {message.text}
            </div>
          )}

          <button
            id="submit"
            type="submit"
            disabled={isSaving}
            className={`w-full px-4 py-2 text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
              isSaving ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isSaving ? 'Saving...' : 'Save Settings'}
          </button>
        </form>
      </main>
    </div>
  );
}
