# Bodhi Browser Extension

A secure Chrome extension that provides a bridge between web pages and locally running LLM services. This extension is part of the Bodhi Browser project and works in conjunction with the [@bodhiapp/bodhijs](../bodhi-js) library.

## Features

- Secure communication bridge between web pages and local LLM services
- OpenAI-compatible API for chat completions
- Support for multiple LLM models
- Built-in security measures to prevent unauthorized access
- Zero-dependency core for minimal attack surface

## Installation

### For Development

1. Clone the repository:

```bash
git clone https://github.com/BodhiSearch/bodhi-browser.git
cd bodhi-browser
```

2. Install dependencies and build the extension:

```bash
make setup
make bodhi-browser-ext
```

3. Load the extension in Chrome:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked" and select the `bodhi-browser-ext/dist` directory

### Loading in Brave Browser

To load the extension in Brave Browser:

1. Install Brave Browser if you haven't already:
   ```bash
   brew install --cask brave-browser
   ```

2. Build the extension:
   ```bash
   make bodhi-browser-ext
   ```

3. Load the extension in Brave:
   - Open Brave and navigate to `brave://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked" and select the `bodhi-browser-ext/dist` directory

4. Test the extension in Brave:
   ```bash
   make ext-test-brave
   ```

### Loading in Microsoft Edge

To load the extension in Microsoft Edge:

1. Install Microsoft Edge if you haven't already:
   ```bash
   brew install --cask microsoft-edge
   ```

2. Build the extension:
   ```bash
   npm run build
   ```

3. Load the extension in Edge:
   - Open Edge and navigate to `edge://extensions/`
   - Enable "Developer mode" in the left sidebar
   - Click "Load unpacked" and select the `bodhi-browser-ext/dist` directory

4. Test the extension in Edge:
   ```bash
   npm run test:edge
   ```

### For Users

1. Download the latest release from the [releases page](https://github.com/BodhiSearch/bodhi-browser/releases)
2. Unzip the downloaded file
3. Follow the same steps as above to load the extension in Chrome

## Development

The extension uses a simple architecture with three main components:

- `background.js`: Handles communication with local LLM services
- `content.js`: Manages communication between web pages and the background script
- `inject.js`: Provides the interface for web pages to interact with the extension

### Commands

```bash
# Install dependencies
npm install

# Build the extension
npm run build

# Run validation (format and lint checks)
npm run validate

# Format code
npm run format

# Run lint checks
npm run lint

# Run tests
npm test
```

### Development Workflow

1. Make your changes in the source files
2. Run validation: `npm run validate`
3. Build the extension: `npm run build`
4. Test your changes:
   - Automated tests: `npm test`
   - Manual testing: Load the extension from `dist/` directory
5. Create a pull request with your changes

## Fast Build

The project includes a fast build system that only rebuilds when source files have changed:

```bash
# Using npm
npm run build:fast

# Using make
make build-fast
```

The fast build system:
- Monitors changes in: `src/`, `public/`, `src-ext/`, `package.json`, and `next.config.ts`
- Stores MD5 hashes of these directories/files in `dist/.build-cache.json`
- Only runs the full build when changes are detected
- Shows which files/directories triggered the build
- Significantly speeds up development when no changes have been made

Example output when no changes are detected:
```
🚀 Fast build check starting...
🔍 Checking for changes...
   Hashing src...
   Hashing public...
   Hashing src-ext...
   Hashing package.json...
   Hashing next.config.ts...
✨ No changes detected, skipping build
```

Example output when changes are detected:
```
🚀 Fast build check starting...
🔍 Checking for changes...
   Hashing src...
   Hashing public...
   Hashing src-ext...
   Hashing package.json...
   Hashing next.config.ts...
📝 Changes detected in:
   - src
🔨 Running build...
[... normal build output ...]
✅ Fast build completed successfully
```

## Testing

The extension is tested at multiple levels:

1. Unit tests for individual components
2. Integration tests with the bodhijs library
3. End-to-end tests simulating real usage

To run the complete test suite:

```bash
# From the project root
make test

# Or specifically for the extension
make test-bodhi-browser-ext
```

### Testing with Different Browsers

The extension supports testing on multiple browsers across different platforms:

```bash
# Test with Chrome (default, Puppeteer will locate Chrome automatically)
npm test

# Test with Brave browser
npm run test:brave

# Test with Microsoft Edge
npm run test:edge

# Test with a custom browser path
BROWSER_PATH=/path/to/browser npm test

# Force using platform-specific path for Chrome (instead of Puppeteer default)
FORCE_PATH=true BROWSER=chrome npm test
```

#### Cross-Platform Testing

The test system automatically detects your operating system and uses the appropriate browser paths:

- **macOS**: Applications folder paths (e.g., `/Applications/Google Chrome.app/Contents/MacOS/Google Chrome`)
- **Windows**: Program Files paths (e.g., `C:\Program Files\Google\Chrome\Application\chrome.exe`)
- **Linux**: Standard binary paths (e.g., `/usr/bin/google-chrome`)

In CI environments, the system will automatically use headless mode and let Puppeteer handle browser detection.

## Security

The extension implements several security measures:

1. Content Security Policy (CSP) to prevent XSS attacks
2. Message validation to ensure only authorized origins can access LLM services
3. Minimal permissions model - only requests necessary permissions
4. No external dependencies in core functionality
5. Regular security audits and updates

## Contributing

1. Fork the repository
2. Create your feature branch: `git checkout -b feature/my-new-feature`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin feature/my-new-feature`
5. Submit a pull request

## Release Process

Releases are managed through GitHub Actions:

1. Ensure all tests pass: `make test`
2. Update version in `manifest.json`
3. Create a release: `make release-bodhi-browser-ext`
4. GitHub Actions will:
   - Build the extension
   - Create a release with the extension package
   - Tag the repository

## License

MIT
