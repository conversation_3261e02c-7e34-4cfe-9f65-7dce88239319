// Fix paths in exported files for Chrome Extension

import fs from 'fs';
import { glob } from 'glob';

console.log('Fixing paths in exported files...');

try {
  // Find all HTML files in the dist directory
  const files = glob.sync('dist/**/*.html');

  files.forEach(file => {
    const content = fs.readFileSync(file, 'utf-8');
    // Replace /_next/ with ./next/ in all HTML files
    const modifiedContent = content.replace(/\/_next/g, './next');
    fs.writeFileSync(file, modifiedContent, 'utf-8');
    console.log(`Fixed paths in: ${file}`);
  });

  // Rename _next directory to next
  const sourcePath = 'dist/_next';
  const destinationPath = 'dist/next';

  fs.rename(sourcePath, destinationPath, err => {
    if (err) {
      console.error('Failed to rename "_next" directory to "next".', err);
    } else {
      console.log('Renamed "_next" directory to "next" successfully.');
    }
  });

  console.log('Path fixing completed successfully!');
} catch (error) {
  console.error('Error fixing paths:', error);
}
