{"name": "bodhi-browser", "version": "0.0.5", "description": "A secure Chrome extension that provides a bridge between web pages and locally running LLM services.", "type": "module", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run build:ui && npm run build:ext", "build:fast": "node ../scripts/build-fast.mjs . 'npm run build' src public src-ext package.json next.config.ts", "build:ui": "next build && node fix-paths.mjs", "build:ext": "webpack --config src-ext/webpack.config.js", "build:release": "npm run build:ui:release && npm run build:ext:release", "build:ui:release": "cross-env NODE_ENV=production next build && node fix-paths.mjs", "build:ext:release": "webpack --config src-ext/webpack.config.js --mode production", "test": "vitest run", "test:brave": "BROWSER=brave vitest run", "test:edge": "BROWSER=edge vitest run", "test:ci": "cross-env CI=true vitest run", "test:watch": "vitest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx,.html", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx,.html --fix", "validate": "npm run lint", "download-llama-binaries": "node ../scripts/download-llama-binaries.js"}, "dependencies": {"next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@bodhiapp/app-bindings": "^0.0.15", "@bodhiapp/mock-llm-server": "file:../mock-llm-server", "@eslint/eslintrc": "^3", "@playwright/test": "^1.54.0", "@tailwindcss/postcss": "^4", "@types/chrome": "^0.0.313", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.28.0", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-html": "^8.1.2", "eslint-plugin-prettier": "^5.1.3", "glob": "^10.3.10", "playwright": "^1.54.0", "prettier": "^3.2.5", "rimraf": "^6.0.1", "tailwindcss": "^4", "ts-loader": "^9.5.2", "tsx": "^4.20.3", "typescript": "^5", "vitest": "^3.0.8", "webpack": "^5.99.6", "webpack-cli": "^5.1.4", "webpack-obfuscator": "^3.5.1"}, "keywords": [], "author": "", "license": "ISC"}