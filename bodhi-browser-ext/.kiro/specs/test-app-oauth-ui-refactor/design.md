# Design Document

## Overview

This design document outlines the comprehensive refactoring of the test-app-oauth user interface to create a modern, professional, and unified application experience. The design transforms the current collection of separate testing pages into a cohesive multi-page application that feels like a complete application rather than individual test pages.

The refactored application will serve as both an integration test for the Bodhi Browser Extension and a high-quality reference implementation for developers building OAuth2 integrations with BodhiApp. The design emphasizes creating an app-like experience with consistent navigation, shared design system, and unified visual identity across all pages while maintaining the existing multi-page structure required for OAuth flow and extension testing.

## Architecture

### Design System Architecture

The new design will implement a modular design system with the following structure:

```
Design System Components:
├── Core Styles
│   ├── CSS Variables (colors, spacing, typography)
│   ├── Reset and base styles
│   └── Utility classes
├── Component Styles
│   ├── Buttons (primary, secondary, danger)
│   ├── Forms (inputs, textareas, selects)
│   ├── Cards and containers
│   ├── Navigation
│   ├── Status indicators
│   └── Loading states
└── Layout Styles
    ├── Grid system
    ├── Responsive breakpoints
    └── Page layouts
```

### Application Architecture

The application maintains its multi-page structure while creating a unified app-like experience through consistent design and navigation:

#### Page Structure
1. **Landing Page (`index.html`)** - Extension detection, OAuth initiation, and extension testing capabilities (merged from libbodhiext-test.html)
2. **Callback Page (`callback.html`)** - OAuth callback processing with immediate redirect after token receipt
3. **API Test Page (`api-test.html`)** - API testing interface with working status indicators

#### Removed Pages
- **User Dashboard (`user.html`)** - Functionality moved to navigation bar user info display
- **Extension Test Page (`libbodhiext-test.html`)** - Functionality merged into index.html

#### Unified Experience Features
- **Consistent Navigation**: Shared navigation component with user info display in header
- **Single CSS File**: Merged CSS without conflicts for consistent styling
- **Single TypeScript File**: Consolidated TypeScript with shared utilities and no duplicates
- **Playwright Test IDs**: Elements have proper IDs for easy test selection
- **User Info in Navigation**: User login status displayed across all pages (index.html, api-test.html)

#### Cross-Page Consistency
- **Visual Identity**: Consistent color scheme, typography, and component styling
- **Navigation Pattern**: Same navigation structure with user info display
- **Status Communication**: Working API status indicators (Ready → Calling → Streaming → Completed/Error)
- **Form Design**: Consistent form styling and interaction patterns
- **Extension Detection**: Unified extension detection across pages

## Components and Interfaces

### Design System Components

#### Color Palette

```css
:root {
  /* Primary Colors */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #dbeafe;
  
  /* Secondary Colors */
  --color-secondary: #64748b;
  --color-secondary-hover: #475569;
  --color-secondary-light: #f1f5f9;
  
  /* Status Colors */
  --color-success: #059669;
  --color-success-light: #d1fae5;
  --color-warning: #d97706;
  --color-warning-light: #fef3c7;
  --color-error: #dc2626;
  --color-error-light: #fee2e2;
  --color-info: #0891b2;
  --color-info-light: #cffafe;
  
  /* Neutral Colors */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Background Colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;
}
```

#### Typography System

```css
:root {
  /* Font Families */
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  
  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
}
```

#### Spacing System

```css
:root {
  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem;   /* 8px */
  --radius-xl: 0.75rem;  /* 12px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}
```

### Component Specifications

#### Button Component

```css
.btn {
  /* Base button styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  
  /* Focus styles */
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
  
  /* Disabled styles */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  
  &:hover:not(:disabled) {
    background-color: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
  }
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: white;
  border-color: var(--color-secondary);
  
  &:hover:not(:disabled) {
    background-color: var(--color-secondary-hover);
    border-color: var(--color-secondary-hover);
  }
}

.btn-danger {
  background-color: var(--color-error);
  color: white;
  border-color: var(--color-error);
  
  &:hover:not(:disabled) {
    background-color: #b91c1c;
    border-color: #b91c1c;
  }
}
```

#### Form Component

```css
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--space-3);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-gray-900);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px var(--color-primary-light);
  }
  
  &::placeholder {
    color: var(--color-gray-400);
  }
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-help {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  margin-top: var(--space-1);
}
```

#### Status Indicator Component

```css
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border-radius: var(--radius-md);
  border: 1px solid;
  
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--space-2);
  }
}

.status-ready {
  background-color: var(--color-info-light);
  border-color: var(--color-info);
  color: var(--color-info);
  
  &::before {
    background-color: var(--color-info);
  }
}

.status-calling,
.status-loading {
  background-color: var(--color-warning-light);
  border-color: var(--color-warning);
  color: var(--color-warning);
  
  &::before {
    background-color: var(--color-warning);
    animation: pulse 2s infinite;
  }
}

.status-streaming {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
  
  &::before {
    background-color: var(--color-primary);
    animation: pulse 2s infinite;
  }
}

.status-completed,
.status-success {
  background-color: var(--color-success-light);
  border-color: var(--color-success);
  color: var(--color-success);
  
  &::before {
    background-color: var(--color-success);
  }
}

.status-error {
  background-color: var(--color-error-light);
  border-color: var(--color-error);
  color: var(--color-error);
  
  &::before {
    background-color: var(--color-error);
  }
}
```

#### Card Component

```css
.card {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-bg-secondary);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-gray-900);
  margin: 0;
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-bg-secondary);
}
```

### Application Layout Components

#### App Header Component

```css
.app-header {
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.app-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.app-subtitle {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  margin: 0;
}

.app-status-bar {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.extension-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.auth-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}
```

#### Navigation Component (Shared Across Pages)

```css
.app-navigation {
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-gray-200);
  padding: 0 var(--space-6);
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-1);
}

.nav-item {
  position: relative;
}

.nav-link {
  display: block;
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--color-gray-600);
  text-decoration: none;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  transition: all 0.2s ease-in-out;
  position: relative;
  
  &:hover:not(.active) {
    color: var(--color-primary);
    background-color: var(--color-gray-50);
  }
  
  &.active {
    color: var(--color-primary);
    background-color: var(--color-bg-secondary);
    border-bottom: 2px solid var(--color-primary);
    font-weight: var(--font-semibold);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: -2px;
  }
}

/* Navigation icons for better visual hierarchy */
.nav-link::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: var(--space-2);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.nav-link[href="/"]::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'/%3E%3C/svg%3E");
}

.nav-link[href="/api-test.html"]::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'/%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 12a3 3 0 11-6 0 3 3 0 016 0z'/%3E%3C/svg%3E");
}

/* User info display in header */
.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

.user-info.authenticated {
  color: var(--color-success);
}

.user-info::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
```

#### Main Layout Component

```css
.app-container {
  min-height: 100vh;
  background-color: var(--color-bg-secondary);
  display: flex;
  flex-direction: column;
}

.app-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  background-color: var(--color-bg-primary);
  box-shadow: var(--shadow-sm);
  min-height: calc(100vh - 120px);
}

.section-container {
  padding: var(--space-8);
}

.section-header {
  margin-bottom: var(--space-8);
  text-align: center;
}

.section-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
}

.section-description {
  font-size: var(--text-lg);
  color: var(--color-gray-600);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
```

## Data Models

### Status State Management

The application will implement a centralized status management system:

```typescript
interface StatusState {
  extension: 'detecting' | 'ready' | 'error' | 'not-found';
  api: 'ready' | 'calling' | 'streaming' | 'completed' | 'error';
  auth: 'unauthenticated' | 'authenticating' | 'authenticated' | 'error';
}

interface StatusMessage {
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  details?: string;
}
```

### User Data Display Model

Simplified user data display for navigation bar:

```typescript
interface UserDisplayData {
  email?: string;
  loggedIn: boolean;
  tokenType?: string;
  role?: string;
  roleSource?: string;
}
```

## Error Handling

### Simplified Error Display System

All errors will be handled through a simple error display system:

```typescript
interface ErrorDisplay {
  show(error: string | Error, context?: string): void;
  clear(): void;
}

class ErrorManager implements ErrorDisplay {
  private errorElement: HTMLElement;
  
  show(error: string | Error, context?: string): void {
    const message = error instanceof Error ? error.message : error;
    const fullMessage = context ? `${context}: ${message}` : message;
    
    this.errorElement.textContent = fullMessage;
    this.errorElement.className = 'error-message show';
  }
  
  clear(): void {
    this.errorElement.textContent = '';
    this.errorElement.className = 'error-message';
  }
}
```

### Status Update System

A centralized status update system will manage all status indicators:

```typescript
class StatusManager {
  private statusElements: Map<string, HTMLElement> = new Map();
  
  updateStatus(elementId: string, status: string, message?: string): void {
    const element = this.statusElements.get(elementId);
    if (element) {
      element.className = `status-indicator status-${status}`;
      element.textContent = message || this.getDefaultMessage(status);
    }
  }
  
  private getDefaultMessage(status: string): string {
    const messages = {
      ready: 'Ready',
      calling: 'Calling API...',
      streaming: 'Streaming...',
      completed: 'Completed',
      error: 'Error occurred'
    };
    return messages[status] || status;
  }
}
```

### Page-Specific Design Considerations

#### Landing Page (`index.html`) Integration
- **Extension Test Integration**: Merge functionality from `libbodhiext-test.html` into the main landing page
- **Multi-Section Layout**: Create sections for OAuth flow and extension testing within the same page
- **Progressive Disclosure**: Show extension testing features after successful extension detection
- **Unified Status Management**: Single status system for both OAuth and extension testing

#### API Test Page Enhancement
- **Working Status Indicators**: Fix the broken API status system with proper state management
- **Real-time Updates**: Implement proper status transitions (Ready → Calling → Streaming → Completed/Error)
- **Enhanced Form Design**: Improve the API testing form with better organization and visual hierarchy
- **Response Display**: Better formatting and presentation of API responses

#### User Info in Navigation
- **Simplified Display**: Show basic user info (email, login status, role) in the navigation header
- **Status Indicators**: Use visual indicators to show authentication state
- **Consistent Across Pages**: User info displayed on both index.html and api-test.html pages

#### Callback Page Polish
- **Progress Visualization**: Add step-by-step progress indicators for OAuth flow
- **Better Error Handling**: Improve error messaging and recovery options
- **Loading Animations**: Add smooth transitions and loading states

### Extension Test Migration Strategy

Since this application is used to test the bodhi-browser-ext chrome extension, the design must ensure:

1. **Test Compatibility**: All existing extension tests (*.test.ts files) must continue to work
2. **Element Preservation**: Critical test elements and IDs must be maintained
3. **Functionality Preservation**: All testing capabilities must be preserved or enhanced
4. **New Test Integration**: Extension tests should be updated to work with the new UI structure

### Browser Compatibility

The design will support:

- Chrome 90+ (Primary target for extension testing)
- Firefox 88+
- Safari 14+
- Edge 90+

### Performance Considerations

1. **CSS Optimization**: Minimal CSS with efficient selectors optimized for extension testing
2. **Fast Loading**: Quick page loads for efficient testing cycles
3. **Memory Efficiency**: Optimized for repeated test runs
4. **Extension Communication**: Efficient handling of extension API calls

## Implementation Approach

### Phase 1: Design System Foundation
1. Create CSS variables and base styles
2. Implement core components (buttons, forms, cards)
3. Set up responsive grid system

### Phase 2: Page Refactoring
1. Refactor landing page with new design
2. Update callback page with progress indicators
3. Enhance user dashboard with structured data display
4. Fix and improve API test page with working status indicators

### Phase 3: Enhanced Features
1. Add loading animations and micro-interactions
2. Implement accessibility improvements
3. Add responsive design optimizations
4. Create comprehensive error handling

### Phase 4: Polish and Testing
1. Cross-browser testing and fixes
2. Performance optimization
3. Accessibility audit and improvements
4. Final visual polish and animations

This design provides a comprehensive foundation for creating a modern, professional, and highly functional reference application that serves both as an integration test and a showcase of best practices for OAuth2 integration with the Bodhi Browser Extension.