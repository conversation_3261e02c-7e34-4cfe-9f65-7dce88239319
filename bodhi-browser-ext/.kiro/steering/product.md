# Product Overview

Bodhi Browser Extension is a secure Chrome extension that provides a bridge between web pages and locally running LLM services. It's part of the Bodhi Browser project and works in conjunction with the @bodhiapp/bodhijs library.

## Core Features

- Secure communication bridge between web pages and local LLM services
- OpenAI-compatible API for chat completions
- Support for multiple LLM models
- Built-in security measures to prevent unauthorized access
- Zero-dependency core for minimal attack surface

## Architecture

The extension uses a three-component architecture:
- `background.js`: Handles communication with local LLM services
- `content.js`: Manages communication between web pages and the background script
- `inject.js`: Provides the interface for web pages to interact with the extension

## Security Focus

Security is a primary concern with multiple layers of protection:
- Content Security Policy (CSP) to prevent XSS attacks
- Message validation to ensure only authorized origins can access LLM services
- Minimal permissions model
- No external dependencies in core functionality
- Regular security audits and updates