# Technology Stack

## Core Technologies

- **TypeScript**: Primary language for type safety and development experience
- **Next.js 15.2.4**: React framework for the UI components with static export
- **React 19**: UI library for the extension popup and settings
- **Chrome Extension Manifest v3**: Modern extension platform
- **Webpack 5**: Bundler for extension scripts (background, content, inject)

## Build System

- **Next.js**: Handles UI build with static export (`output: 'export'`)
- **Webpack**: Separate build for extension scripts
- **TypeScript Compiler**: Type checking and compilation
- **Webpack Obfuscator**: Code obfuscation for production builds

## Testing & Quality

- **Vitest**: Primary test runner with Node.js environment
- **Playwright**: Browser automation and E2E testing
- **ESLint**: Code linting with TypeScript and Prettier integration
- **Prettier**: Code formatting with specific style rules

## Development Tools

- **tsx**: TypeScript execution for scripts
- **rimraf**: Cross-platform file cleanup
- **cross-env**: Cross-platform environment variables

## Common Commands

```bash
# Setup and installation
npm ci                    # Install exact dependencies
make setup               # Full setup with llama binaries

# Development builds
npm run build            # Build both UI and extension
npm run build:ui         # Build Next.js UI only
npm run build:ext        # Build extension scripts only

# Production builds
npm run build:release    # Production build with obfuscation
make ci.release-build    # CI production build with zip

# Testing
npm test                 # Run all tests
npm run test:brave       # Test with Brave browser
npm run test:edge        # Test with Edge browser
npm run test:ci          # CI test mode

# Code quality
npm run lint             # Run ESLint checks
npm run lint:fix         # Auto-fix ESLint issues
make validate            # Full validation (lint + format)

# Utilities
npm run clean            # Clean build artifacts
```

## Code Style

- **Prettier configuration**: Single quotes, 100 char width, 2-space tabs, trailing commas
- **ESLint rules**: TypeScript recommended + Next.js core web vitals
- **Import style**: ES modules throughout
- **File extensions**: `.ts` for TypeScript, `.tsx` for React components