# Project Structure

## Root Level Organization

```
bodhi-browser/
├── src/                 # Next.js UI components and pages
├── src-ext/             # Chrome extension scripts
├── tests/               # Test files and test utilities
├── public/              # Static assets and manifest
├── dist/                # Build output (generated)
├── plans/               # Project planning documents
└── scripts/             # Build and utility scripts
```

## Source Code Structure

### UI Components (`src/`)
- `src/components/Settings/` - Settings UI components
- `src/pages/` - Next.js pages (_app.tsx, _document.tsx, index.tsx)
- `src/styles/` - CSS modules and global styles

### Extension Scripts (`src-ext/`)
- `background.ts` - Service worker for extension background tasks
- `content.ts` - Content script injected into web pages
- `inject.ts` - Script providing web page interface
- `shared/` - Shared utilities, types, and constants
- `webpack.config.js` - Webpack configuration for extension build
- `tsconfig.json` - TypeScript config specific to extension

### Testing (`tests/`)
- `*.test.ts` - Test files using Vitest
- `test-helpers.ts` - Shared test utilities
- `setup.ts` - Test environment setup
- `test-app-oauth/` - OAuth testing application
- `test-extension/` - Test extension for development
- `test-page/` - Static HTML pages for testing

## Build Outputs

### Development Build
- `dist/` - Combined output directory
  - Next.js static export files (HTML, CSS, JS)
  - Extension scripts (background.js, content.js, inject.js)
  - manifest.json (copied from public/)
  - Static assets and icons

### Key Configuration Files

- `package.json` - Dependencies and npm scripts
- `tsconfig.json` - Main TypeScript configuration
- `next.config.ts` - Next.js build configuration with obfuscation
- `vitest.config.js` - Test runner configuration
- `playwright.config.ts` - Browser automation configuration
- `eslint.config.js` - Linting rules and configuration
- `Makefile` - Build automation and common tasks

## File Naming Conventions

- **TypeScript files**: `.ts` extension
- **React components**: `.tsx` extension
- **Test files**: `.test.ts` or `.test.tsx`
- **Configuration files**: Use appropriate extensions (.js, .ts, .json)
- **CSS modules**: `.module.css` suffix

## Import Patterns

- Use ES modules (`import/export`) throughout
- Path aliases: `@/*` maps to `./src/*`
- Relative imports for local files
- Absolute imports for external dependencies