# bodhi-browser-ext Makefile
# This Makefile provides commands to build and test the Chrome extension

.PHONY: all clean build build-fast test ci.test setup install lint format validate release help test-brave

# Default target - builds and tests everything
all: setup lint-fix validate build test ## Default target, builds and tests everything

# Clean all build artifacts
clean: ## Clean all build artifacts
	npm run clean
	@echo "bodhi-browser-ext build artifacts cleaned"

# Setup - install dependencies
install: ## Install dependencies (using npm install)
	@echo "Installing dependencies for bodhi-browser-ext..."
	npm install
	@echo "bodhi-browser-ext dependencies installed successfully"

# Setup - install dependencies with exact versions
setup: ## Install dependencies with exact versions (using npm ci)
	@echo "Installing dependencies for bodhi-browser-ext..."
	npm ci
	@echo "Downloading llama binaries..."
	npm run download-llama-binaries
	@echo "bodhi-browser-ext dependencies installed successfully"

# Build the extension
build: ## Build browser extension
	npm run build
	@echo "bodhi-browser-ext built successfully"

# Fast build - only if sources changed
build-fast: ## Fast build browser extension (only if sources changed)
	npm run build:fast
	@echo "bodhi-browser-ext fast build completed"

# CI release build - always use production mode for webpack
ci.release-build: ## Build browser extension in production mode for CI/release
	npm run build:release
	cd dist && zip -r ../bodhi-browser-ext.zip * && cd -
	@echo "bodhi-browser-ext CI release build (production mode) completed"

# Test against production build
ci.test: clean
	$(MAKE) ci.release-build
	npm run test:ci
	@echo "bodhi-browser-ext tests (against production build) completed successfully"

# Run tests
test: build ## Test browser extension
	npm test
	@echo "bodhi-browser-ext tests completed successfully"

# Test with Brave browser
test-brave: ## Test browser extension with Brave browser
	npm run test:brave
	@echo "bodhi-browser-ext tests with Brave completed successfully"

# Run ESLint checks
lint: ## Run ESLint checks
	@echo "Running ESLint checks..."
	npm run lint
	@echo "ESLint checks completed"

# Fix ESLint issues automatically where possible
lint-fix: ## Fix ESLint and formatting issues automatically
	@echo "Fixing ESLint and formatting issues..."
	npm run lint:fix
	@echo "ESLint and formatting fixes completed"

# Validate code (format and lint)
validate: ## Run browser extension validation (format and lint)
	@echo "Validating bodhi-browser-ext..."
	npm run lint
	@echo "bodhi-browser-ext validation completed successfully"

# Function to check git branch status
define check_git_branch
	@CURRENT_BRANCH=$$(git branch --show-current) && \
	if [ "$$CURRENT_BRANCH" != "main" ]; then \
		read -p "Warning: You are not on main branch (current: $$CURRENT_BRANCH). Continue? [y/N] " confirm && \
		if [ "$$confirm" != "y" ]; then \
			echo "Aborting release." && exit 1; \
		fi \
	fi && \
	echo "Fetching latest changes from remote..." && \
	git fetch origin main && \
	LOCAL_HEAD=$$(git rev-parse HEAD) && \
	REMOTE_HEAD=$$(git rev-parse origin/main) && \
	if [ "$$LOCAL_HEAD" != "$$REMOTE_HEAD" ]; then \
		echo "Warning: Your local main branch is different from origin/main" && \
		echo "Local:  $$LOCAL_HEAD" && \
		echo "Remote: $$REMOTE_HEAD" && \
		read -p "Continue anyway? [y/N] " confirm && \
		if [ "$$confirm" != "y" ]; then \
			echo "Aborting release." && exit 1; \
		fi \
	fi
endef

# Function to safely delete existing tag
define delete_tag_if_exists
	echo "Checking for existing tag $(1)..." && \
	if git rev-parse "$(1)" >/dev/null 2>&1; then \
		read -p "Tag $(1) already exists. Delete and recreate? [y/N] " confirm && \
		if [ "$$confirm" = "y" ]; then \
			echo "Deleting existing tag $(1)..." && \
			git tag -d "$(1)" 2>/dev/null || true && \
			git push --delete origin "$(1)" 2>/dev/null || true; \
		else \
			echo "Aborting release." && exit 1; \
		fi \
	fi
endef

# Release browser extension
release: ## Create and push tag for Chrome extension release
	@echo "Preparing to release Chrome extension..."
	$(call check_git_branch)
	@CURRENT_VERSION=$$(node -p "require('./manifest.json').version") && \
	IFS='.' read -r MAJOR MINOR PATCH <<< "$$CURRENT_VERSION" && \
	NEXT_VERSION="$$MAJOR.$$MINOR.$$((PATCH + 1))" && \
	echo "Current version in manifest.json: $$CURRENT_VERSION" && \
	echo "Next version to release: $$NEXT_VERSION" && \
	TAG_NAME="bodhi-browser-ext/v$$NEXT_VERSION" && \
	$(call delete_tag_if_exists,$$TAG_NAME) && \
	echo "Creating tag $$TAG_NAME..." && \
	git tag "$$TAG_NAME" && \
	git push origin "$$TAG_NAME" && \
	echo "Tag $$TAG_NAME pushed. GitHub workflow will handle the release process."

.DEFAULT_GOAL := help
.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z0-9._-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) 