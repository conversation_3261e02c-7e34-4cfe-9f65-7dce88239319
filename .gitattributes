# Set default behavior to automatically normalize line endings
* text=auto eol=lf

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.html text eol=lf
*.mjs text eol=lf
*.cjs text eol=lf
*.yml text eol=lf
*.yaml text eol=lf

# Declare files that will always have CRLF line endings on checkout
*.bat text eol=crlf
*.cmd text eol=crlf

# Declare files that will always have LF line endings on checkout
*.sh text eol=lf 