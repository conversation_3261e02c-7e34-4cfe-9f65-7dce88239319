# Implementation Plan

## Task Overview

This implementation plan follows a test-driven development approach to update the bodhi-js library. We create a new React test application (bodhijs-test-app-react) and comprehensive integration tests from scratch, heavily inspired by bodhi-browser-ext/tests/test-app-oauth. The library will be updated to follow the proven libbodhiext.ts patterns.

## Task: Project Restructuring and Legacy Migration

- [x] task-move-to-legacy: Move existing tests to legacy directory
  - Create bodhi-js-legacy-test directory at project root
  - Move existing bodhi-js/tests directory to bodhi-js-legacy-test/tests
  - Move existing bodhi-js/tests/test-app-react to bodhi-js-legacy-test/test-app-react
  - Update .gitignore to include bodhi-js-legacy-test if needed
  - Verify legacy tests are preserved but not maintained or referenced
  - _Files: bodhi-js-legacy-test/, .gitignore_
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

## Task: Test Infrastructure Setup

- [x] task-setup-test-infrastructure: Create comprehensive test infrastructure
  - Create bodhi-js/tests directory structure
  - Create .env.test file with updated test configuration (INTEG*TEST*\* variables)
  - Create vitest setup.ts to read .env.test and create global constants
  - Create tests/test-utils/browser-manager.ts for Playwright browser management with extension loading
  - Create tests/test-utils/bodhi-app-server-manager.ts for Bodhi server management with app-bindings
  - Create tests/test-utils/test-app-manager.ts for test app management on port 12345
  - Implement parameterized service managers with no direct .env.test knowledge
  - Implement service lifecycle management with spawn/exec attached to parent process
  - Add stdout/stderr piping to parent process for all services
  - Add health checks for all services (/ping for server, /index.html for test app)
  - Enforce port 12345 requirement for test app or fail with clear error (configurable but defaults to 12345)
  - _Files: bodhi-js/tests/test-utils/, bodhi-js/.env.test, bodhi-js/tests/setup.ts_
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 12.1, 12.2, 12.3, 12.4, 12.5_

## Task: Library Architecture Update

- [x] task-update-library-architecture: Update bodhi-js library to follow libbodhiext.ts patterns
  - Study bodhi-browser-ext/tests/test-app-oauth/src/libbodhiext.ts structure
  - Replace function-based exports with BodhiClient class in src/index.ts
  - Create BodhiClient class with constructor(extensionId: string)
  - Implement loadBodhiClient(timeout?: number) factory function
  - Add getExtensionId(), sendApiRequest(), sendStreamRequest(), ping() methods
  - Implement ExtensionNotFoundError and ExtensionTimeoutError classes with parameterized values preserved, e.g. timeout value
  - Add private ensureExtensionAvailable() method
  - Update TypeScript types to match new architecture
  - Remove all function-based exports (no backwards compatibility)
  - _Files: bodhi-js/src/index.ts, bodhi-js/src/core.ts, bodhi-js/src/types.ts_
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

## Task: Extension Detection Test Application

- [x] task-create-test-app: Create React test application with extension detection
  - Create bodhijs-test-app-react directory with React + Vite + React Router setup
  - Create package.json with React, Vite, React Router dependencies
  - Create vite.config.ts with proper configuration (copy index.html as 404.html for path support)
  - Create blank.html page in public directory for extension loading (`<html><body></body></html>`)
  - Create src/App.tsx with routing setup
  - Create src/pages/LandingPage.tsx with extension detection display
  - Add @bodhiapp/bodhijs library with relative path to project `"@bodhiapp/bodhijs": "file:../../",`
  - Implement extension detection using loadBodhiClient() from updated library (only bodhi-js interacts with window.bodhiext)
  - Display extension status (detecting, detected with ID, timeout, error)
  - Add proper error handling for ExtensionNotFoundError and ExtensionTimeoutError
  - Style with basic CSS for clear status display
  - _Files: bodhijs-test-app-react/src/, bodhijs-test-app-react/package.json, bodhijs-test-app-react/vite.config.ts, bodhijs-test-app-react/public/blank.html_
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

## Task: Extension Detection Integration Tests

- [x] task-test-extension-detection: Create integration tests for extension detection
  - Once per test session/run, do the following:
    - Do `npm run build:fast` on bodhi-browser-ext project, find path using \_\_dirname and relative path
    - Do `npm run build` on bodhijs-test-app-react, find path using \_\_dirname and relative path
    - Start test app server in test setup on port 12345, tear down ensuring port is no longer in use
    - Start bodhi app server in test setup on random port, stop server ensuring port is no longer in use
  - Create tests/extension-detection-with-extension.test.ts for extension installed scenarios
  - Create tests/extension-detection-without-extension.test.ts for extension not installed scenarios
  - Update browser-manager.ts to properly configure extension with server URL
  - Navigate to landing page `/` of test app for testing extension detection (no blank.html needed)
  - Get actual extension ID from window.bodhiext.extension_id
  - Navigate to chrome-extension://{extension_id}/index.html for configuration
  - Configure extension with Bodhi server URL using proper test selectors
  - If cannot find extension ID in configureExtensionSettings, fail the test
  - Have browser manager initialized in beforeAll, torn down in afterAll
  - Test extension detection with browser extension loaded
  - Test extension detection timeout with browser without extension (separate test file)
  - Verify proper error handling and status display in UI
  - Test extension ID retrieval and caching
  - Use Playwright to interact with test app UI and verify extension detection behavior (no code injection)
  - Verify ExtensionNotFoundError is properly handled when extension not installed
  - Always have global timeouts: vitest 30s, playwright 3s for element finding, no inline timeouts
  - Run in headless mode if CI env variable is set, otherwise launch browser
  - Remove try-catch and if-else from tests, throw errors to fail tests with clear messages
  - Reduce console logging, only for error scenarios
  - _Files: bodhi-js/tests/extension-detection-with-extension.test.ts, bodhi-js/tests/extension-detection-without-extension.test.ts, bodhi-js/tests/test-utils/browser-manager.ts_
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

## Task: OAuth Flow Test Application

- [x] task-implement-oauth-flow: Implement OAuth authentication flow in test application
  - Create src/pages/LoginPage.tsx with login/logout UI
  - Create src/pages/CallbackPage.tsx for OAuth callback handling (atomic code-to-token exchange to prevent React re-render issues)
  - Create src/utils/oauth.ts with OAuth manager (similar to bodhi-browser-ext patterns)
  - Implement PKCE flow with request-access using BodhiClient.sendApiRequest()
  - Add authorization URL construction and token exchange
  - Implement token storage in localStorage and authentication state management
  - Add user info display and logout functionality
  - Update App.tsx with authentication state and routing
  - _Files: bodhijs-test-app-react/src/pages/, bodhijs-test-app-react/src/utils/oauth.ts_
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

## Task: OAuth Flow Integration Tests

- [x] task-test-oauth-flow: Create comprehensive OAuth integration tests
  - Create tests/oauth-flow.test.ts using established test infrastructure
  - Use global test app URL and browser manager with extension loaded
  - Use Playwright to click login button and complete OAuth flow
  - Enter test credentials and handle OAuth server interaction
  - Verify redirect to callback page and token exchange
  - Test return to landing page with authenticated state
  - Verify logout functionality and token cleanup
  - Test OAuth flow error scenarios and proper error handling
  - _Files: bodhi-js/tests/oauth-flow.test.ts_
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

## Task: API Testing Interface, Streaming Support, and Integration Tests

- [x] task-create-api-testing-functionality: Create comprehensive API testing page with streaming support and integration tests
  - Create src/pages/ApiTestPage.tsx similar to bodhi-browser-ext test-app-oauth
  - Add form with method selector, endpoint input, headers textarea, body textarea
  - Add authentication toggle checkbox for including/excluding auth headers
  - Add streaming toggle checkbox for streaming/non-streaming requests
  - Add API status indicators (ready, calling, streaming, completed, error)
  - Add response display section with status, headers, body formatting
  - Add real-time streaming content display area with progressive content updates
  - Add error display with detailed error information
  - Add clear/reset functionality for form and responses
  - Update routing to include API test page
  - Implement streaming support with sendStreamRequest method returning AsyncIterable
  - Add streaming status indicators and proper stream lifecycle management
  - Handle streaming errors and cleanup properly
  - Create tests/api-testing.test.ts using established test infrastructure (authenticated scenarios)
  - Create tests/api-testing-unauthenticated.test.ts for unauthenticated scenarios
  - Use global test app URL and browser manager with extension loaded
  - Complete OAuth flow and navigate to API test page for authenticated tests
  - Test authenticated requests to /v1/chat/completions endpoint
  - Test non-authenticated requests to /ping endpoint
  - Test authentication error scenarios (calling chat API without auth)
  - Test streaming chat completions with real-time content updates
  - Verify stream lifecycle (start, data chunks, completion)
  - Test streaming error handling and recovery
  - Verify proper response formatting and status display
  - Test API request error handling and error display
  - Test custom headers functionality
  - Test clear/reset functionality
  - _Files: bodhijs-test-app-react/src/pages/ApiTestPage.tsx, bodhi-js/tests/api-testing.test.ts, bodhi-js/tests/api-testing-unauthenticated.test.ts_
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 9.1, 9.2, 9.3, 9.4, 9.5, 10.1, 10.2, 10.3, 10.4, 10.5_

## Task: Error Scenario Testing

- [ ] task-test-error-scenarios: Implement comprehensive error scenario testing
  - Update test infrastructure to support error scenarios (browser without extension, server unavailable)
  - Create tests/error-scenarios.test.ts for comprehensive error testing
  - Test extension not installed scenarios with proper error display
  - Test server unavailable scenarios (extension present, server down)
  - Test authentication errors and proper error handling
  - Test API errors and streaming errors with proper error propagation
  - Update test application error handling and user feedback
  - _Files: bodhi-js/tests/error-scenarios.test.ts, bodhijs-test-app-react/src/pages/_
  - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5_

## Task: TypeScript Integration and Documentation

- [x] task-finalize-typescript-docs: Ensure comprehensive TypeScript support and documentation
  - Verify all library exports have proper TypeScript types
  - Add comprehensive JSDoc comments to all public methods
  - Update bodhi-js/README.md with new BodhiClient API documentation
  - Add usage examples for extension detection, API requests, and streaming
  - Document error handling patterns and best practices
  - thoroughly analyze ai-docs/context, specially ai-docs/context/bodhi-js.md folder and update for the changes in this task
  - _Files: bodhi-js/src/, bodhi-js/README.md_
  - _Requirements: 13.1, 13.2, 13.3, 13.4, 13.5, 14.1, 14.2, 14.3, 14.4, 14.5_

## Testing Requirements

After each task:

1. **Build the library**: Run `npm run build` in bodhi-js directory
2. **Build the test app**: Run `npm run build` in bodhijs-test-app-react directory
3. **Run integration tests**: Execute `npm test` in bodhi-js directory to run Playwright tests
4. **Manual verification**: Start test app using test infrastructure and verify functionality
5. **Test error scenarios**: Verify proper error handling for all failure cases
6. **All tests must pass** before proceeding to the next task

Each task is designed to be:

- **Self-contained**: Can be completed and tested independently
- **Incremental**: Builds on previous tasks
- **Testable**: Has clear success criteria and test coverage
- **Functional**: Produces working software at each step

## Key Design Decisions

- **Complete legacy separation**: Move all existing code to bodhi-js-legacy-test
- **New React test app**: Create bodhijs-test-app-react from scratch with React + Vite + React Router
- **Library follows libbodhiext.ts patterns**: Proven architecture from bodhi-browser-ext
- **Comprehensive test infrastructure**: Service managers for browser, server, and test app
- **Test-driven development**: Integration tests for each feature
- **Port 12345 requirement**: Test app must run on port 12345 or fail
- **No backwards compatibility**: Complete API redesign with BodhiClient interface
