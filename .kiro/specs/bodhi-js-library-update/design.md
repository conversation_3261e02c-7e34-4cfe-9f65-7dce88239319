# Design Document

## Overview

This design document outlines the technical approach for updating the bodhi-js library to achieve feature parity with the bodhi-browser-ext project. The design follows a test-driven development approach where we create a new React test application (bodhijs-test-app-react) and comprehensive integration tests from scratch, heavily inspired by bodhi-browser-ext/tests/test-app-oauth.

The core architectural shift moves from direct window.bodhiext usage to a client-based abstraction modeled after bodhi-browser-ext/tests/test-app-oauth/src/libbodhiext.ts, providing better encapsulation, improved error handling, and enhanced developer experience while maintaining full compatibility with the browser extension's capabilities.

**Key Design Decisions**:
- Complete separation of legacy code by moving existing tests to bodhi-js-legacy-test
- New React test application built with React + Vite + React Router
- Comprehensive test infrastructure with service managers for browser, server, and test app
- Library structure modeled after proven libbodhiext.ts patterns
- Test-driven development with integration tests for each feature

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Test Infrastructure"
        TI1[browser-manager.ts] --> TI2[Playwright + Extension]
        TI3[bodhi-app-server-manager.ts] --> TI4[Bodhi Server + app-bindings]
        TI5[test-app-manager.ts] --> TI6[Test App on port 12345]
        TI7[.env.test] --> TI1
        TI7 --> TI3
        TI7 --> TI5
    end
    
    subgraph "React Test App (bodhijs-test-app-react)"
        A[Landing Page] --> B[Extension Detection]
        A --> C[Login/Logout UI]
        C --> D[OAuth Callback Page]
        D --> E[API Test Page]
        E --> F[Streaming Interface]
    end
    
    subgraph "Updated Bodhi-JS Library"
        G[BodhiClient] --> H[Extension Detection]
        G --> J[API Methods]
        G --> K[Streaming Support]
        G --> L[Error Handling]
    end
    
    subgraph "Browser Extension"
        M[window.bodhiext]
        N[sendApiRequest]
        O[sendStreamRequest]
        P[getExtensionId]
    end
    
    subgraph "Legacy Code"
        Q[bodhi-js-legacy-test/]
        R[Old tests & test-app-react]
    end
    
    A --> G
    G --> M
    J --> N
    K --> O
    H --> P
    TI2 --> M
    TI4 --> N
    TI6 --> A
```

### Component Architecture

The system is organized into four main layers:

1. **Test Infrastructure Layer**: Service managers for browser, server, and test app with configuration management
2. **Presentation Layer**: New React test application (bodhijs-test-app-react) with routing and UI components
3. **Library Layer**: Updated BodhiClient modeled after libbodhiext.ts patterns
4. **Extension Layer**: Browser extension interface (window.bodhiext)
5. **Legacy Layer**: Archived code in bodhi-js-legacy-test for reference

## Components and Interfaces

### Test Infrastructure Components

#### Service Managers

**browser-manager.ts**
- Manages Playwright browser context with extension loading and no-extension scenarios
- Loads bodhi-browser-ext/dist extension into browser when extensionPath provided
- Supports extensionPath: null for testing extension not found scenarios
- Uses launchPersistentContext with embedded Playwright config settings
- Configures extension settings with Bodhi server URL when extension is loaded
- Provides browser instance for integration tests with proper lifecycle management
- Handles both CI (headless) and local (visible) browser modes

**bodhi-app-server-manager.ts**
- Launches Bodhi server with app-bindings on random port
- Waits for /ping endpoint to return 200 OK
- Spawns/exec servers attached to parent process for automatic cleanup
- Pipes server stdout/stderr to parent process
- Manages server lifecycle and cleanup
- Parameterized - no direct .env.test knowledge

**test-app-manager.ts**
- Launches test app using npx serve on port 12345 with automatic port clearing
- Includes buildApp() method for building test app using build:fast command
- Automatically kills processes using port 12345 before starting (cross-platform)
- Waits for /index.html to return 200 OK with configurable timeout
- Spawns/exec servers attached to parent process for automatic cleanup
- Pipes serve stdout/stderr to parent process for debugging
- Manages complete test app lifecycle including build, start, and cleanup

#### Configuration Management

**Test Setup Architecture**
- .env.test file read by vitest setup.ts and created as global constants
- Vitest lifecycle hooks (beforeAll/afterAll) in setup.ts manage session-wide resources
- Extension and test app built once per session using build:fast commands for efficiency
- Global test app URL shared via globalThis.TEST_APP_URL across all tests
- Browser manager supports both extension-loaded and no-extension scenarios (extensionPath: null)
- Test app manager includes robust port clearing and automatic process cleanup
- Only bodhi-js library interacts with window.bodhiext (no test-app or test-utils interaction)
- Integration tests interact only with UI components using Playwright selectors

**Environment Configuration (.env.test)**
```
INTEG_TEST_AUTH_URL=https://main-id.getbodhi.app
INTEG_TEST_AUTH_REALM=bodhi
INTEG_TEST_CLIENT_ID=resource-a80c41c4-9d5b-4054-8322-689fe0ecc659
INTEG_TEST_CLIENT_SECRET=GiIui3Qlwmwow20oVZ67UqAf82pbp0At
INTEG_TEST_USERNAME=<EMAIL>
INTEG_TEST_PASSWORD=pass

INTEG_TEST_APP_CLIENT_ID=app-a05c53c5-3fc4-409d-833d-f4acc90e1611
```

### New React Test Application Components (bodhijs-test-app-react)

#### Core Components

**App Component**
- Manages global application state
- Handles routing between pages
- Provides error boundary functionality
- Maintains authentication state across navigation

**Navigation Component**
- Displays consistent navigation bar
- Shows extension detection status
- Displays user authentication state
- Provides logout functionality

**Landing Page Component**
- Shows extension detection status using BodhiClient
- Displays login button when unauthenticated
- Shows user information when authenticated
- Handles OAuth flow initiation using manual URL construction

**OAuth Callback Component**
- Processes OAuth authorization code using BodhiClient.sendApiRequest()
- Handles code-to-token exchange atomically and only once (prevents React re-render issues)
- Exchanges code for access token via API calls
- Stores token securely in localStorage/sessionStorage
- Redirects to landing page with authenticated state

**API Test Page Component**
- Provides comprehensive API request form similar to bodhi-browser-ext test-app-oauth
- Supports authenticated/non-authenticated modes via checkbox
- Handles streaming/non-streaming requests via checkbox
- Displays real-time results and streaming content
- Shows detailed error information and status indicators

**Build Configuration**
- React app build copies index.html as 404.html for path support (/callback routes)
- Enables proper routing support when serving with npx serve

#### State Management

```typescript
interface AppState {
  extensionStatus: 'detecting' | 'ready' | 'error' | 'not-found';
  authState: 'unauthenticated' | 'authenticating' | 'authenticated' | 'error';
  apiStatus: 'ready' | 'calling' | 'streaming' | 'completed' | 'error';
  userInfo: UserInfo | null;
  error: string | null;
}

interface UserInfo {
  email: string;
  role: string;
  tokenType: string;
  loggedIn: boolean;
}
```

### Updated Library Implementation

#### BodhiClient Class

The main interface for the library, modeled after bodhi-browser-ext/tests/test-app-oauth/src/libbodhiext.ts patterns. This provides proven, battle-tested architecture with proper encapsulation and error handling.

```typescript
class BodhiClient {
  private extensionId: string;
  
  constructor(extensionId: string) {
    this.extensionId = extensionId;
  }
  
  // Core methods (similar to libbodhiext.ts)
  getExtensionId(): string
  async sendApiRequest(method: string, endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse>
  async sendStreamRequest(method: string, endpoint: string, body?: any, headers?: Record<string, string>): Promise<AsyncIterable<StreamChunk>>
  async ping(): Promise<{ message: string }>
  
  // Private methods
  private ensureExtensionAvailable(): void
}

// Factory function (similar to loadExtensionClient)
async function loadBodhiClient(timeout: number = 10000): Promise<BodhiClient>

// Error classes with original values preserved
class ExtensionNotFoundError extends Error {
  constructor(public timeout: number) {
    super(`Bodhi extension not detected within ${timeout}ms`);
  }
}

class ExtensionTimeoutError extends Error {
  constructor(public timeout: number) {
    super(`Timeout fetching extension ID within ${timeout}ms`);
  }
}
```

#### Extension Detection System

**LoadBodhiClient Function**
- Polls for window.bodhiext availability
- Listens for 'bodhiext:initialized' event
- Implements configurable timeout
- Returns initialized BodhiClient instance

**Status Management**
- Tracks extension detection state
- Provides detailed error information on failures
- Simple boolean status checking without event listeners

#### OAuth Integration (Test App Level)

OAuth functionality is implemented at the test application level, similar to the bodhi-browser-ext test-app-oauth approach. The test app will:

- Use BodhiClient.sendApiRequest() for request-access calls to /bodhi/v1/auth/request-access
- Handle PKCE flow generation and authorization URL construction
- Manage token exchange via direct fetch calls to OAuth server
- Store tokens in localStorage and include authentication headers in API requests
- Provide comprehensive OAuth flow testing and validation

This approach follows the proven pattern from bodhi-browser-ext and allows comprehensive end-to-end OAuth testing.

#### API Request System

**Generic API Methods**
- sendApiRequest: Standard HTTP requests
- sendStreamRequest: Streaming requests returning AsyncIterable
- Consistent error handling across all methods
- Automatic authentication header injection

**Chat Completion Integration**
- Supports both streaming and non-streaming modes
- Returns appropriate types based on stream parameter
- Maintains compatibility with OpenAI-style API

#### Streaming Implementation

**Stream Processing**
- AsyncIterable interface for streaming responses
- Proper chunk parsing and yielding
- Error propagation through stream
- Cleanup on stream completion or error

```typescript
async function* processStream(
  streamResponse: AsyncIterable<StreamChunk>
): AsyncIterable<ChatChunk> {
  try {
    for await (const chunk of streamResponse) {
      yield parseStreamChunk(chunk);
    }
  } catch (error) {
    throw new BodhiError('Stream processing failed', 'STREAM_ERROR', 0, error);
  }
}
```

## Data Models

### Core Types

```typescript
// API Request/Response Types
interface ApiRequestOptions {
  method: string;
  endpoint: string;
  body?: any;
  headers?: Record<string, string>;
  authenticated?: boolean;
}

interface ApiResponse {
  body: any;
  headers: Record<string, string>;
  status: number;
}

interface StreamChunk {
  body: any;
  headers?: Record<string, string>;
  status?: number;
}

// Chat Completion Types
interface ChatRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatChoice[];
  usage: TokenUsage;
}

interface ChatChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatChoiceDelta[];
}

// OAuth Types
interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  scope: string;
}

// Extension Types
interface ExtensionState {
  status: 'detecting' | 'ready' | 'error' | 'not-found' | 'timeout';
  isInstalled: boolean;
  error?: string;
}
```

### Error Types

```typescript
enum ErrorCode {
  EXTENSION_NOT_INSTALLED = 'EXTENSION_NOT_INSTALLED',
  EXTENSION_TIMEOUT = 'EXTENSION_TIMEOUT',
  SERVER_UNAVAILABLE = 'SERVER_UNAVAILABLE',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  STREAM_ERROR = 'STREAM_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

class BodhiError extends Error {
  constructor(
    message: string,
    public code: ErrorCode,
    public status?: number,
    public data?: any
  ) {
    super(message);
    this.name = 'BodhiError';
  }
}

class ExtensionNotFoundError extends BodhiError {
  constructor(timeout: number) {
    super(
      `Bodhi extension not detected within ${timeout}ms`,
      ErrorCode.EXTENSION_NOT_INSTALLED
    );
  }
}

class ExtensionTimeoutError extends BodhiError {
  constructor(timeout: number) {
    super(
      `Timeout fetching extension ID within ${timeout}ms`,
      ErrorCode.EXTENSION_TIMEOUT
    );
  }
}
```

## Error Handling

### Error Hierarchy

The system implements a comprehensive error handling strategy with specific error types for different failure scenarios:

1. **Extension Errors**: Detection failures, timeouts, unavailability
2. **Authentication Errors**: OAuth failures, token issues, access denied
3. **API Errors**: Server errors, network failures, invalid requests
4. **Streaming Errors**: Stream interruption, parsing failures, cleanup issues

### Error Propagation

- **Synchronous Methods**: Throw BodhiError instances
- **Asynchronous Methods**: Reject promises with BodhiError instances
- **Streaming Methods**: Propagate errors through AsyncIterable
- **React Components**: Use error boundaries and state management

### Error Recovery

- **Extension Detection**: Retry with exponential backoff
- **API Requests**: Provide retry mechanisms for transient failures
- **Authentication**: Clear invalid tokens and prompt re-authentication
- **Streaming**: Graceful degradation to non-streaming mode when possible

## Testing Strategy

### Integration Testing Approach

#### Test Environment Setup

**Service Management Architecture**
- Use browser-manager.ts to launch Playwright with bodhi-browser-ext/dist extension loaded
- Use bodhi-app-server-manager.ts to launch Bodhi server with app-bindings on random port
- Use test-app-manager.ts to launch test app on port 12345 using npx serve
- All services spawn/exec attached to parent process for automatic cleanup on parent death
- All services pipe stdout/stderr to parent process for debugging
- Health checks ensure services are ready before tests begin
- Extension settings configured with Bodhi server URL in beforeAll of each test

**Configuration Management**
- Read all test configuration from .env.test file in vitest setup.ts
- Create global constants accessible by test files' beforeAll
- Pass configuration as parameters to test utility managers
- Support app client id, resource client credentials, auth URL configuration
- Include test user credentials for OAuth flow testing
- Enforce port 12345 requirement for test app or fail with clear error

**Library Behavior Testing via Test App**
- Use Playwright to interact with React test app and validate BodhiClient behavior
- Test extension detection, OAuth flow, API requests, and streaming through UI
- Focus on end-to-end library functionality rather than React component testing
- Verify error handling and recovery scenarios

#### Test Categories

**OAuth Flow Tests**
- Complete login flow with real authentication server
- Token exchange and storage verification
- Error handling for authentication failures
- Cross-page state persistence

**API Request Tests**
- Standard HTTP methods (GET, POST, etc.)
- Authenticated vs non-authenticated requests
- Error scenarios (invalid endpoints, server errors)
- Response parsing and validation

**Streaming Tests**
- Real-time streaming chat completions
- Stream lifecycle management (start, data, complete)
- Error handling during streaming
- Concurrent stream management

**Library Behavior Tests (via React Test App)**
- BodhiClient functionality validation through UI interactions
- Extension detection and initialization behavior
- API request and streaming functionality
- Error handling and recovery patterns

#### Test Data Management

**Mock Data Strategy**
- Use real authentication server for OAuth testing
- Test against actual Bodhi server for API validation
- Maintain test user accounts for consistent testing
- Generate realistic chat completion scenarios

**Test Isolation**
- Clean authentication state between tests
- Reset extension state for each test suite
- Manage browser context for consistent testing
- Handle asynchronous operations properly

### Continuous Integration

**Automated Testing Pipeline**
- Run tests against multiple browser versions
- Test with different extension configurations
- Validate against various server configurations
- Performance and reliability testing

**Quality Gates*ut*
- Code coverage requirements
- Integration test pass rates
- Performance benchmarks
- Security vulnerability scanning

## Implementation Considerations

### Performance Optimization

**Extension Detection**
- Efficient polling with exponential backoff
- Event-based detection when available
- Caching of extension ID and status

**API Requests**
- Connection pooling and reuse
- Request/response compression
- Timeout management and retry logic

**Streaming**
- Efficient chunk processing
- Memory management for long streams
- Proper cleanup of resources

### Security Considerations

**Token Management**
- Secure storage of access tokens
- Automatic token refresh when possible
- Clear tokens on logout or error

**API Security**
- Validate all API responses
- Sanitize user inputs
- Prevent injection attacks

**Extension Communication**
- Validate extension presence and authenticity
- Handle malicious or modified extensions
- Secure message passing

### Browser Compatibility

**Extension Support**
- Chrome/Chromium-based browsers
- Extension manifest v3 compatibility
- Graceful degradation when extension unavailable

**React Application**
- Modern browser support (ES2020+)
- TypeScript strict mode compliance
- Responsive design for different screen sizes

### Development Experience

**TypeScript Integration**
- Comprehensive type definitions
- IntelliSense support for all APIs
- Strict type checking and validation

**Documentation**
- Comprehensive API documentation
- Usage examples and tutorials
- Migration guide from direct window.bodhiext usage

**Debugging Support**
- Detailed error messages with context
- Debug logging capabilities
- Development mode features

This design provides a solid foundation for implementing the bodhi-js library update while ensuring maintainability, testability, and excellent developer experience.