# Requirements Document

## Introduction

The bodhi-js library needs to be updated to align with recent changes made in the bodhi-browser-ext project. The browser extension has been enhanced with streaming support, improved error handling, better extension detection, and a more robust API structure. The bodhi-js library currently lacks these capabilities and needs to be updated to provide feature parity and improved developer experience.

This update follows a test-driven development approach where we create a new React test application (bodhijs-test-app-react) and comprehensive integration tests from scratch, heavily inspired by bodhi-browser-ext/tests/test-app-oauth. The existing tests will be moved to bodhi-js-legacy-test for reference.

## Requirements

### Requirement 1: Project Restructuring and Legacy Migration

**User Story:** As a developer maintaining bodhi-js, I want to cleanly separate legacy code from new implementation, so that I can develop the new architecture without interference from existing code.

**Complexity:** Low  
**Area:** Project Structure

#### Acceptance Criteria

1. WHEN restructuring the project THEN the system SHALL move existing bodhi-js/tests directory to bodhi-js-legacy-test
2. WHEN restructuring the project THEN the system SHALL move existing test-app-react to bodhi-js-legacy-test/test-app-react
3. WHEN restructuring the project THEN the system SHALL move existing integration tests to bodhi-js-legacy-test/tests
4. WHEN restructuring the project THEN the system SHALL preserve legacy tests for reference but not maintain them
5. WHEN restructuring the project THEN the system SHALL create clean workspace for new implementation

### Requirement 2: Test Infrastructure Setup

**User Story:** As a developer maintaining bodhi-js, I want comprehensive test infrastructure with proper service management, so that I can run reliable integration tests against real services.

**Complexity:** High  
**Area:** Test Infrastructure

#### Acceptance Criteria

1. WHEN setting up test infrastructure THEN the system SHALL create browser-manager.ts for Playwright browser management with extension loading and no-extension scenarios
2. WHEN setting up test infrastructure THEN the system SHALL create bodhi-app-server-manager.ts for launching Bodhi server with app-bindings on random port
3. WHEN setting up test infrastructure THEN the system SHALL create test-app-manager.ts for launching test app on port 12345 using npx serve with robust port clearing
4. WHEN setting up test infrastructure THEN the system SHALL read test constants from .env.test file in vitest setup.ts and create global constants
5. WHEN setting up test infrastructure THEN the system SHALL use Vitest lifecycle hooks (beforeAll/afterAll) in setup.ts for session management
6. WHEN setting up test infrastructure THEN the system SHALL build extension and test app once per test session using build:fast commands
7. WHEN setting up test infrastructure THEN the system SHALL share test app URL globally via globalThis.TEST_APP_URL
8. WHEN test app launches THEN the system SHALL automatically clear port 12345 and verify availability or fail the test
9. WHEN services start THEN the system SHALL wait for health checks (/ping for server, /index.html for test app) before proceeding
10. WHEN browser manager starts THEN the system SHALL support both extension-loaded and no-extension scenarios for comprehensive testing

### Requirement 3: Library Architecture Update

**User Story:** As a web developer using bodhi-js, I want a library structure similar to bodhi-browser-ext/tests/test-app-oauth/src/libbodhiext.ts, so that I have a consistent and familiar API pattern.

**Complexity:** High  
**Area:** Library Core Architecture

#### Acceptance Criteria

1. WHEN updating library structure THEN the system SHALL model the new bodhi-js after libbodhiext.ts patterns
2. WHEN updating library structure THEN the system SHALL provide BodhiClient class as the main interface
3. WHEN updating library structure THEN the system SHALL implement extension detection with proper error handling
4. WHEN updating library structure THEN the system SHALL provide sendApiRequest and sendStreamRequest methods
5. WHEN updating library structure THEN the system SHALL maintain TypeScript types and error classes with original values preserved
6. WHEN updating library structure THEN the system SHALL ensure only bodhi-js interacts with window.bodhiext (no test-app or test-utils interaction)

### Requirement 4: Extension Detection Test Application

**User Story:** As a developer testing bodhi-js, I want a React test application that detects browser extension presence, so that I can validate extension integration works correctly.

**Complexity:** Medium  
**Area:** Test Application Extension Detection

#### Acceptance Criteria

1. WHEN creating test app THEN the system SHALL use React + Vite + React Router architecture
2. WHEN building test app THEN the system SHALL copy index.html as 404.html for path support (/callback routes)
3. WHEN test app loads THEN the system SHALL display extension detection status on landing page
4. WHEN extension is present THEN the system SHALL show "Extension detected" with extension ID
5. WHEN extension detection times out THEN the system SHALL show "Extension not detected" message
6. WHEN extension detection fails THEN the system SHALL display appropriate error messages

### Requirement 5: Extension Detection Integration Tests

**User Story:** As a developer maintaining bodhi-js, I want integration tests for extension detection scenarios, so that I can ensure the library handles both extension present and absent cases correctly.

**Complexity:** Medium  
**Area:** Integration Testing Extension Detection

#### Acceptance Criteria

1. WHEN running extension detection tests THEN the system SHALL test with browser extension loaded and verify detection succeeds with extension ID display
2. WHEN running extension detection tests THEN the system SHALL test with browser without extension (extensionPath: null) and verify timeout behavior
3. WHEN running extension detection tests THEN the system SHALL interact only with UI components using Playwright selectors (no code injection)
4. WHEN extension is detected THEN the system SHALL verify extension ID is properly retrieved, cached, and matches window.bodhiext.getExtensionId()
5. WHEN extension is not detected THEN the system SHALL verify proper error handling, timeout messages, and retry functionality
6. WHEN extension detection times out THEN the system SHALL verify ExtensionNotFoundError is displayed with appropriate timeout details and installation instructions

### Requirement 6: OAuth Flow Test Application

**User Story:** As a developer testing bodhi-js, I want OAuth authentication flow in the test application, so that I can validate complete authentication integration with real Bodhi server.

**Complexity:** High  
**Area:** Test Application Authentication

#### Acceptance Criteria

1. WHEN user is not authenticated THEN the system SHALL display Login button on landing page
2. WHEN user clicks Login THEN the system SHALL initiate OAuth flow with proper PKCE parameters
3. WHEN OAuth flow completes THEN the system SHALL exchange authorization code for access token atomically and only once
4. WHEN user is authenticated THEN the system SHALL display Logout button and user information
5. WHEN user clicks Logout THEN the system SHALL clear stored tokens and return to unauthenticated state

### Requirement 7: OAuth Flow Integration Tests

**User Story:** As a developer maintaining bodhi-js, I want comprehensive OAuth integration tests using Playwright, so that I can ensure authentication works end-to-end with real authentication server.

**Complexity:** High  
**Area:** Integration Testing Authentication

#### Acceptance Criteria

1. WHEN running OAuth tests THEN the system SHALL use Playwright to click login button and complete authentication flow
2. WHEN running OAuth tests THEN the system SHALL enter username/password credentials from test environment
3. WHEN OAuth flow completes THEN the system SHALL verify user is redirected back to landing page with authenticated state
4. WHEN user is authenticated THEN the system SHALL verify logout button and user information are displayed
5. WHEN testing OAuth THEN the system SHALL verify tokens are properly stored and retrieved across page navigation

### Requirement 8: API Testing Interface

**User Story:** As a developer testing bodhi-js, I want a comprehensive API testing page similar to bodhi-browser-ext test-app-oauth, so that I can validate all API functionality through a user-friendly interface.

**Complexity:** High  
**Area:** Test Application API Testing

#### Acceptance Criteria

1. WHEN on API test page THEN the system SHALL provide form for API requests (method, endpoint, headers, body)
2. WHEN making API calls THEN the system SHALL support both authenticated and non-authenticated modes
3. WHEN making API calls THEN the system SHALL support both streaming and non-streaming modes
4. WHEN API calls complete THEN the system SHALL display results with proper formatting (status, headers, body)
5. WHEN streaming is enabled THEN the system SHALL display real-time streaming content updates
6. WHEN API calls fail THEN the system SHALL display detailed error information

### Requirement 9: API Testing Integration Tests

**User Story:** As a developer maintaining bodhi-js, I want comprehensive API testing integration tests, so that I can ensure all API functionality works correctly with real backend services.

**Complexity:** High  
**Area:** Integration Testing API

#### Acceptance Criteria

1. WHEN testing API functionality THEN the system SHALL complete OAuth flow and navigate to API test page
2. WHEN testing authenticated requests THEN the system SHALL make successful calls to /v1/chat/completions with proper authentication
3. WHEN testing non-authenticated requests THEN the system SHALL make successful calls to /ping without authentication
4. WHEN testing authentication errors THEN the system SHALL verify proper error handling when calling chat API without authentication
5. WHEN testing API responses THEN the system SHALL verify response format, status codes, and content are correct

### Requirement 10: Streaming Support Integration

**User Story:** As a developer testing bodhi-js, I want streaming chat completion support in both test application and integration tests, so that I can validate real-time functionality works correctly.

**Complexity:** High  
**Area:** Integration Testing Streaming

#### Acceptance Criteria

1. WHEN testing streaming THEN the system SHALL complete OAuth flow and navigate to API test page
2. WHEN enabling streaming mode THEN the system SHALL make streaming chat completion requests and display real-time updates
3. WHEN streaming responses arrive THEN the system SHALL verify proper chunk handling and progressive content display
4. WHEN stream completes THEN the system SHALL verify proper stream cleanup and completion handling
5. WHEN streaming errors occur THEN the system SHALL verify proper error propagation and display

### Requirement 11: Error Scenario Testing

**User Story:** As a developer maintaining bodhi-js, I want comprehensive error scenario testing, so that I can ensure the library handles all failure cases gracefully.

**Complexity:** Medium  
**Area:** Integration Testing Error Handling

#### Acceptance Criteria

1. WHEN extension is not installed THEN the system SHALL display appropriate error message and handle ExtensionNotFoundError
2. WHEN server is unavailable THEN the system SHALL display server unavailable error and handle connection failures
3. WHEN authentication fails THEN the system SHALL display authentication error and provide recovery options
4. WHEN API calls fail THEN the system SHALL display detailed error information with proper error codes
5. WHEN streaming fails THEN the system SHALL handle stream errors gracefully and provide appropriate feedback

### Requirement 12: Test Environment Configuration

**User Story:** As a developer maintaining bodhi-js, I want configurable test environment with proper credential management, so that I can run tests against different environments securely.

**Complexity:** Medium  
**Area:** Test Configuration

#### Acceptance Criteria

1. WHEN reading configuration THEN the system SHALL read .env.test in vitest setup.ts and create global constants
2. WHEN configuring tests THEN the system SHALL support app client id, resource client id/secret, auth URL configuration
3. WHEN configuring tests THEN the system SHALL support username/password credentials for OAuth testing
4. WHEN configuring tests THEN the system SHALL support server port configuration and service endpoints
5. WHEN test app starts THEN the system SHALL enforce port 12345 requirement (configurable but defaults to 12345) or fail with clear error message

### Requirement 13: TypeScript Integration and Type Safety

**User Story:** As a web developer using bodhi-js, I want comprehensive TypeScript support with accurate type definitions, so that I have proper IntelliSense and compile-time error checking.

**Complexity:** Medium  
**Area:** Library Type System

#### Acceptance Criteria

1. WHEN using TypeScript THEN the system SHALL provide accurate types for all exported classes and methods
2. WHEN using streaming APIs THEN the system SHALL provide proper AsyncIterable and streaming response types
3. WHEN handling errors THEN the system SHALL provide proper BodhiError type definitions with all error codes
4. WHEN using extension detection THEN the system SHALL provide ExtensionState and status types
5. WHEN building test application THEN the system SHALL ensure all library usage is properly typed

### Requirement 14: Documentation and Migration Guide

**User Story:** As a developer adopting the new bodhi-js library, I want clear documentation and migration guidance, so that I can understand the new API and migrate from the old version.

**Complexity:** Low  
**Area:** Documentation

#### Acceptance Criteria

1. WHEN updating documentation THEN the system SHALL provide comprehensive API documentation for new BodhiClient interface
2. WHEN updating documentation THEN the system SHALL include usage examples for all major functionality
3. WHEN updating documentation THEN the system SHALL provide migration guide from function-based to client-based API
4. WHEN updating documentation THEN the system SHALL document error handling patterns and best practices
5. WHEN updating documentation THEN the system SHALL include streaming usage examples and patterns
