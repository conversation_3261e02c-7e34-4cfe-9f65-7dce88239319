# Implementation Plan

## Overview

This implementation plan converts the approved BodhiPlatform design into actionable coding tasks. Each task follows test-driven development principles with integration testing using the bodhijs-test-app-react as a reference implementation and testing platform.

All tasks must follow this directive:

- <PERSON>oughly read through all the files in ai-docs/context folder
  - ai-docs/context/architecture.md
  - ai-docs/context/domain.md
  - ai-docs/context/bodhi-browser-ext.md
  - ai-docs/context/extension-message-format.md
  - ai-docs/context/bodhi-js.md
- List out and thoroughly read all the relevant files in bodhi-js, bodhi-js/tests, bodhi-js/tests/bodhijs-test-app-react folder
- outline how you are going to implement the task
- post open questions or unclear issues for user input
- get feedback on the plan outline first before starting implementation and changing any files

## Testing Standards

All tasks must follow these testing requirements:

- **Framework**: Vitest for integration tests, Playwright for browser automation
- **Browser Testing**: Real browser instances with extension loading using existing BrowserManager
- **Test Quality**: Fewer substantial scenarios over many fine-grained tests
- **No try-catch**: Throw errors to fail tests, no conditional logic in tests
- **Deterministic Tests**: Test known paths with explicit expects, separate success/error scenarios
- **Setup Strategy**: Costly setup (servers, extensions) in beforeAll, cheap setup (mocks) in beforeEach
- **Test Completion**: All tests must pass - no skipped tests, fix root causes
- **Library Testing Approach**: Test library functionality through the bodhijs-test-app-react UI components in integration tests rather than direct unit tests,
  so it is a black box testing of features,
  as this approach is easier, more useful, and more maintainable when multiple components are involved.

## Tasks

- [x] **extclient-detect-methods** - Enhance BodhiExtClient with Detection Methods

  - ✅ Renamed `detectExtension` to `loadExtensionState` and moved to root level as a standalone function (not static method on BodhiExtClient)
  - ✅ Updated `ExtensionState` to discriminated union with `ExtensionStateReady` and `ExtensionStateNotReady` types
  - ✅ Removed `loadBodhiExtClient` function and related error classes (`ExtensionNotFoundError`, `ExtensionTimeoutError`)
  - ✅ Enhanced `getServerState()` method to return `ServerState` format with proper error mapping
  - ✅ Added `buildPlatformState(extension: ExtensionState, server: ServerState): BodhiPlatformState` method to BodhiExtClient
  - ✅ **Test App Changes**: Migrated LandingPage.tsx, ApiTestPage.tsx, and CallbackPage.tsx from loadBodhiExtClient to new loadExtensionState API
  - ✅ **Integration Tests**: Updated existing detection tests to work with new API structure
  - **Test Requirements**: Extension states (ready, unreachable, not-installed) and server states (ready, setup, resource-admin, unreachable, error, pending-extension-ready) are tested through existing integration tests
  - _Requirements: 1.2, 1.3, 2.1, 3.2, 14.1, 14.2, 14.3_

- [x] **extension-server-state-api** - Add Server State API to Browser Extension

  - Add `serverState(): Promise<ServerStateInfo>` method to window.bodhiext interface in bodhi-browser-ext project
  - Create new server endpoint `/bodhi/v1/info` that returns server state information (completed, ask user for the openapi.json declarations)
  - Implement server state detection logic that returns: setup (initial state after installation), resource-admin (after setup but before admin login), ready (fully configured), error (configuration issues like tampered secrets), unreachable (server not responding)
  - Server state information should include: status, version, url, error details if applicable
  - Extension should communicate with local server through this endpoint and return structured state information
  - read through the directive applicable for all tasks, do the analysis, prepare the task outline, and present the outline with open questions to user for feedback before proceeding with implementation and changing any file
  - **Extension Changes**: Modify bodhi-browser-ext to add serverState method that calls `/bodhi/v1/info` endpoint
  - **Server Changes**: Implement `/bodhi/v1/info` endpoint in app-bindings that returns current server state based on configuration status
  - **Test Requirements**: Test all server states through extension API communication
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

- [x] **platform-core-class** - Implement Core BodhiPlatform Class

  - ✅ Create BodhiPlatform class with BodhiConfig interface supporting timeout, onboarding, and analytics options
  - ✅ Implement non-blocking `initialize()` method using sequential detection pattern
  - ✅ Add `getClient()` method that returns BodhiExtClient when platform is ready
  - ✅ Add `showOnboarding()` method as placeholder (throws "not implemented yet" - will be implemented in modal-infrastructure task)
  - ✅ Implement sequential detection logic: extension first, then server through extension
  - ✅ Handle 'pending-extension-ready' server state when extension is not ready
  - ✅ **Test App Changes**: Migrated existing LandingPage.tsx from loadBodhiExtClient to BodhiPlatform initialization via usePlatformDetection hook, removing old approach entirely
  - ✅ **Integration Tests**: BodhiPlatform functionality is tested through UI components in existing detection test files (following library testing approach through test-app UI)
  - **Test Requirements**: Sequential detection, state building delegation to BodhiExtClient, and non-blocking initialization are tested through existing integration tests via UI components
  - _Requirements: 1.1, 1.2, 1.4, 3.1, 6.1, 14.4_

- [x] **modal-infrastructure** - Create Modal Infrastructure

  - ✅ Implemented BodhiOnboardingModal class with iframe srcdoc approach in `src/onboarding/` directory
  - ✅ Created modal overlay with proper z-index (9999) and backdrop handling
  - ✅ Implemented iframe creation with sandbox attributes (`allow-scripts allow-same-origin`) and CSP compliance
  - ✅ Added basic postMessage communication system between parent and iframe for dismissal and redetection
  - ✅ Handled escape key, backdrop clicks, and Close button dismissal functionality
  - ✅ Used basic, non-conflicting, self-contained styling that cannot be overridden by parent page
  - ✅ Desktop-first responsive design with basic mobile compatibility (not broken on small screens)
  - ✅ Static content only, no animations or transitions
  - ✅ **Test App Changes**: Added modal trigger functionality with data-testid to existing LandingPage.tsx for testing modal display and interaction
  - ✅ **Integration Tests**: Integrated modal tests into existing `extension-detection-with-extension.test.ts` and `extension-detection-without-extension.test.ts` instead of creating duplicate browser setups, testing modal creation, iframe srcdoc, and postMessage communication using real iframe content (black-box testing)
  - **Test Requirements**: Modal overlay creation, iframe sandbox attributes, CSP compliance, cross-frame communication, and different platform state scenarios tested through existing extension detection test infrastructure
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 15.1, 15.3_

- [x] **complete-modal-onboarding-system** - Complete Self-Contained Modal Onboarding System

  - **✅ Already Implemented (Modal Infrastructure)**:
    - BodhiOnboardingModal class with complete modal overlay and iframe srcdoc approach
    - OnboardingContentGenerator class with basic platform state-based content generation
    - Complete postMessage communication system (dismiss-modal, request-redetection, setup-complete, modal-ready)
    - Theme support (light/dark/auto) with CSS custom properties
    - CSP-compliant implementation with all HTML/CSS/JS embedded inline
    - Callback system for onComplete, onDismiss, and onRedetection events
    - Proper cleanup of event listeners and DOM elements
  - **❌ Missing: Enhanced Content Generation System**:
    - Enhance OnboardingContentGenerator to include browser detection (Chrome/Edge supported, others show "not supported")
    - Add OS detection for macOS (supported), others show "coming soon"
    - Create detailed extension installation content with browser-specific instructions and store links
    - Create detailed server setup content with OS-specific download links and installation steps
    - Build comprehensive error state content with GitHub issues integration and system details
    - Add troubleshooting content and recovery guidance for different error scenarios
  - **❌ Missing: Configuration Constants System**:
    - Create constants.ts file in src/onboarding/ with download URLs and GitHub issue templates
    - Define browser-specific download URLs (Chrome/Edge extension stores)
    - Define OS-specific download URLs (macOS server downloads)
    - Set up GitHub issue templates with prefilled system details
    - Include placeholder values for manual population during development
  - **❌ Missing: Browser and OS Detection Integration**:
    - Implement browser detection utilities (Chrome supported, Edge supported, others not supported)
    - Create OS detection utilities (macOS supported, others coming soon)
    - Generate browser-specific installation instructions and store links for supported browsers
    - Add "not supported" messaging for unsupported browsers with GitHub issue links for feature requests
    - Add "coming soon" messaging for unsupported OS with GitHub issue links for interest tracking
    - Integrate detection results into OnboardingContentGenerator content selection logic
  - **❌ Missing: BodhiPlatform Integration**:
    - Replace placeholder BodhiPlatform.showOnboarding() method with actual modal instantiation
    - Integrate modal with BodhiPlatform state management and re-detection flow
    - Handle modal state updates when platform state changes during onboarding process
    - Implement proper modal lifecycle management within BodhiPlatform class
  - **Implementation Requirements**:
    - read through the directive applicable for all tasks, do the analysis, prepare the task outline, and present the outline with open questions to user for feedback before proceeding with implementation and changing any files
    - Follow CSP compliance: all content embedded in iframe srcdoc without external resource loading
    - Ensure framework-agnostic compatibility with self-contained styling
    - Implement proper cleanup of event listeners and DOM elements
    - Use existing BodhiExtClient detection methods and state building functionality
    - Use constants.ts for all configuration values instead of environment variable injection
  - **Test App Changes**:
    - Migrate existing LandingPage.tsx to test complete modal onboarding scenarios across all states
    - Add controls for testing different browser/OS combinations
    - Demonstrate onComplete and onDismiss callback functionality with logging
    - Remove old approach entirely, focusing on BodhiPlatform integration
  - **Integration Tests**:
    - **Automated Testing**: Focus on supported path (macOS/Chrome) with comprehensive integration tests
    - Test modal content generation across different BodhiPlatformState scenarios
    - Test postMessage communication and callback execution in real browser environments
    - Test constants.ts configuration integration and content rendering
    - Validate browser detection and appropriate content display for supported browsers
    - **Manual Testing**: Verify unsupported browser/OS messaging and GitHub issue links
    - Manual verification of constants.ts configuration integration
  - **Test Requirements**:
    - Test complete onboarding flows for supported configurations (macOS/Chrome)
    - Test modal creation, iframe srcdoc generation, and postMessage communication
    - Test browser detection (Chrome/Edge supported path, others manual verification)
    - Test OS detection (macOS supported path, others manual verification)
    - Test constants-based links and inline content embedding
    - Test callback execution and real-time state updates
    - Validate CSP compliance and cross-frame communication security
  - read through the directive applicable for all tasks, do the analysis, prepare the task outline, and present the outline with open questions to user for feedback before proceeding with implementation and changing any file
  - _Requirements: 4.5, 5.1, 5.2, 5.3, 5.4, 5.5, 6.2, 6.3, 7.1, 7.2, 7.3, 7.4, 7.5, 8.1, 8.2, 8.3, 8.4, 8.5, 15.2_

- [ ] **setup-validation** - Implement Setup Validation and Health Checks

  - Implement server API health checks through extension
  - Create end-to-end connectivity validation
  - Add troubleshooting steps for validation failures
  - Ensure BodhiPlatformState shows 'ready' status only when all components are functional
  - **Test App Changes**: Migrate existing LandingPage.tsx to display health check results and validation status, removing old approach entirely
  - **Integration Tests**: Update existing detection tests to include validation logic and health checks
  - **Test Requirements**: Test health checks and validation failure scenarios
  - _Requirements: 9.2, 9.3, 9.4, 9.5_

- [ ] **comprehensive-testing** - Implement Comprehensive Integration Testing
  - Create end-to-end integration tests covering complete onboarding flows
  - Test sequential detection flow: BodhiExtClient.detectExtension() first, then getServerState()
  - Validate all extension and server state combinations
  - Test modal functionality with real browser environments and actual extension loading
  - Create test scenarios for complex onboarding workflows
  - **Test App Changes**: Ensure migrated LandingPage.tsx supports all test scenarios with comprehensive state simulation
  - **Integration Tests**: Create `comprehensive-onboarding.test.ts` with complete end-to-end scenarios
  - **Test Requirements**: Test complete onboarding flows, state transitions, and error recovery in real browser environments
  - _Requirements: 13.1, 13.2, 13.3, 13.4, 13.5_

## Post-MVP Features (To be implemented after core functionality is complete)

- [ ] **theme-accessibility** - Implement Theme and Accessibility Support

  - Add theme support for light, dark, and auto modes in modal content
  - Implement WCAG accessibility guidelines with proper focus management
  - Add keyboard navigation support within iframe
  - Create screen reader compatible content and announcements
  - Implement internationalization structure for future localization
  - _Requirements: 6.4, 11.1, 11.2, 11.3, 11.4, 11.5_

- [ ] **error-recovery** - Create Error Handling and Recovery System

  - Implement BodhiError hierarchy with specific error codes
  - Create ErrorRecoveryManager for handling different error scenarios
  - Add retry mechanisms for transient failures
  - Implement detailed error reporting with troubleshooting guidance
  - Create fallback handling for unrecoverable errors
  - _Requirements: 3.4, 9.4, 9.5_

- [ ] **analytics-telemetry** - Add Optional Analytics and Telemetry
  - Implement analytics configuration through BodhiConfig
  - Add telemetry tracking for setup funnel progression and error scenarios
  - Create privacy-compliant analytics with opt-out mechanisms
  - Track completion rates, abandonment points, and error frequencies
  - Ensure analytics only activate with developer permission
  - _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5_

## Implementation Notes

### Key Architectural Principles

- **Sequential Detection**: Always detect extension first, then server through extension
- **BodhiExtClient Integration**: Leverage existing functionality instead of duplicating code
- **CSP Compliance**: All modal content must be self-contained in iframe srcdoc
- **Framework Agnostic**: Components must work with any framework or vanilla JS
- **Browser Security**: Server communication must go through extension due to localhost restrictions

### MVP Scope and Limitations

- **Browser Support**: Chrome and Edge only (Chromium-based browsers)
- **OS Support**: macOS only for Bodhi App server
- **Testing**: Chrome only for automated testing, manual verification for other features
- **Unsupported Platforms**: Show "not supported" or "coming soon" messaging with GitHub issue links

### Test App Evolution Strategy

The existing bodhijs-test-app-react LandingPage will evolve to support BodhiPlatform testing:

1. **Current State**: Basic extension detection and OAuth flow using loadBodhiClient
2. **Enhanced State**: Add BodhiPlatform integration alongside existing functionality
3. **Testing Features**: State simulation, modal triggers, environment configuration display
4. **Reference Implementation**: Demonstrate BodhiPlatform capabilities within existing structure

### Testing Strategy

- **Integration Focus**: No unit tests, focus on end-to-end integration testing
- **Real Browser Testing**: Use existing BrowserManager with actual extension loading
- **Existing Test Evolution**: Update current detection tests rather than creating new ones
- **Modal Testing**: Separate modal test files for infrastructure testing
- **Manual Testing**: Environment configuration and browser compatibility verified manually

### Environment Configuration

- **Build System**: Use Rollup for build-time variable injection (check package.json)
- **Environment Support**: local, development, production with different variable sets
- **Limited Scope**: Chrome/Edge extension links, macOS server links, GitHub issue links for others
- **Manual Verification**: No automated integration tests for environment configuration initially

This implementation plan ensures incremental development with continuous testing and validation, building from core functionality to complete onboarding system integration while evolving the test app into a comprehensive reference implementation.
