# Requirements Document

## Introduction

The bodhi-js library needs gradual onboarding features to provide a smooth user experience when the required Bodhi Platform components (browser extension or Bodhi App server) are not available. Currently, users encounter errors or failures when these dependencies are missing, creating friction in the adoption process.

This feature will implement a new BodhiPlatform class with built-in modal onboarding that guides users through downloading and setting up the browser extension and Bodhi App server when they are not detected. The onboarding system will provide clear instructions, download links, and status checking to ensure users can successfully complete the setup process.

The onboarding will use a modal-first approach with self-contained HTML+JavaScript+CSS components delivered via iframe srcdoc, ensuring framework-agnostic compatibility without interfering with the host application. This represents a significant architectural improvement over the current loadBodhiClient approach. This is the first release, so no backwards compatibility requirements apply.

## Requirements

### Requirement 1: BodhiPlatform Initialization with Sequential Detection

**User Story:** As a web developer integrating bodhi-js, I want a non-blocking initialization pattern that clearly separates platform detection from client usage, so that I can handle setup states appropriately.

#### Acceptance Criteria

1. WHEN initializing BodhiPlatform THEN the system SHALL provide a non-blocking initialize() method that returns BodhiPlatformState
2. WHEN detecting platform state THEN the system SHALL use sequential detection: extension first, then server through extension
3. WHEN platform state is 'ready' THEN the system SHALL provide access to BodhiExtClient for immediate use with state building handled by BodhiExtClient.buildPlatformState() method
4. WHEN platform state is 'setup' THEN the system SHALL indicate which components need configuration
5. WHEN getting current state THEN the system SHALL use initialize() method to get up-to-date state without persistent storage

### Requirement 2: Extension State Detection via BodhiExtClient

**User Story:** As a web developer integrating bodhi-js, I want detailed extension state information beyond simple installed/not-installed, so that I can provide appropriate guidance to users.

#### Acceptance Criteria

1. ✅ WHEN detecting extension THEN the system SHALL use BodhiExtClient.detectExtension() static method that leverages existing functionality without throwing errors
2. ✅ WHEN extension is installed but not responding THEN the system SHALL detect 'unreachable' status and provide troubleshooting steps
3. ✅ WHEN extension is fully functional THEN the system SHALL detect 'ready' status and provide BodhiExtClient access

### Requirement 3: Sequential Server State Detection via Extension

**User Story:** As a user with the extension installed, I want detailed server state information to understand what setup steps are needed for the Bodhi App server.

#### Acceptance Criteria

1. ✅ WHEN extension is not ready THEN the system SHALL set server status to 'pending-extension-ready' indicating server detection requires extension setup first
2. ✅ WHEN server detection occurs through extension THEN the system SHALL use BodhiExtClient.getServerState() instance method due to browser security restrictions
3. ✅ WHEN server needs initial setup THEN the system SHALL detect 'setup' status and provide configuration instructions
4. ✅ WHEN server needs resource/admin configuration THEN the system SHALL detect 'resource-admin' status and provide admin guidance
5. ✅ WHEN server is fully functional THEN the system SHALL detect 'ready' status and enable API communication

### Requirement 4: Modal-Based Onboarding Interface

**User Story:** As a user needing to complete Bodhi Platform setup, I want a modal interface that guides me through the process without interfering with the host application.

#### Acceptance Criteria

1. ✅ WHEN onboarding is triggered THEN the system SHALL display a modal overlay that is independent from the host application
2. ✅ WHEN modal is displayed THEN the system SHALL use iframe srcdoc for self-contained content without backend dependencies
3. ✅ WHEN modal content is generated THEN the system SHALL embed all HTML, CSS, and JavaScript for complete isolation
4. ✅ WHEN modal is shown THEN the system SHALL handle escape key, backdrop clicks, and provide dismissal options if configured
5. ✅ WHEN modal communicates with parent THEN the system SHALL use postMessage for secure cross-frame communication

### Requirement 5: Progressive Setup Status Tracking

**User Story:** As a user going through the onboarding process, I want to see my progress and current setup status, so that I understand what steps remain and can track my completion.

#### Acceptance Criteria

1. WHEN onboarding starts THEN the system SHALL display content based on current BodhiPlatformState
2. WHEN extension needs installation THEN the system SHALL show extension installation content with environment-based download links
3. WHEN server needs setup THEN the system SHALL show server setup content with OS-specific download links
4. WHEN setup progresses THEN the system SHALL update content dynamically based on real-time state detection
5. WHEN setup encounters errors THEN the system SHALL show specific error states with recovery options and GitHub issues links

### Requirement 6: BodhiPlatform Configuration and Callbacks

**User Story:** As a web developer integrating bodhi-js, I want to configure the onboarding experience and receive callbacks when setup completes, so that I can integrate it seamlessly with my application flow.

#### Acceptance Criteria

1. WHEN initializing BodhiPlatform THEN the system SHALL accept BodhiConfig with timeout, onboarding mode, theme, and dismissible options
2. ✅ WHEN onboarding completes successfully THEN the system SHALL trigger onComplete callback with BodhiPlatformState and BodhiExtClient
3. ✅ WHEN onboarding is dismissed THEN the system SHALL trigger onDismiss callback with current BodhiPlatformState
4. ✅ WHEN configuring theme THEN the system SHALL support 'light', 'dark', and 'auto' theme options
5. ✅ WHEN modal is dismissible THEN the system SHALL allow users to close the modal and continue with limited functionality

### Requirement 7: Configuration Constants System

**User Story:** As a developer deploying bodhi-js, I want download links and configuration to be easily manageable through a constants file, so that I can update URLs and settings without complex build configuration.

#### Acceptance Criteria

1. WHEN displaying extension download links THEN the system SHALL use constants from constants.ts file
2. WHEN showing server download links THEN the system SHALL provide OS-specific download URLs from constants configuration
3. WHEN generating GitHub issues links THEN the system SHALL include prefilled system details from constants templates
4. WHEN configuring endpoints THEN the system SHALL use constants for server URLs and API endpoints
5. WHEN updating configuration THEN the system SHALL allow easy modification through constants.ts without build system changes

### Requirement 8: Multi-Browser Support and Detection

**User Story:** As a user on different browsers, I want the onboarding to detect my browser and provide appropriate installation instructions, so that I get relevant setup guidance.

#### Acceptance Criteria

1. WHEN detecting browser THEN the system SHALL identify Chrome, Edge, Firefox, and other supported browsers
2. WHEN showing extension links THEN the system SHALL provide browser-specific store links from environment configuration
3. WHEN browser is unsupported THEN the system SHALL display compatibility information and alternatives
4. WHEN extension is available THEN the system SHALL show browser-specific installation instructions
5. WHEN multiple browsers are detected THEN the system SHALL provide options for the user's preferred browser

### Requirement 9: Setup Validation and Health Checks

**User Story:** As a user completing setup, I want the system to validate that everything is working correctly, so that I can be confident the integration will function properly.

#### Acceptance Criteria

1. WHEN server connection is established THEN the system SHALL perform basic API health checks and detect appropriate server status
2. WHEN validation succeeds THEN the system SHALL confirm all components are ready and provide BodhiExtClient access
3. WHEN validation fails THEN the system SHALL provide specific troubleshooting steps and error details with retry capability
4. WHEN setup is complete THEN the system SHALL ensure BodhiPlatformState shows 'ready' status with functional client built using BodhiExtClient.buildPlatformState() method

### Requirement 10: Optional Analytics and Telemetry

**User Story:** As a developer maintaining bodhi-js, I want to understand where users encounter setup difficulties, so that I can improve the onboarding experience.

#### Acceptance Criteria

1. WHEN analytics are configured THEN the system SHALL use BodhiConfig object to enable telemetry with developer permission
2. WHEN users encounter errors THEN the system SHALL optionally log error types and frequencies if analytics enabled
3. WHEN setup completes THEN the system SHALL optionally track completion rates if analytics enabled
4. WHEN users abandon setup THEN the system SHALL optionally track abandonment points if analytics enabled
5. WHEN analytics are enabled THEN the system SHALL respect user privacy and provide clear opt-out mechanisms

### Requirement 11: Accessibility and Internationalization

**User Story:** As a user with accessibility needs or non-English language preferences, I want the onboarding experience to be accessible and localized, so that I can complete setup regardless of my abilities or language.

#### Acceptance Criteria

1. WHEN displaying onboarding modal THEN the system SHALL follow WCAG accessibility guidelines with proper focus management
2. WHEN providing instructions THEN the system SHALL support screen readers and keyboard navigation within the iframe
3. WHEN showing text content THEN the system SHALL support internationalization and localization through configuration
4. WHEN using colors or icons THEN the system SHALL provide alternative indicators for accessibility
5. WHEN onboarding is complete THEN the system SHALL announce completion status to assistive technologies

### Requirement 12: Test Application Integration

**User Story:** As a developer testing bodhi-js onboarding features, I want the bodhijs-test-app-react to demonstrate BodhiPlatform functionality, so that I can validate the feature works correctly.

#### Acceptance Criteria

1. WHEN updating test app THEN the system SHALL migrate bodhijs-test-app-react from loadBodhiExtClient to BodhiPlatform class, removing old initialization approach entirely
2. WHEN testing onboarding THEN the system SHALL demonstrate extension and server state detection with modal onboarding
3. WHEN testing different states THEN the system SHALL show various BodhiPlatformState scenarios (ready, setup, error)
4. WHEN onboarding completes THEN the system SHALL transition seamlessly to BodhiExtClient usage for normal app functionality
5. WHEN testing scenarios THEN the system SHALL allow simulation of different extension and server states

### Requirement 13: Integration Testing Coverage

**User Story:** As a developer maintaining bodhi-js, I want comprehensive integration tests for BodhiPlatform onboarding features, so that I can ensure the functionality works reliably across different scenarios in real browser environments.

#### Acceptance Criteria

1. WHEN adding onboarding tests THEN the system SHALL create integration tests in the bodhi-js/tests/ folder focusing on end-to-end functionality rather than unit tests due to the complex nature of the application
2. WHEN testing sequential detection THEN the system SHALL test extension detection using BodhiExtClient.detectExtension() first, then server detection through BodhiExtClient.getServerState()
3. WHEN testing server states THEN the system SHALL test all server states including 'pending-extension-ready' when extension is not ready
4. WHEN testing modal functionality THEN the system SHALL validate modal display, iframe content generation, and postMessage communication in real browser environments
5. WHEN testing complex scenarios THEN the system SHALL use real browser environments with actual extension loading and mock services for different conditions, validating BodhiExtClient detection and state building methods

### Requirement 14: BodhiExtClient Integration and State Management

**User Story:** As a developer maintaining bodhi-js, I want the BodhiPlatform to leverage existing BodhiExtClient functionality for detection and state management, so that I can avoid code duplication and maintain consistency with existing patterns.

#### Acceptance Criteria

1. ✅ WHEN implementing extension detection THEN the system SHALL use BodhiExtClient.detectExtension() static method rather than creating separate detector classes
2. ✅ WHEN implementing server detection THEN the system SHALL use BodhiExtClient.getServerState() instance method rather than direct server communication
3. ✅ WHEN building platform state THEN the system SHALL use BodhiExtClient.buildPlatformState() method to determine overall status from extension and server states
4. ✅ WHEN BodhiPlatform initializes THEN the system SHALL delegate state building logic to BodhiExtClient rather than implementing it in BodhiPlatform
5. ✅ WHEN detection results are processed THEN the system SHALL avoid timestamps and focus on current state without persistent storage

### Requirement 15: Security and CSP Compatibility

**User Story:** As a web developer deploying in security-conscious environments, I want the onboarding modal to work with strict Content Security Policies, so that it doesn't compromise application security.

#### Acceptance Criteria

1. ✅ WHEN modal is displayed THEN the system SHALL use iframe srcdoc without external resource loading
2. ✅ WHEN embedding content THEN the system SHALL embed all CSS and JavaScript inline without eval() or unsafe-inline
3. ✅ WHEN communicating between frames THEN the system SHALL use postMessage for secure cross-frame communication
4. ✅ WHEN isolating modal context THEN the system SHALL ensure modal runs in separate context without access to parent page data
5. ✅ WHEN handling user data THEN the system SHALL avoid persistent storage in modal context and communicate only through defined message interface
