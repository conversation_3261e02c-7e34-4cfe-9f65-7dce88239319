# Magic Patterns Prompt for Bodhi Setup Wizard Modal

## Project Brief: Bodhi Local LLM Setup Wizard

**Create a modern, clean setup wizard modal for a developer tool that helps users set up local LLM (Large Language Model) integration in web browsers.**

### Context & Purpose
- **Product**: Bodhi Browser - enables web pages to securely connect to locally running AI/LLM services
- **User Flow**: Web developers integrating AI features need to install a Chrome extension + local server app
- **Technical Constraint**: Modal renders inside an iframe with inline CSS (no external resources)
- **Auto-Detection**: The wizard automatically detects installation progress and updates in real-time (no manual "Next" buttons)

### Two-Step Setup Process

**STEP 1: Browser Extension Setup**
- State: Extension not installed → Show installation instructions with browser-specific download links
- State: Extension installed but not responding → Show troubleshooting steps  
- State: Extension ready → Auto-advance to Step 2

**STEP 2: Local Server Setup** (only accessible after Step 1 complete)
- State: Server not running → Show download/installation instructions for Bodhi App
- State: Server running but needs setup → Show configuration guidance
- State: Server needs admin access → Show admin setup steps
- State: Server connection issues → Show troubleshooting
- State: Server ready → Show completion message

### Platform Support Matrix
- **Browsers**: Chrome ✅, Edge ✅, Firefox 🔄 (coming soon), Safari 🔄 (coming soon), Others ❌ (not supported)
- **Operating Systems**: macOS ✅, Windows 🔄 (coming soon), Linux 🔄 (coming soon)

### Design Requirements

**Visual Style**: 
- Modern, clean, professional setup wizard (think VS Code installer, Stripe onboarding, or GitHub setup flows)
- Cross-browser compatible styling that renders consistently
- Responsive design that works on different screen widths
- Informative and developer-friendly (target audience: web developers)

**Layout Structure**:
- Modal overlay with centered wizard container
- Step indicator/progress bar at top showing "Extension Setup" → "Server Setup" → "Complete"
- Current step content area with instructions, status indicators, and action buttons
- Auto-updating status indicators for each component (Extension: ✅/❌/🔄, Server: ✅/❌/🔄)

**Content Sections Per Step**:
1. **Status Panel**: Real-time status of extension and server with visual indicators
2. **Instructions Panel**: Step-by-step setup instructions with OS/browser-specific guidance
3. **Download Section**: Prominent download buttons with browser/OS detection
4. **Troubleshooting**: Collapsible section with common issues and solutions
5. **Actions**: "Check Again" button, "Close" button, GitHub issue reporting links

**State Handling**:
- **Supported Platforms**: Show download buttons and detailed instructions
- **Coming Soon Platforms**: Show "coming soon" message with GitHub issue link for interest
- **Unsupported Platforms**: Show "not supported" message with alternative suggestions
- **Error States**: Inline error messages with specific troubleshooting steps
- **Loading States**: Subtle loading indicators during detection

**Animations & Transitions**:
- Smooth transitions between steps (slide/fade effects)
- Step progress indicator animation
- Status indicator state changes (loading spinners, checkmarks, error icons)
- Subtle hover effects on interactive elements

**Color Scheme** (will be provided separately):
- Primary brand colors for buttons and accents
- Success green for completed states
- Warning amber for "coming soon" states  
- Error red for failed states
- Neutral grays for secondary content

### Technical Constraints
- **Iframe Rendering**: All CSS must be inline (no external stylesheets)
- **No External Resources**: No external fonts, icons, or images
- **Cross-Browser**: Must work in Chrome, Edge, Firefox, Safari
- **CSP Compliant**: No eval(), no unsafe-inline scripts
- **Mobile Responsive**: Should not break on mobile screens (though desktop-first)

### Key User Scenarios

**Scenario 1 - New User (Nothing Installed)**:
Step 1: "Install Chrome Extension" → Step 2: "Install Bodhi App" → Complete

**Scenario 2 - Extension Installed, Server Missing**:
Step 1: ✅ Extension Ready → Step 2: "Install Bodhi App" → Complete  

**Scenario 3 - Both Installed, Server Not Running**:
Step 1: ✅ Extension Ready → Step 2: "Start Bodhi App" troubleshooting → Complete

**Scenario 4 - Unsupported Browser/OS**:
Step 1: "Browser not supported - use Chrome or Edge" + GitHub issue link

### Content Tone
- **Professional but friendly**: "Let's get your local AI setup running"
- **Developer-focused**: Technical accuracy, clear error messages
- **Helpful**: Specific troubleshooting steps, not generic "contact support"
- **Encouraging**: Progress indicators, "almost there" messaging

### Interactive Elements
- **Auto-updating status badges** for extension and server states
- **Prominent download buttons** with platform detection
- **Expandable troubleshooting sections** 
- **"Check Again" button** to manually trigger re-detection
- **GitHub issue links** for unsupported platforms or bug reports
- **Close/dismiss functionality** (if modal is dismissible)

### Success State
Final screen shows:
- ✅ Extension Ready
- ✅ Server Ready  
- "Setup Complete! Your page can now use local AI services"
- "Continue to Application" button

### Reference Designs to Consider
- VS Code installation wizard
- Stripe Connect onboarding flow  
- GitHub CLI setup process
- Docker Desktop setup wizard
- Homebrew installation flow

### Specific UI Components Needed
1. **Step Progress Indicator**: Horizontal progress bar or step dots
2. **Status Cards**: Component cards showing Extension/Server status with icons
3. **Download Button Variants**: Primary (supported), Secondary (coming soon), Disabled (not supported)
4. **Collapsible Sections**: For troubleshooting and advanced options
5. **Toast/Alert Components**: For error states and status updates
6. **Loading States**: Spinner/skeleton components for detection phases

### Key Interactions to Design
- Auto-advancing between steps (no manual next buttons)
- Real-time status updates with visual feedback
- Platform-specific content showing/hiding
- Error state handling with recovery actions
- Modal dismissal and completion flows

---

**Design this as a complete, self-contained HTML modal with inline CSS that creates a polished, professional setup wizard experience for developer users setting up local AI integration. Focus on the auto-detection and real-time updating behavior that makes this unique compared to typical step-by-step wizards.**