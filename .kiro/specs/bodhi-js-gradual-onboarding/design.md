# Design Document

## Overview

This design document outlines the technical approach for implementing gradual onboarding features in the bodhi-js library through a new BodhiPlatform architecture. The design introduces a modal-first onboarding system that replaces the current loadBodhiClient approach with a more intuitive, non-blocking initialization pattern.

The core architectural shift moves from direct window.bodhiext usage to a platform-based abstraction that provides comprehensive state management, modal onboarding, and enhanced developer experience. The onboarding modal uses iframe srcdoc for complete self-containment, ensuring framework-agnostic compatibility and CSP compliance.

**Key Design Decisions:**

- BodhiPlatform class with non-blocking initialization and clear state management
- Modal-first onboarding using iframe srcdoc for complete isolation
- Enhanced state detection with detailed extension and server states
- Self-contained HTML+CSS+JS delivery without backend dependencies
- postMessage communication for secure cross-frame interaction

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Host Application"
        A[BodhiPlatform] --> B[initialize]
        A --> R[BodhiExtClient]
        A --> D[getClient]
        A --> E[showOnboarding]
    end

    subgraph "State Detection"
        F[Extension Detection] --> G[ExtensionState]
        H[Server Detection] --> I[ServerState]
        G --> J[BodhiPlatformState]
        I --> J
    end

    subgraph "Onboarding Modal"
        K[BodhiOnboardingModal] --> L[Modal Overlay]
        L --> M[iframe srcdoc]
        M --> N[Self-contained HTML+CSS+JS]
        N --> O[postMessage Communication]
    end

    subgraph "Browser Extension"
        P[window.bodhiext]
        Q[Extension API]
    end

    subgraph "Bodhi App Server"
        S[Local LLM Server]
        T[API Endpoints]
        U[Health Checks]
    end

    A --> F
    A --> H
    E --> K
    R --> P
    F --> Q
    H --> T
    O --> A
```

### Component Architecture

The system is organized into four main layers:

1. **Platform Layer**: BodhiPlatform class managing initialization and state
2. **Detection Layer**: Extension and server state detection with detailed status
3. **Onboarding Layer**: Modal interface with iframe-based self-contained UI
4. **Communication Layer**: BodhiExtClient for extension API interaction

## Components and Interfaces

### Core Platform Components

#### BodhiPlatform Class

The main interface for the library, replacing the current loadBodhiClient approach:

```typescript
interface BodhiConfig {
  timeout?: number;
  onboarding?: {
    mode: 'modal'; // Only modal mode supported in MVP
    theme?: 'light' | 'dark' | 'auto';
    dismissible?: boolean;
    callbacks?: {
      onComplete?: (state: BodhiPlatformState, client: BodhiExtClient) => void;
      onDismiss?: (state: BodhiPlatformState) => void;
    };
  };
  analytics?: {
    enabled: boolean;
  };
}

class BodhiPlatform {
  private config: BodhiConfig;
  private currentState: BodhiPlatformState | null = null;
  private client: BodhiExtClient | null = null;
  private modal: BodhiOnboardingModal | null = null;

  constructor(config: BodhiConfig = {}) {
    this.config = this.mergeDefaultConfig(config);
  }

  // Non-blocking initialization
  async initialize(): Promise<BodhiPlatformState> {
    const detectionResult = await this.detectCurrentState();

    // Use BodhiExtClient to build platform state
    if (detectionResult.extension.status === 'ready') {
      const extensionClient = new BodhiExtClient(detectionResult.extension.id!);
      this.currentState = extensionClient.buildPlatformState(
        detectionResult.extension,
        detectionResult.server
      );
      this.client = extensionClient;
    } else {
      // Build state without client when extension not ready
      this.currentState = {
        status: 'setup',
        extension: detectionResult.extension,
        server: detectionResult.server,
      };
    }

    return this.currentState;
  }

  // Get client if ready
  getClient(): BodhiExtClient | null {
    return this.client;
  }

  // Show onboarding modal (placeholder - implemented in modal-infrastructure task)
  showOnboarding(): void {
    throw new Error('Onboarding modal not implemented yet');
  }

  private async detectCurrentState(): Promise<DetectionResult> {
    // Sequential detection: extension first, then server through extension
    const extension = await BodhiExtClient.detectExtension(this.config.timeout);

    let server: ServerState;
    if (extension.status === 'ready') {
      // Only detect server if extension is ready
      const extensionClient = new BodhiExtClient(extension.id!);
      server = await extensionClient.getServerState();
    } else {
      // Server state is pending extension setup
      server = { status: 'pending-extension-ready' };
    }

    return { extension, server };
  }
}
```

#### State Management System

```typescript
interface BodhiPlatformState {
  status: 'ready' | 'setup' | 'error';
  extension: ExtensionState;
  server: ServerState;
}

// ✅ IMPLEMENTED: Enhanced ExtensionState interface
interface ExtensionState {
  status: 'ready' | 'unreachable' | 'not-installed';
  version?: string;
  id?: string;
  error?: {
    type: 'network' | 'server' | 'config';
    code?: string;
    message: string;
    retryable?: boolean; // Made optional for flexibility
  };
}

interface ServerState {
  status:
    | 'pending-extension-ready'
    | 'ready'
    | 'setup'
    | 'resource-admin'
    | 'unreachable'
    | 'error';
  url?: string;
  version?: string;
  error?: {
    type: 'network' | 'server' | 'auth' | 'config';
    code?: string;
    message: string;
    retryable: boolean;
  };
}

// State progression logic moved to BodhiExtClient
// BodhiExtClient.buildPlatformState(extension, server) handles this logic
```

### Onboarding Modal Components

#### BodhiOnboardingModal Class

```typescript
class BodhiOnboardingModal {
  private modal: HTMLElement | null = null;
  private iframe: HTMLIFrameElement | null = null;
  private messageHandler: (event: MessageEvent) => void;

  constructor(private state: BodhiPlatformState, private config: BodhiConfig) {
    this.messageHandler = this.handleIframeMessage.bind(this);
  }

  show(): void {
    if (this.modal) return; // Already showing

    this.modal = this.createModalOverlay();
    this.iframe = this.createOnboardingIframe();

    this.modal.appendChild(this.iframe);
    document.body.appendChild(this.modal);

    // Setup event listeners
    this.setupEventListeners();
    window.addEventListener('message', this.messageHandler);
  }

  hide(): void {
    if (this.modal) {
      window.removeEventListener('message', this.messageHandler);
      document.body.removeChild(this.modal);
      this.modal = null;
      this.iframe = null;
    }
  }

  private createModalOverlay(): HTMLElement {
    const overlay = document.createElement('div');
    overlay.style.cssText = this.getOverlayStyles();

    // Handle backdrop clicks if dismissible
    if (this.config.onboarding?.dismissible) {
      overlay.addEventListener('click', e => {
        if (e.target === overlay) {
          this.handleDismiss();
        }
      });
    }

    return overlay;
  }

  private createOnboardingIframe(): HTMLIFrameElement {
    const iframe = document.createElement('iframe');

    // Use srcdoc for self-contained content
    iframe.setAttribute('srcdoc', this.generateOnboardingHTML());
    iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin');
    iframe.style.cssText = this.getIframeStyles();

    return iframe;
  }

  private generateOnboardingHTML(): string {
    return `<!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>Bodhi Setup</title>
      <style>${this.getEmbeddedCSS()}</style>
    </head>
    <body>
      ${this.getOnboardingContent()}
      <script>${this.getEmbeddedJS()}</script>
    </body>
    </html>`;
  }

  private getOnboardingContent(): string {
    // Generate content based on current state
    if (this.state.extension.status === 'not-installed') {
      return this.getExtensionInstallContent();
    } else if (this.state.extension.status === 'unreachable') {
      return this.getExtensionTroubleshootContent();
    } else if (this.state.server.status !== 'ready') {
      return this.getServerSetupContent();
    }
    return this.getGenericSetupContent();
  }

  private handleIframeMessage(event: MessageEvent): void {
    if (event.source !== this.iframe?.contentWindow) return;

    switch (event.data.type) {
      case 'setup-complete':
        this.handleSetupComplete();
        break;
      case 'request-redetection':
        this.handleRedetection();
        break;
      case 'dismiss-modal':
        this.handleDismiss();
        break;
    }
  }
}
```

#### Self-Contained Content Generation

```typescript
class OnboardingContentGenerator {
  constructor(private state: BodhiPlatformState, private config: BodhiConfig) {}

  getExtensionInstallContent(): string {
    const downloadLinks = this.getEnvironmentDownloadLinks();
    const browserInfo = this.detectBrowser();

    return `
      <div class="onboarding-step">
        <h2>Install Bodhi Browser Extension</h2>
        <p>The Bodhi browser extension is required to connect your web application to local LLM services.</p>
        
        <div class="browser-detection">
          <p>Detected browser: <strong>${browserInfo.name}</strong></p>
        </div>
        
        <div class="download-section">
          <a href="${downloadLinks[browserInfo.type]}" 
             class="download-button primary" 
             target="_blank">
            Install Extension for ${browserInfo.name}
          </a>
        </div>
        
        <div class="instructions">
          <ol>
            <li>Click the download link above</li>
            <li>Follow your browser's installation prompts</li>
            <li>Enable the extension when prompted</li>
            <li>Return to this page to continue</li>
          </ol>
        </div>
        
        <div class="actions">
          <button onclick="requestRedetection()" class="button secondary">
            Check Again
          </button>
          <button onclick="showTroubleshooting()" class="button tertiary">
            Troubleshooting
          </button>
        </div>
      </div>
    `;
  }

  getServerSetupContent(): string {
    const serverDownloadLinks = this.getServerDownloadLinks();
    const osInfo = this.detectOperatingSystem();

    return `
      <div class="onboarding-step">
        <h2>Install Bodhi App Server</h2>
        <p>The Bodhi App server runs locally to provide LLM inference capabilities.</p>
        
        <div class="os-detection">
          <p>Detected OS: <strong>${osInfo.name}</strong></p>
        </div>
        
        <div class="download-section">
          <a href="${serverDownloadLinks[osInfo.type]}" 
             class="download-button primary" 
             target="_blank">
            Download Bodhi App for ${osInfo.name}
          </a>
        </div>
        
        <div class="instructions">
          <ol>
            <li>Download the Bodhi App for your operating system</li>
            <li>Install and launch the application</li>
            <li>Complete the initial setup wizard</li>
            <li>Ensure the server is running</li>
          </ol>
        </div>
        
        <div class="actions">
          <button onclick="testConnection()" class="button secondary">
            Test Connection
          </button>
          <button onclick="showServerHelp()" class="button tertiary">
            Server Help
          </button>
        </div>
      </div>
    `;
  }

  private getDownloadLinks(): Record<string, string> {
    // Import from constants.ts
    return {
      chrome: ONBOARDING_CONFIG.downloadLinks.extension.chrome,
      edge: ONBOARDING_CONFIG.downloadLinks.extension.edge,
    };
  }
}
```

### Detection System Components

#### Browser Extension API Enhancement

The browser extension (bodhi-browser-ext) needs to be enhanced with a new server state detection method:

```typescript
// New method to be added to window.bodhiext interface
interface WindowBodhiExt {
  // ... existing methods ...

  /**
   * Get server state information by calling /bodhi/v1/info endpoint
   * @returns Promise resolving to server state information
   */
  serverState(): Promise<ServerStateInfo>;
}

interface ServerStateInfo {
  status: 'setup' | 'resource-admin' | 'ready' | 'error' | 'unreachable';
  url?: string;
  version?: string;
  error?: {
    type: 'network' | 'server' | 'auth' | 'config';
    code?: string;
    message: string;
    retryable: boolean;
  };
}
```

The `/bodhi/v1/info` endpoint will be implemented in the app-bindings server component to return current server state based on:

- **setup**: Initial state after Bodhi App Server installation
- **resource-admin**: After setup process, registered with central auth but user not admin yet
- **ready**: User logged in and made admin, fully functional
- **error**: Configuration issues like tampered secret files
- **unreachable**: Server not responding

#### Extension and Server Detection via BodhiExtClient

The detection system leverages the existing BodhiExtClient functionality rather than implementing separate detector classes:

```typescript
// Extension detection is handled by BodhiExtClient static method
class BodhiExtClient {
  // Static method for extension detection without throwing errors
  static async detectExtension(timeout: number = 10000): Promise<ExtensionState> {
    // ✅ IMPLEMENTED: Returns detailed extension state without throwing errors
    // Used by BodhiPlatform for safe extension detection
    try {
      // Step 1: Check if window.bodhiext exists with polling
      const extensionDetected = await new Promise<boolean>(resolve => {
        const startTime = Date.now();
        const checkExtension = () => {
          if (typeof window.bodhiext !== 'undefined') {
            resolve(true);
            return;
          }
          if (Date.now() - startTime >= timeout) {
            resolve(false);
            return;
          }
          setTimeout(checkExtension, 100);
        };
        checkExtension();
      });

      if (!extensionDetected) {
        return { status: 'not-installed' };
      }

      // Step 2: Try to get extension ID to verify it's functional
      try {
        const extensionIdPromise = window.bodhiext!.getExtensionId();
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Extension ID timeout')), timeout);
        });
        const extensionId = await Promise.race([extensionIdPromise, timeoutPromise]);
        return { status: 'ready', id: extensionId };
      } catch (error) {
        return {
          status: 'unreachable',
          error: {
            type: 'network',
            message: error instanceof Error ? error.message : 'Extension not responding',
            retryable: true,
          },
        };
      }
    } catch (error) {
      return {
        status: 'not-installed',
        error: {
          message: error instanceof Error ? error.message : 'Extension detection failed',
          type: 'network',
          retryable: false,
        },
      };
    }
  }

  // Instance method for server state detection
  async getServerState(): Promise<ServerState> {
    // ✅ IMPLEMENTED: Server detection through extension with comprehensive error handling
    try {
      // Server detection through extension due to browser security sandbox
      // Web pages cannot directly communicate with localhost/local network
      // Uses window.bodhiext.serverState() method that calls /bodhi/v1/info endpoint
      const serverInfo = await window.bodhiext!.serverState();

      // Map ServerStateInfo to ServerState format with enhanced error handling
      return {
        status: serverInfo.status,
        version: serverInfo.version,
        url: serverInfo.url,
        error: serverInfo.error
          ? {
              message: serverInfo.error.message,
              type: serverInfo.error.type,
              code: serverInfo.error.code,
              retryable: true, // Default to retryable for server errors
            }
          : undefined,
      };
    } catch (error) {
      return {
        status: 'error',
        error: {
          message: error instanceof Error ? error.message : 'Unknown server error',
          type: 'network',
          retryable: true,
        },
      };
    }
  }

  // ✅ IMPLEMENTED: State building logic centralized in BodhiExtClient
  buildPlatformState(extension: ExtensionState, server: ServerState): BodhiPlatformState {
    let status: 'ready' | 'setup' | 'error';

    // Determine overall status based on component states
    if (extension.status === 'ready' && server.status === 'ready') {
      status = 'ready';
    } else if (
      extension.status === 'not-installed' ||
      extension.status === 'unreachable' ||
      server.status === 'setup' ||
      server.status === 'resource-admin' ||
      server.status === 'pending-extension-ready' ||
      server.status === 'unreachable'
    ) {
      status = 'setup';
    } else {
      status = 'error';
    }

    return { status, extension, server };
  }
}
```

## Data Models

### Core Types

```typescript
// Configuration Types
interface BodhiConfig {
  timeout?: number;
  onboarding?: OnboardingConfig;
  analytics?: AnalyticsConfig;
}

interface OnboardingConfig {
  mode: 'modal';
  theme?: 'light' | 'dark' | 'auto';
  dismissible?: boolean;
  callbacks?: {
    onComplete?: (state: BodhiPlatformState, client: BodhiExtClient) => void;
    onDismiss?: (state: BodhiPlatformState) => void;
  };
}

interface AnalyticsConfig {
  enabled: boolean;
}

// State Types
// ✅ IMPLEMENTED: BodhiPlatformState interface
interface BodhiPlatformState {
  status: 'ready' | 'setup' | 'error';
  extension: ExtensionState;
  server: ServerState;
  // Note: client is managed separately in BodhiPlatform class, not in state object
}

interface ExtensionState {
  status: 'ready' | 'unreachable' | 'not-installed';
  version?: string;
  id?: string;
}

// ✅ IMPLEMENTED: Enhanced ServerState interface
interface ServerState {
  status:
    | 'ready'
    | 'setup'
    | 'resource-admin'
    | 'unreachable'
    | 'error'
    | 'pending-extension-ready';
  url?: string;
  version?: string;
  error?: {
    type: 'network' | 'server' | 'auth' | 'config';
    code?: string;
    message: string;
    retryable?: boolean; // Made optional for flexibility
  };
}

interface ServerError {
  type: 'network' | 'server' | 'auth' | 'config';
  code?: string;
  message: string;
  retryable: boolean;
}

// Detection Types
interface DetectionResult {
  extension: ExtensionState;
  server: ServerState;
}

// Communication Types
interface ModalMessage {
  type: 'setup-complete' | 'request-redetection' | 'dismiss-modal' | 'config-update';
  data?: any;
}
```

### Configuration Constants

```typescript
interface OnboardingConfig {
  downloadLinks: {
    extension: {
      chrome: string;
      edge: string;
    };
    server: {
      macos: string;
    };
  };
  githubIssues: {
    baseUrl: string;
    unsupportedBrowserTemplate: string;
    unsupportedOSTemplate: string;
    bugReportTemplate: string;
  };
  serverEndpoints: {
    default: string;
    health: string;
  };
}

// Configuration constants from constants.ts
export const ONBOARDING_CONFIG: OnboardingConfig = {
  downloadLinks: {
    extension: {
      chrome: 'https://chrome.google.com/webstore/detail/bodhi-extension/...',
      edge: 'https://microsoftedge.microsoft.com/addons/detail/bodhi-extension/...',
    },
    server: {
      macos: 'https://releases.bodhi.app/macos/latest',
    },
  },
  githubIssues: {
    baseUrl: 'https://github.com/bodhiapp/bodhi-js/issues/new',
    unsupportedBrowserTemplate: '?template=unsupported_browser.md&title=Browser%20Support%20Request',
    unsupportedOSTemplate: '?template=unsupported_os.md&title=OS%20Support%20Request',
    bugReportTemplate: '?template=bug_report.md&title=Onboarding%20Issue',
  },
  serverEndpoints: {
    default: 'http://localhost:8080',
    health: '/ping',
  },
};
```

## Error Handling

### Error Hierarchy

```typescript
enum BodhiErrorCode {
  EXTENSION_NOT_INSTALLED = 'EXTENSION_NOT_INSTALLED',
  EXTENSION_UNREACHABLE = 'EXTENSION_UNREACHABLE',
  SERVER_UNREACHABLE = 'SERVER_UNREACHABLE',
  SERVER_NEEDS_SETUP = 'SERVER_NEEDS_SETUP',
  SERVER_NEEDS_CONFIG = 'SERVER_NEEDS_CONFIG',
  SERVER_ERROR = 'SERVER_ERROR',
  MODAL_COMMUNICATION_ERROR = 'MODAL_COMMUNICATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

class BodhiError extends Error {
  constructor(
    message: string,
    public code: BodhiErrorCode,
    public retryable: boolean = false,
    public data?: any
  ) {
    super(message);
    this.name = 'BodhiError';
  }
}

// Specific error classes
class ExtensionNotInstalledError extends BodhiError {
  constructor() {
    super(
      'Bodhi browser extension is not installed',
      BodhiErrorCode.EXTENSION_NOT_INSTALLED,
      false
    );
  }
}

class ServerUnreachableError extends BodhiError {
  constructor(serverUrl: string) {
    super(
      `Cannot connect to Bodhi server at ${serverUrl}`,
      BodhiErrorCode.SERVER_UNREACHABLE,
      true,
      { serverUrl }
    );
  }
}
```

### Error Recovery Strategies

```typescript
class ErrorRecoveryManager {
  constructor(private platform: BodhiPlatform) {}

  async handleError(error: BodhiError): Promise<boolean> {
    switch (error.code) {
      case BodhiErrorCode.EXTENSION_NOT_INSTALLED:
        return this.handleExtensionNotInstalled();

      case BodhiErrorCode.SERVER_UNREACHABLE:
        return this.handleServerUnreachable(error.data?.serverUrl);

      case BodhiErrorCode.EXTENSION_UNREACHABLE:
        return this.handleExtensionUnreachable();

      default:
        return false;
    }
  }

  private async handleExtensionNotInstalled(): Promise<boolean> {
    // Show onboarding modal with extension installation
    this.platform.showOnboarding();
    return true;
  }

  private async handleServerUnreachable(serverUrl: string): Promise<boolean> {
    // Show onboarding modal with server setup
    this.platform.showOnboarding();
    return true;
  }

  private async handleExtensionUnreachable(): Promise<boolean> {
    // Show onboarding modal with extension troubleshooting
    this.platform.showOnboarding();
    return true;
  }
}
```

## Testing Strategy

### Testing Approach

The testing strategy will focus on validating the BodhiPlatform architecture and onboarding functionality through multiple layers:

**Integration Testing:**

- Integration with bodhijs-test-app-react using the new BodhiPlatform API
- End-to-end onboarding flows from extension detection through server setup
- Modal functionality including iframe content generation and cross-frame communication
- State transitions and callback execution
- Sequential detection flow validation (extension first, then server through extension)
- All extension states (ready, installed, unreachable, not-installed, unsupported, disabled)
- All server states (ready, setup, resource-admin, unreachable, error, pending-extension-ready)
- Modal rendering, user interactions, and postMessage communication
- Error handling and recovery mechanisms across different scenarios
- Browser compatibility testing across Chrome, Firefox, and Edge
- Environment-based configuration testing (dev vs production builds)
- BodhiPlatform class initialization and state management in real browser environments
- BodhiExtClient detection methods and state building functionality
- Complex onboarding scenarios with different extension and server state combinations

**Test Implementation:**

- Tests will be implemented in the bodhi-js/tests/ folder
- Test app will be updated to demonstrate BodhiPlatform functionality
- Tests will use real browser environments with actual extension loading
- Mock services will simulate different server states and extension conditions
- Cross-frame communication will be validated through actual iframe interactions

The testing implementation will be guided by the current state of the codebase and will focus on ensuring reliable functionality across different browser environments and setup scenarios.

## Implementation Considerations

### Performance Optimization

**Modal Loading**

- Lazy load modal content only when needed
- Cache generated HTML for repeated displays
- Minimize iframe content size

**State Detection**

- Implement efficient polling with exponential backoff
- Cache detection results with appropriate TTL
- Use sequential detection: extension first, then server through extension
- Optimize server detection by only running when extension is ready

**Memory Management**

- Proper cleanup of event listeners
- Remove modal DOM elements when hidden
- Clear timeouts and intervals

### Security Considerations

**Content Security Policy**

- All content embedded in iframe srcdoc
- No external resource loading
- No eval() or unsafe-inline usage
- Secure postMessage communication

**Data Isolation**

- Modal runs in separate iframe context
- No access to parent page data
- Communication only through defined message interface
- No persistent storage in modal context

### Browser Compatibility

**Extension Support**

- Chrome/Chromium-based browsers (primary)
- Firefox support (secondary)
- Edge support (secondary)
- Graceful degradation for unsupported browsers

**Modal Compatibility**

- Modern browser support (ES2020+)
- Responsive design for mobile devices
- Accessibility compliance (WCAG 2.1)
- Keyboard navigation support

### Development Experience

**TypeScript Integration**

- Comprehensive type definitions
- Strict type checking
- IntelliSense support for all APIs

**Debugging Support**

- Detailed error messages with context
- Debug logging capabilities
- Development mode features

**Documentation**

- Comprehensive API documentation
- Usage examples and tutorials
- Migration guide from loadBodhiClient

## Implementation Status

### ✅ Completed Components

#### BodhiExtClient Detection Methods (extclient-detect-methods)

- **Static `detectExtension()` method**: Implemented with comprehensive error handling and timeout support
- **Enhanced `getServerState()` method**: Returns ServerState format with proper error mapping
- **`buildPlatformState()` method**: Combines extension and server states into platform state
- **New type definitions**: ExtensionState, ServerState, and BodhiPlatformState interfaces
- **Test app integration**: LandingPage.tsx updated to use new detection methods
- **Integration tests**: Enhanced to verify platform state display and functionality

#### Browser Extension Server State API (extension-server-state-api)

- **`serverState()` method**: Added to window.bodhiext interface
- **`/bodhi/v1/info` endpoint**: Implemented in server to return state information
- **Server state detection**: All states (setup, resource-admin, ready, error, unreachable) supported
- **Comprehensive testing**: All server states tested through extension API

#### Modal Infrastructure (modal-infrastructure)

- **✅ BodhiOnboardingModal class**: Complete implementation with iframe srcdoc approach
- **✅ Modal overlay and styling**: Self-contained CSS with proper z-index and backdrop handling
- **✅ postMessage communication**: Full bidirectional communication system
- **✅ Event handling**: Escape key, backdrop clicks, and dismissal functionality
- **✅ Theme support**: Light/dark/auto theme support with CSS custom properties
- **✅ CSP compliance**: All content embedded inline without external resources

#### OnboardingContentGenerator (partial implementation)

- **✅ Basic content generation**: Platform state-based content selection
- **✅ Template system**: HTML template with placeholder replacement
- **✅ Responsive design**: Mobile-compatible styling with proper viewport handling
- **❌ Browser detection**: Missing browser-specific content and detection logic
- **❌ OS detection**: Missing OS-specific content and detection logic
- **❌ Constants integration**: Missing constants.ts configuration integration

### 🔄 Next Implementation Phase

The modal infrastructure is complete. Remaining components to implement:

- **Enhanced content generation**: Browser/OS detection and constants-based content
- **Configuration constants**: constants.ts file with download URLs and GitHub templates
- **BodhiPlatform integration**: Replace placeholder showOnboarding() method
- **Comprehensive testing**: Integration tests for complete onboarding flows

This design provides a solid foundation for implementing the BodhiPlatform onboarding system while ensuring maintainability, testability, and excellent developer experience. The modal-first approach with iframe isolation ensures compatibility across different host applications while providing a consistent, professional onboarding experience.
