# Bodhi Setup Wizard UI Wireframes

ASCII wireframes showing different states of the setup wizard modal.

## Modal Container Structure

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          MODAL OVERLAY (backdrop)                          │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │                    WIZARD CONTAINER                           │     │
│     │  ┌─────────────────────────────────────────────────────────┐  │     │
│     │  │              STEP PROGRESS INDICATOR                   │  │     │
│     │  └─────────────────────────────────────────────────────────┘  │     │
│     │  ┌─────────────────────────────────────────────────────────┐  │     │
│     │  │                CONTENT AREA                            │  │     │
│     │  │                                                        │  │     │
│     │  │  [Dynamic content based on current step/state]        │  │     │
│     │  │                                                        │  │     │
│     │  └─────────────────────────────────────────────────────────┘  │     │
│     │  ┌─────────────────────────────────────────────────────────┐  │     │
│     │  │                 ACTION BUTTONS                         │  │     │
│     │  └─────────────────────────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 1. Initial State - Extension Not Installed

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Setup Your Local AI                               │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  PROGRESS: [●○○] Step 1 of 2: Install Browser Extension       │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  STATUS PANEL                                                 │     │
│     │  ┌─────────────────┐  ┌─────────────────────────────────────┐  │     │
│     │  │ 🔌 Extension    │  │ 🖥️  Bodhi App Server               │  │     │
│     │  │ ❌ Not Installed│  │ ⏸️  Waiting for Extension          │  │     │
│     │  └─────────────────┘  └─────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  INSTALLATION INSTRUCTIONS                                    │     │
│     │                                                               │     │
│     │  📋 Install Bodhi Browser Extension                           │     │
│     │                                                               │     │
│     │  Detected: Chrome 120.0 on macOS                            │     │
│     │                                                               │     │
│     │  1. Click the download button below                          │     │
│     │  2. Follow Chrome's installation prompts                     │     │
│     │  3. Enable the extension when prompted                       │     │
│     │  4. Return here - we'll detect it automatically              │     │
│     │                                                               │     │
│     │  ┌─────────────────────────────────────────────────────────┐  │     │
│     │  │     [📥 Install Extension for Chrome]                   │  │     │
│     │  └─────────────────────────────────────────────────────────┘  │     │
│     │                                                               │     │
│     │  ⚠️  Troubleshooting (click to expand)                       │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  [🔄 Check Again]  [❌ Close]                                  │     │
│     └───────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. Extension Installed but Unreachable

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Setup Your Local AI                               │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  PROGRESS: [●○○] Step 1 of 2: Fix Extension Connection        │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  STATUS PANEL                                                 │     │
│     │  ┌─────────────────┐  ┌─────────────────────────────────────┐  │     │
│     │  │ 🔌 Extension    │  │ 🖥️  Bodhi App Server               │  │     │
│     │  │ ⚠️  Unreachable  │  │ ⏸️  Waiting for Extension          │  │     │
│     │  └─────────────────┘  └─────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  ⚠️  EXTENSION TROUBLESHOOTING                                 │     │
│     │                                                               │     │
│     │  Extension is installed but not responding. Try these steps:  │     │
│     │                                                               │     │
│     │  1. ✅ Check if extension is enabled in Chrome                │     │
│     │     • Go to chrome://extensions/                             │     │
│     │     • Find "Bodhi Browser Extension"                         │     │
│     │     • Make sure the toggle is ON                             │     │
│     │                                                               │     │
│     │  2. 🔄 Refresh the extension                                  │     │
│     │     • Click the extension icon in toolbar                    │     │
│     │     • Or disable/enable in chrome://extensions/             │     │
│     │                                                               │     │
│     │  3. 🔄 Refresh this page                                      │     │
│     │     • Press F5 or Ctrl+R (Cmd+R on Mac)                     │     │
│     │                                                               │     │
│     │  Still having issues? [🐛 Report Bug]                        │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  [🔄 Check Again]  [❌ Close]                                  │     │
│     └───────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 3. Extension Ready - Auto-Advancing to Step 2

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Setup Your Local AI                               │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  PROGRESS: [●●○] Step 2 of 2: Install Bodhi App Server        │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  STATUS PANEL                                                 │     │
│     │  ┌─────────────────┐  ┌─────────────────────────────────────┐  │     │
│     │  │ 🔌 Extension    │  │ 🖥️  Bodhi App Server               │  │     │
│     │  │ ✅ Ready        │  │ ❌ Not Running                      │  │     │
│     │  └─────────────────┘  └─────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  INSTALLATION INSTRUCTIONS                                    │     │
│     │                                                               │     │
│     │  📋 Install Bodhi App Server                                  │     │
│     │                                                               │     │
│     │  Detected: macOS 14.2 (Apple Silicon)                       │     │
│     │                                                               │     │
│     │  The Bodhi App runs locally on your computer and provides    │     │
│     │  AI/LLM services to your browser extension.                  │     │
│     │                                                               │     │
│     │  1. Download the Bodhi App for macOS                         │     │
│     │  2. Open the .dmg file and drag to Applications             │     │
│     │  3. Launch Bodhi App from Applications folder               │     │
│     │  4. Complete the initial setup wizard                        │     │
│     │  5. Keep the app running in the background                   │     │
│     │                                                               │     │
│     │  ┌─────────────────────────────────────────────────────────┐  │     │
│     │  │     [📥 Download Bodhi App for macOS]                   │  │     │
│     │  └─────────────────────────────────────────────────────────┘  │     │
│     │                                                               │     │
│     │  ⚠️  Troubleshooting (click to expand)                       │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  [🔄 Check Again]  [❌ Close]                                  │     │
│     └───────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. Server Needs Setup Configuration

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Setup Your Local AI                               │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  PROGRESS: [●●○] Step 2 of 2: Complete Server Setup           │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  STATUS PANEL                                                 │     │
│     │  ┌─────────────────┐  ┌─────────────────────────────────────┐  │     │
│     │  │ 🔌 Extension    │  │ 🖥️  Bodhi App Server               │  │     │
│     │  │ ✅ Ready        │  │ ⚙️  Needs Setup                     │  │     │
│     │  └─────────────────┘  └─────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  CONFIGURATION REQUIRED                                       │     │
│     │                                                               │     │
│     │  🎉 Great! Bodhi App is running but needs initial setup.     │     │
│     │                                                               │     │
│     │  Complete these steps in the Bodhi App:                      │     │
│     │                                                               │     │
│     │  1. ✅ Launch Bodhi App (already running)                     │     │
│     │  2. 📝 Complete the welcome wizard                            │     │
│     │  3. 🔐 Set up authentication (if required)                   │     │
│     │  4. 📂 Configure model storage location                       │     │
│     │  5. 🤖 Download your first AI model                          │     │
│     │                                                               │     │
│     │  💡 Look for the Bodhi App icon in your menu bar or dock     │     │
│     │                                                               │     │
│     │  ┌─────────────────────────────────────────────────────────┐  │     │
│     │  │     [🚀 Open Bodhi App Settings]                        │  │     │
│     │  └─────────────────────────────────────────────────────────┘  │     │
│     │                                                               │     │
│     │  Need help? [📖 Setup Guide]  [🐛 Report Issue]              │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  [🔄 Check Again]  [❌ Close]                                  │     │
│     └───────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. Server Connection Issues

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Setup Your Local AI                               │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  PROGRESS: [●●○] Step 2 of 2: Fix Server Connection           │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  STATUS PANEL                                                 │     │
│     │  ┌─────────────────┐  ┌─────────────────────────────────────┐  │     │
│     │  │ 🔌 Extension    │  │ 🖥️  Bodhi App Server               │  │     │
│     │  │ ✅ Ready        │  │ 🔴 Connection Failed               │  │     │
│     │  └─────────────────┘  └─────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  🔴 CONNECTION TROUBLESHOOTING                                 │     │
│     │                                                               │     │
│     │  Cannot connect to Bodhi App server. Let's fix this:         │     │
│     │                                                               │     │
│     │  ✅ Check if Bodhi App is running:                           │     │
│     │     • Look for Bodhi icon in menu bar/system tray           │     │
│     │     • If not running, launch from Applications              │     │
│     │                                                               │     │
│     │  🔍 Verify server settings:                                  │     │
│     │     • Default port: 1135                                     │     │
│     │     • Server URL: http://localhost:1135                     │     │
│     │                                                               │     │
│     │  🔥 Check firewall settings:                                 │     │
│     │     • Allow Bodhi App through macOS firewall                │     │
│     │     • Port 1135 should not be blocked                       │     │
│     │                                                               │     │
│     │  🔄 Try restarting:                                          │     │
│     │     • Quit and relaunch Bodhi App                           │     │
│     │     • Refresh this browser page                             │     │
│     │                                                               │     │
│     │  Still stuck? [🐛 Report Connection Issue]                   │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  [🔄 Check Again]  [❌ Close]                                  │     │
│     └───────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 6. Setup Complete - Success State

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Setup Your Local AI                               │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  PROGRESS: [●●●] Setup Complete!                              │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  STATUS PANEL                                                 │     │
│     │  ┌─────────────────┐  ┌─────────────────────────────────────┐  │     │
│     │  │ 🔌 Extension    │  │ 🖥️  Bodhi App Server               │  │     │
│     │  │ ✅ Ready        │  │ ✅ Ready                            │  │     │
│     │  └─────────────────┘  └─────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  🎉 SETUP COMPLETE!                                           │     │
│     │                                                               │     │
│     │         🚀 Your Local AI is Ready to Use! 🚀                 │     │
│     │                                                               │     │
│     │  ✅ Browser extension connected                               │     │
│     │  ✅ Bodhi App server running                                  │     │
│     │  ✅ All systems operational                                   │     │
│     │                                                               │     │
│     │  Your web page can now:                                      │     │
│     │  • Make API requests to your local LLM server               │     │
│     │  • Use streaming responses for real-time AI                 │     │
│     │  • Keep all data private on your machine                    │     │
│     │                                                               │     │
│     │  ┌─────────────────────────────────────────────────────────┐  │     │
│     │  │        [🎯 Continue to Application]                     │  │     │
│     │  └─────────────────────────────────────────────────────────┘  │     │
│     │                                                               │     │
│     │  💡 Keep Bodhi App running in the background for best       │     │
│     │     performance. You can find it in your menu bar.          │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  [❌ Close]                                                    │     │
│     └───────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 7. Unsupported Browser State

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Setup Your Local AI                               │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  PROGRESS: [●○○] Browser Compatibility Check                  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  STATUS PANEL                                                 │     │
│     │  ┌─────────────────┐  ┌─────────────────────────────────────┐  │     │
│     │  │ 🔌 Extension    │  │ 🖥️  Bodhi App Server               │  │     │
│     │  │ ❌ Not Supported│  │ ⏸️  Waiting for Extension          │  │     │
│     │  └─────────────────┘  └─────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  ⚠️  BROWSER NOT SUPPORTED                                     │     │
│     │                                                               │     │
│     │  Detected: Firefox 121.0 on macOS                           │     │
│     │                                                               │     │
│     │  Bodhi Browser Extension currently supports:                 │     │
│     │  ✅ Google Chrome                                            │     │
│     │  ✅ Microsoft Edge                                           │     │
│     │                                                               │     │
│     │  Coming soon:                                                 │     │
│     │  🔄 Mozilla Firefox                                          │     │
│     │  🔄 Safari                                                    │     │
│     │                                                               │     │
│     │  To use Bodhi right now:                                     │     │
│     │  1. 📥 Install Chrome or Edge                                │     │
│     │  2. 🔄 Return to this page in the supported browser         │     │
│     │  3. 🚀 Complete the setup process                            │     │
│     │                                                               │     │
│     │  ┌─────────────────────────────────────────────────────────┐  │     │
│     │  │  [📥 Download Chrome]  [📥 Download Edge]               │  │     │
│     │  └─────────────────────────────────────────────────────────┘  │     │
│     │                                                               │     │
│     │  Want Firefox support? [🗳️  Vote for Firefox Support]       │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  [❌ Close]                                                    │     │
│     └───────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 8. Loading/Detection State

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Setup Your Local AI                               │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  PROGRESS: [●○○] Detecting your setup...                      │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  STATUS PANEL                                                 │     │
│     │  ┌─────────────────┐  ┌─────────────────────────────────────┐  │     │
│     │  │ 🔌 Extension    │  │ 🖥️  Bodhi App Server               │  │     │
│     │  │ 🔄 Checking...  │  │ ⏸️  Waiting...                      │  │     │
│     │  └─────────────────┘  └─────────────────────────────────────┘  │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  🔍 DETECTING YOUR SETUP                                      │     │
│     │                                                               │     │
│     │              🔄 Scanning for components...                    │     │
│     │                                                               │     │
│     │  Detected environment:                                        │     │
│     │  • Browser: Chrome 120.0                                     │     │
│     │  • Operating System: macOS 14.2                              │     │
│     │  • Architecture: Apple Silicon                               │     │
│     │                                                               │     │
│     │  Checking installation status:                               │     │
│     │  🔄 Looking for browser extension...                         │     │
│     │  ⏸️  Waiting to check server status...                       │     │
│     │                                                               │     │
│     │  This usually takes just a few seconds.                      │     │
│     └───────────────────────────────────────────────────────────────┘     │
│                                                                             │
│     ┌───────────────────────────────────────────────────────────────┐     │
│     │  [❌ Close]                                                    │     │
│     └───────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Key Design Elements

### Progress Indicators
- **Step dots**: `[●○○]` (filled = complete, empty = pending)
- **Progress bar**: Alternative linear progress representation
- **Status badges**: Real-time component status with icons

### Status Icons
- ✅ Ready/Success
- ❌ Not installed/Failed  
- ⚠️ Warning/Issue
- 🔄 Loading/Checking
- ⏸️ Waiting/Paused
- 🔴 Error/Connection failed

### Interactive Elements
- **Primary buttons**: Download, Continue actions
- **Secondary buttons**: Check Again, troubleshooting
- **Links**: GitHub issues, documentation
- **Expandable sections**: Troubleshooting details
- **Auto-refresh**: Status updates without user action

### Responsive Considerations
- Modal should scale down on smaller screens
- Stack buttons vertically on mobile
- Reduce padding and font sizes appropriately
- Ensure touch targets are adequate (44px minimum)