# Project Structure

## Monorepo Organization

The repository follows a component-based monorepo structure with each major component in its own directory:

```
bodhi-browser/
├── bodhi-browser-ext/    # Chrome extension (main component)
├── bodhi-js/            # JavaScript library (@bodhiapp/bodhijs)
├── ai-docs/             # AI-focused documentation
└── scripts/             # Shared build scripts
```

## Component Structure Patterns

### Chrome Extension (bodhi-browser-ext/)
```
bodhi-browser-ext/
├── src-ext/             # Extension scripts (TypeScript)
│   ├── background.ts    # Service worker
│   ├── content.ts       # Content script
│   ├── inject.ts        # Page script
│   └── shared/          # Shared utilities
├── src/                 # Next.js UI components
│   ├── components/      # React components
│   └── pages/           # Next.js pages
├── public/              # Static assets + manifest.json
├── tests/               # Test files and test applications
└── dist/                # Build output
```

### JavaScript Library (bodhi-js/)
```
bodhi-js/
├── src/                 # Library source code
│   ├── index.ts         # Main entry point
│   ├── api.ts           # API implementations
│   ├── core.ts          # Core functionality
│   └── types.ts         # Type definitions
├── tests/               # Integration tests
└── dist/                # Build output (CJS + ESM)
```

## Documentation Structure

### AI Documentation (ai-docs/)
```
ai-docs/
├── context/             # System architecture and domain docs
├── feature-specs/       # Feature specifications
├── guides/              # Implementation guides
└── unfiled/             # Temporary documentation
```

## Configuration Files

### Root Level
- `Makefile` - Main build orchestration
- `.gitignore` - Git ignore patterns
- `.prettierrc` - Code formatting rules

### Component Level
- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `eslint.config.js` - Linting rules
- `vitest.config.ts` - Test configuration
- Component-specific Makefile

## Build Artifacts

### Extension Build Output (bodhi-browser-ext/dist/)
```
dist/
├── manifest.json        # Extension manifest
├── background.js        # Compiled service worker
├── content.js          # Compiled content script
├── inject.js           # Compiled page script
├── _next/              # Next.js static export
└── icons/              # Extension icons
```

### Library Build Output (bodhi-js/dist/)
```
dist/
├── bodhi.cjs.js        # CommonJS bundle
├── bodhi.esm.js        # ES module bundle
├── index.d.ts          # Type definitions
└── *.d.ts              # Additional type files
```

## Naming Conventions

### Files
- TypeScript/JavaScript: camelCase for files, PascalCase for React components
- Configuration files: kebab-case (e.g., `eslint.config.js`)
- Test files: `*.test.ts` or `*.test.tsx`

### Directories
- Component directories: kebab-case
- Source directories: lowercase (`src`, `tests`, `dist`)
- Documentation: kebab-case

### Code
- Variables/functions: camelCase
- Classes/interfaces: PascalCase
- Constants: UPPER_SNAKE_CASE
- Types: PascalCase with descriptive suffixes (e.g., `ApiResponse`)

## Import/Export Patterns

### Library Exports
- Main entry: `src/index.ts` exports public API
- Internal modules: Use relative imports
- External dependencies: Import from node_modules

### Extension Scripts
- Shared utilities: `src-ext/shared/` directory
- Cross-script communication: Message passing interfaces
- Chrome APIs: Direct imports from `chrome` global

## Testing Structure

### Test Organization
- Unit tests: Alongside source files or in `__tests__` directories
- Integration tests: `tests/` directory at component root
- E2E tests: Component-specific test applications in `tests/`

### Test Naming
- Test files: `*.test.ts` or `*.test.tsx`
- Test applications: `test-app-*` directories
- Test utilities: `test-*.ts` files