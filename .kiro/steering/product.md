# Product Overview

Bodhi Browser is a secure browser extension and JavaScript library system that enables web applications to interact with locally running LLM services through an OpenAI-compatible API.

## Core Components

- **bodhi-browser-ext**: Chrome extension that acts as a secure bridge between web pages and local LLM servers
- **bodhijs**: JavaScript/TypeScript library (@bodhiapp/bodhijs) providing an easy-to-use interface for web applications

## Key Features

- Secure communication bridge with Content Security Policy implementation
- OpenAI-compatible API for chat completions with streaming support
- Extension-to-extension communication capabilities
- Configurable backend server endpoints
- Zero-dependency core functionality for minimal attack surface
- Comprehensive testing infrastructure with real browser automation

## Target Use Cases

- Web applications needing to access local LLM services
- AI-powered browser extensions
- Privacy-focused AI applications that keep data local
- Development and testing of LLM integrations