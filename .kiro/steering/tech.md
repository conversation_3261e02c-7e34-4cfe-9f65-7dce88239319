# Technology Stack

## Build System
- **Primary**: Make-based monorepo with component-specific Makefiles
- **Package Manager**: npm with package-lock.json
- **Build Tools**: 
  - Webpack for extension scripts
  - Next.js with static export for extension UI
  - Rollup for JavaScript library bundling
  - TypeScript compilation across all components

## Core Technologies
- **Language**: TypeScript/JavaScript (ES modules)
- **Runtime**: Node.js for development, browser for runtime
- **Extension**: Chrome Extension Manifest V3
- **UI Framework**: React with Next.js (extension UI only)
- **Styling**: TailwindCSS for extension UI

## Testing Infrastructure
- **Test Framework**: Vitest for unit/integration tests
- **Browser Automation**: Playwright for end-to-end testing
- **Test Environment**: Real Chrome/Chromium with extension loaded
- **Mock Services**: Custom mock LLM server for testing

## Key Libraries
- **Extension Development**: @types/chrome for Chrome APIs
- **Build Tools**: webpack, rollup, typescript
- **Testing**: @playwright/test, vitest
- **Development**: eslint, prettier for code quality

## Common Commands

### Root Level (Monorepo)
```bash
make setup          # Install all dependencies
make build          # Build all components
make test           # Run all tests
make lint           # Run linting on all components
make lint-fix       # Fix linting issues automatically
make clean          # Clean all build artifacts
```

### Component-Specific
```bash
# Browser Extension
make ext-build      # Build extension only
make ext-test       # Test extension only
make ext-release    # Release extension

# JavaScript Library  
make bodhi-js-build # Build library only
make bodhi-js-test  # Test library only
make bodhi-js-release # Release library

# Mock Server
make mock-llm-server-build # Build mock server
make server-ext     # Run mock server + extension test page
make server-js      # Run mock server + library test page
```

### Development Workflow
```bash
npm run build:fast  # Fast incremental builds
npm run validate    # Run linting and formatting
npm test            # Component-specific testing
npm run lint:fix    # Auto-fix linting issues
```

## Architecture Patterns
- **Monorepo**: Multiple related packages in single repository
- **Message Passing**: Chrome extension messaging with unified message format
- **Streaming**: AsyncIterator pattern for streaming responses
- **Security**: CSP implementation and message validation
- **Testing**: Multi-layer testing (unit, integration, e2e)