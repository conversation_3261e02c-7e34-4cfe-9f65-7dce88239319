#!/usr/bin/env node
/**
 * Script: download-llama-binaries.js
 * ---------------------------------
 * Fetches the latest release of the BodhiSearch/llama.cpp project and downloads
 * all `llama-server` artifacts matching the current platform triple.
 *
 * The binaries are placed under:
 *   bin/<platform-triple>/<variant>/llama-server[.exe]
 * relative to the repository root.
 *
 * The script always checks for the latest release and compares it with the stored
 * release information in bin/latest.json. Downloads are skipped only if the release
 * is the same and binaries already exist.
 */

const fs = require('node:fs');
const path = require('node:path');
const os = require('node:os');
const { pipeline } = require('node:stream/promises');

const GITHUB_API_LATEST =
  'https://api.github.com/repos/BodhiSearch/llama.cpp/releases/latest';

/**
 * Resolve the repository root (two directories up from this script file).
 */
function getRepoRoot() {
  // __dirname => <repo>/scripts
  return path.resolve(__dirname, '..');
}

/**
 * Derive GNU-style platform triple for the current runtime.
 * Supported triples:
 *   - aarch64-apple-darwin
 *   - x86_64-apple-darwin
 *   - x86_64-unknown-linux-gnu
 *   - x86_64-pc-windows-msvc
 */
function getPlatformTriple() {
  const arch = os.arch();
  const platform = os.platform();

  const archPart = arch === 'arm64' ? 'aarch64' : arch === 'x64' ? 'x86_64' : null;
  if (!archPart) {
    console.error(`Unsupported architecture: ${arch}`);
    process.exit(1);
  }

  switch (platform) {
    case 'darwin':
      return `${archPart}-apple-darwin`;
    case 'linux':
      return `${archPart}-unknown-linux-gnu`;
    case 'win32':
      return `${archPart}-pc-windows-msvc`;
    default:
      console.error(`Unsupported platform: ${platform}`);
      process.exit(1);
  }
}

/**
 * Get the stored latest release info from bin/latest.json
 */
function getStoredReleaseInfo(repoRoot) {
  const latestPath = path.join(repoRoot, 'bin', 'latest.json');
  try {
    const content = fs.readFileSync(latestPath, 'utf8').trim();
    return JSON.parse(content);
  } catch (err) {
    return null; // File doesn't exist or is invalid
  }
}

/**
 * Store the latest release info to bin/latest.json
 */
async function storeReleaseInfo(repoRoot, releaseInfo) {
  const latestPath = path.join(repoRoot, 'bin', 'latest.json');
  await fs.promises.mkdir(path.dirname(latestPath), { recursive: true });
  await fs.promises.writeFile(latestPath, JSON.stringify(releaseInfo, null, 2));
}

/**
 * Check if binaries already exist for the platform.
 */
function binariesExist(repoRoot, triple) {
  const binDir = path.join(repoRoot, 'bin', triple);
  return fs.existsSync(binDir);
}

/**
 * Check if we need to download binaries by comparing release info and binary existence.
 */
function needsDownload(storedRelease, currentRelease, repoRoot, triple) {
  // If no stored release info, we need to download
  if (!storedRelease) {
    return true;
  }

  // If release tag is different, we need to download
  if (storedRelease.tag_name !== currentRelease.tag_name) {
    return true;
  }

  // If release is the same but binaries don't exist, we need to download
  if (!binariesExist(repoRoot, triple)) {
    return true;
  }

  // Release is same and binaries exist, no download needed
  return false;
}

/**
 * Fetch JSON from the given URL.
 */
async function fetchJson(url, init = {}) {
  // Node 18+ has native fetch
  const res = await fetch(url, {
    headers: {
      'User-Agent': 'bodhi-browser-binary-fetcher',
      ...init.headers,
      ...(process.env.GITHUB_TOKEN ? { Authorization: `token ${process.env.GITHUB_TOKEN}` } : {}),
    },
  });
  if (!res.ok) {
    throw new Error(`Failed to fetch ${url}: ${res.status} ${res.statusText}`);
  }
  return res.json();
}

/**
 * Download a file at `url` to `destPath` and make it executable.
 */
async function downloadFile(url, destPath) {
  const res = await fetch(url);
  if (!res.ok) {
    throw new Error(`Failed to download ${url}: ${res.status} ${res.statusText}`);
  }
  await fs.promises.mkdir(path.dirname(destPath), { recursive: true });
  const fileStream = fs.createWriteStream(destPath, { mode: 0o755 });
  await pipeline(res.body, fileStream);
  // Ensure executable bit is set (no-op on Windows)
  try {
    await fs.promises.chmod(destPath, 0o755);
  } catch (_) {
    /* ignore */
  }
}

async function main() {
  const repoRoot = getRepoRoot();
  const triple = getPlatformTriple();

  console.log(`Checking for latest llama-server binaries for ${triple}…`);

  // Always fetch the latest release info
  const currentRelease = await fetchJson(GITHUB_API_LATEST);
  if (!currentRelease.assets || !Array.isArray(currentRelease.assets)) {
    console.error('No assets found on the latest release.');
    process.exit(1);
  }

  // Get stored release info
  const storedRelease = getStoredReleaseInfo(repoRoot);

  // Check if we need to download
  if (!needsDownload(storedRelease, currentRelease, repoRoot, triple)) {
    console.log(`Latest binaries for ${triple} (${currentRelease.tag_name}) already exist → skipping download.`);
    process.exit(0);
  }

  // Log what we're doing
  if (!storedRelease) {
    console.log(`No previous release info found. Downloading ${currentRelease.tag_name}…`);
  } else if (storedRelease.tag_name !== currentRelease.tag_name) {
    console.log(`New release found: ${storedRelease.tag_name} → ${currentRelease.tag_name}. Downloading…`);
  } else {
    console.log(`Binaries missing for ${currentRelease.tag_name}. Downloading…`);
  }

  const matchingAssets = currentRelease.assets.filter((asset) =>
    asset.name.startsWith(`llama-server--${triple}--`)
  );

  if (matchingAssets.length === 0) {
    console.error(`No assets found for platform triple ${triple}.`);
    process.exit(1);
  }

  // Remove existing binaries for this platform to ensure clean state
  const platformBinDir = path.join(repoRoot, 'bin', triple);
  if (fs.existsSync(platformBinDir)) {
    console.log(`Removing existing binaries for ${triple}…`);
    await fs.promises.rm(platformBinDir, { recursive: true, force: true });
  }

  for (const asset of matchingAssets) {
    const parts = asset.name.split('--');
    const variantWithExt = parts[2]; // <variant>[.exe]
    const variant = variantWithExt.replace(/\.exe$/, '');
    const fileName = asset.name.endsWith('.exe') ? 'llama-server.exe' : 'llama-server';
    const destPath = path.join(repoRoot, 'bin', triple, variant, fileName);

    console.log(`• ${variant}: downloading…`);
    await downloadFile(asset.browser_download_url, destPath);
    console.log(`  ↳ saved to ${destPath}`);
  }

  // Store the current release info
  const releaseInfo = {
    tag_name: currentRelease.tag_name,
    id: currentRelease.id,
    published_at: currentRelease.published_at,
    html_url: currentRelease.html_url
  };
  await storeReleaseInfo(repoRoot, releaseInfo);

  console.log('All binaries downloaded successfully.');
  console.log(`Release ${currentRelease.tag_name} info stored in bin/latest.json`);
  console.log(`Set BODHI_EXEC_LOOKUP_PATH to: ${repoRoot}`);
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
}); 