.PHONY: all clean build test setup install lint lint-fix help test-stub-server server

# Default target
all: clean lint-fix build test ## Default target, builds and tests everything

# Setup - install dependencies
setup: ## Install dependencies
	@echo "Installing dependencies for mock-llm-server..."
	npm ci
	@echo "Dependencies installed successfully"

# Install - alias for setup
install: ## Alias for setup
	@echo "Installing dependencies for mock-llm-server..."
	npm install
	@echo "Dependencies installed successfully"

# Clean build artifacts
clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	npm run clean
	@echo "Build artifacts cleaned"

# Build the package
build: ## Build the package
	@echo "Building mock-llm-server..."
	npm run build
	@echo "mock-llm-server built successfully"

# Run tests
test: build ## Run tests
	@echo "Testing mock-llm-server..."
	npm test
	@echo "Tests completed successfully"

# Test the stub bodhi server
test-stub-server: build ## Test the stub bodhi server
	@echo "Testing stub bodhi server..."
	npm run test-stub-server
	@echo "Stub server test completed"

# Run the mock LLM server
server: build ## Run the mock LLM server (Ctrl+C to stop)
	@echo "Starting mock LLM server..."
	npm run server -- $(filter-out $@,$(MAKECMDGOALS))
	@echo "Server stopped"

# Run ESLint
lint: ## Run ESLint
	@echo "Running ESLint checks..."
	npm run lint
	@echo "ESLint checks completed"

# Fix ESLint issues automatically
lint-fix: ## Fix ESLint issues automatically
	@echo "Fixing ESLint issues..."
	npm run lint-fix
	@echo "ESLint fixes completed"

server-ext: ## Run the mock-llm-server and a local web server for testing
	@echo "Starting server ext pages and mock-llm-server..."
	npm run server-ext
	@echo "Server stopped"

server-js: ## Run the mock-llm-server and a local web server for testing
	@echo "Starting server js pages and mock-llm-server..."
	npm run server-js
	@echo "Server stopped"

.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z0-9._-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) 