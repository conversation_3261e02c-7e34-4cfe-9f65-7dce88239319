import { spawn, ChildProcess } from 'child_process';
import { platform, arch } from 'os';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import http from 'http';

export const DEFAULT_STUB_BODHI_PORT = 11135;

// ES Module equivalent for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export interface StubBodhiServerOptions {
  port?: number;
}

export interface StubBodhiServer {
  process: ChildProcess;
  port: number;
  close: () => void;
}

/**
 * Gets the appropriate target triple for the current platform
 */
function getTargetTriple(): string {
  const platformMap: Record<string, Record<string, string>> = {
    'win32': {
      'x86_64': 'x86_64-pc-windows-msvc',
      'arm64': 'aarch64-pc-windows-msvc'
    },
    'darwin': {
      'x64': 'x86_64-apple-darwin',
      'arm64': 'aarch64-apple-darwin'
    },
    'linux': {
      'x64': 'x86_64-unknown-linux-gnu',
      'arm64': 'aarch64-unknown-linux-gnu'
    }
  };

  const currentPlatform = platform();
  const currentArch = arch();

  // Handle macOS and Linux with different architectures
  if (platformMap[currentPlatform]?.[currentArch]) {
    return platformMap[currentPlatform][currentArch];
  }

  throw new Error(`Unsupported platform: ${currentPlatform} ${currentArch}`);
}

/**
 * Gets the executable name based on the platform
 */
function getExecutableName(): string {
  return platform() === 'win32' ? 'bodhi.exe' : 'llama-server';
}

/**
 * Gets the full path to the bodhi server binary
 */
function getBinaryPath(): string {
  const targetTriple = getTargetTriple();
  const executableName = getExecutableName();

  const binaryPath = path.resolve(
    __dirname,
    '..',
    'bin',
    targetTriple,
    'default',
    executableName
  );

  if (!fs.existsSync(binaryPath)) {
    throw new Error(
      `Bodhi server binary not found at: ${binaryPath}. ` +
      `Make sure the binary for ${targetTriple} is available.`
    );
  }

  return binaryPath;
}

/**
 * Check if the server is responsive by polling the health endpoint
 */
function checkServerHealth(port: number): Promise<boolean> {
  console.log(`Checking server health on port ${port}...`);
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port,
      path: '/health',
      method: 'GET',
      timeout: 100
    }, (res) => {
      if (res.statusCode === 200) {
        resolve(true);
      } else {
        resolve(false);
      }
    });

    req.on('error', () => {
      resolve(false);
    });

    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

/**
 * Wait for the server to become ready by polling the health endpoint
 */
async function waitForServerReady(port: number): Promise<void> {
  const timeoutMs = 10000; // 30 seconds timeout
  const pollIntervalMs = 100; // 1 second between checks
  const maxAttempts = Math.ceil(timeoutMs / pollIntervalMs);

  console.log(`Waiting for bodhi server to be ready on port ${port}...`);

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    const isHealthy = await checkServerHealth(port);

    if (isHealthy) {
      console.log(`Bodhi server is ready on port ${port}`);
      return;
    }

    if (attempt < maxAttempts) {
      console.log(`Bodhi server not ready yet, attempt ${attempt}/${maxAttempts}, waiting ${pollIntervalMs}ms...`);
      await new Promise(resolve => setTimeout(resolve, pollIntervalMs));
    }
  }

  throw new Error(`Timed out waiting for bodhi server to be ready after ${maxAttempts} attempts (${timeoutMs}ms)`);
}

/**
 * Creates and starts a stub bodhi server using the appropriate binary
 */
export async function startStubBodhiServer(options: StubBodhiServerOptions = {}): Promise<StubBodhiServer> {
  try {
    const port = options.port || DEFAULT_STUB_BODHI_PORT;
    const binaryPath = getBinaryPath();

    console.log(`Starting stub bodhi server on port ${port} using binary: ${binaryPath}`);

    // Prepare arguments for the binary
    const args = [
      '--port', port.toString(),
      '-m',
      path.resolve(process.env.HOME || process.env.USERPROFILE || '', '.cache/huggingface/hub/models--bartowski--Llama-3.2-1B-Instruct-GGUF/snapshots/067b946cf014b7c697f3654f621d577a3e3afd1c/Llama-3.2-1B-Instruct-Q8_0.gguf')
    ];

    // Spawn the process
    const bodhiServerProcess = spawn(binaryPath, args, {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    // Handle process output
    bodhiServerProcess.stdout.on('data', (data) => {
      console.log(`[Bodhi Server]: ${data.toString().trim()}`);
    });

    bodhiServerProcess.stderr.on('data', (data) => {
      console.error(`[Bodhi Server Error]: ${data.toString().trim()}`);
    });

    // Handle process exit
    bodhiServerProcess.on('exit', (code) => {
      console.log(`Bodhi server process exited with code ${code}`);
    });

    bodhiServerProcess.on('close', (code) => {
      console.log(`Bodhi server process closed with code ${code}`);
    });

    // Handle process error
    bodhiServerProcess.on('error', (err) => {
      console.error('Failed to start bodhi server:', err);
      throw err;
    });

    // Wait for the server to be ready
    await waitForServerReady(port);
    // Return the server object
    return {
      process: bodhiServerProcess,
      port,
      close: () => {
        // Force kill on Windows, regular kill on other platforms
        if (platform() === 'win32') {
          spawn('taskkill', ['/pid', bodhiServerProcess.pid!.toString() || '0', '/f', '/t']);
        } else {
          bodhiServerProcess.kill('SIGTERM');
        }
        console.log('Stub bodhi server closed');
      }
    };
  } catch (error) {
    console.error('Error starting stub server:', error);
    throw error;
  }
}