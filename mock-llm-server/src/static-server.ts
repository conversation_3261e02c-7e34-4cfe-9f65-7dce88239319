import http from 'http';
import express from 'express';
import serveStatic from 'serve-static';

// Server port constants
export const DEFAULT_STATIC_PORT = 2222;

export interface StaticServerOptions {
  port?: number;
  staticPath?: string;
}

export interface StaticServer {
  server: http.Server;
  port: number;
  close: () => void;
}

/**
 * Creates and starts a static file server on the specified port
 */
export function startStaticServer(options: StaticServerOptions = {}): StaticServer {
  const port = options.port || DEFAULT_STATIC_PORT;
  const app = express();

  // Serve static files from the provided path
  if (options.staticPath) {
    app.use(serveStatic(options.staticPath));
  }

  // Start the server
  const server = app.listen(port, () => {
    console.log(`Static file server running at http://localhost:${port}`);
  });

  // Return the server and a close function
  return {
    server,
    port,
    close: () => {
      server.close();
      console.log('Static file server closed');
    },
  };
} 