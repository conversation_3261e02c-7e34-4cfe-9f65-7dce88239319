import express from 'express';
import cors from 'cors';
import http from 'http';

export const DEFAULT_LLM_PORT = 11135;

export interface MockLLMServerOptions {
  port?: number;
}

export interface MockLLMServer {
  server: http.Server;
  port: number;
  close: () => void;
}

/**
 * Creates and starts a mock LLM server on the specified port
 */
export function startMockLLMServer(options: MockLLMServerOptions = {}): MockLLMServer {
  const port = options.port || DEFAULT_LLM_PORT;
  console.log(`Starting mock LLM server on port ${port}`);

  const app = express();

  // Enable CORS for all routes and all origins
  app.use(cors());

  // Parse JSON body
  app.use(express.json());

  // Ping endpoint
  app.get('/ping', (req, res) => {
    res.json({ message: 'pong' });
  });

  // Mock chat completions endpoint
  app.post('/v1/chat/completions', (req, res) => {
    const { messages, stream } = req.body;
    const lastMessage = messages[messages.length - 1];
    const responseId = `chatcmpl-${Date.now()}`;
    const model = req.body.model || 'test-model';
    const createdTimestamp = Math.floor(Date.now() / 1000);

    // Handle streaming response
    if (stream) {
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');

      // Generate a response to create more chunks
      const responseContent = `This is a mock response to: ${lastMessage.content}. The streaming feature allows clients to receive tokens incrementally`;

      // Split into words for more natural streaming
      const tokens = responseContent.split(' ');
      let tokenIndex = 0;

      // Send the initial chunk with role
      const initialChunk = {
        id: responseId,
        object: 'chat.completion.chunk',
        created: createdTimestamp,
        model: model,
        choices: [
          {
            index: 0,
            delta: { role: 'assistant', content: '' },
            finish_reason: null,
          },
        ],
      };

      res.write(`data: ${JSON.stringify(initialChunk)}\n\n`);

      // Function to send each token
      const sendNextToken = () => {
        if (tokenIndex < tokens.length) {
          const token = tokens[tokenIndex];

          const chunkData = {
            id: responseId,
            object: 'chat.completion.chunk',
            created: createdTimestamp,
            model: model,
            choices: [
              {
                index: 0,
                delta: { content: token + (tokenIndex < tokens.length - 1 ? ' ' : '') },
                finish_reason: null,
              },
            ],
          };

          res.write(`data: ${JSON.stringify(chunkData)}\n\n`);

          tokenIndex++;
          setTimeout(sendNextToken, 10); // Send next token after 10ms
        } else {
          // Send final chunk with finish_reason
          const finalChunk = {
            id: responseId,
            object: 'chat.completion.chunk',
            created: createdTimestamp,
            model: model,
            choices: [
              {
                index: 0,
                delta: {},
                finish_reason: 'stop',
              },
            ],
          };

          res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
          res.write('data: [DONE]\n\n');
          res.end();
        }
      };

      // Start sending tokens
      sendNextToken();
    } else {
      // Handle non-streaming response
      const responseData = {
        id: responseId,
        object: 'chat.completion',
        created: createdTimestamp,
        model: model,
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: `This is a mock response to: ${lastMessage.content}`,
            },
            finish_reason: 'stop',
          },
        ],
      };

      res.json(responseData);
    }
  });

  // Create the server
  const server = http.createServer(app);

  // Add error handling
  server.on('error', (error: NodeJS.ErrnoException) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`Error: Port ${port} is already in use. Mock LLM server could not start.`);
    } else {
      console.error('Error starting mock LLM server:', error);
    }
    throw error;
  });

  // Start the server
  server.listen(port, () => {
    console.log(`Mock LLM server running at http://localhost:${port}`);
  });

  // Return the server and a close function
  return {
    server,
    port,
    close: () => {
      server.close();
      console.log('Mock LLM server closed');
    },
  };
}