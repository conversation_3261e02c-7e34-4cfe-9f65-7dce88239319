import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { startStubBodhiServer, StubBodhiServer } from './stub-bodhi-server.js';
import axios from 'axios';

// Test configuration
const TEST_PORT = 11135;
const MAX_WAIT_TIME_MS = 10000; // 10 seconds timeout

describe('Stub Bodhi Server', () => {
  let server: StubBodhiServer | null = null;

  beforeAll(async () => {
    try {
      // Start the stub server - will automatically wait for server to be ready
      server = await startStubBodhiServer({
        port: TEST_PORT,
      });
      console.log('Server started successfully and is ready');
    } catch (error) {
      console.warn(`Unable to start server: ${error}`);
      throw error;
    }
  }, MAX_WAIT_TIME_MS + 5000); // Add 5 seconds buffer to the timeout

  afterAll(async () => {
    if (server) {
      server.close();
      console.log('Server closed');
    }
  });

  it('should respond to completions API with valid response', async () => {
    // Skip if server isn't available
    if (!server) {
      throw new Error('Server is not available');
    }

    try {
      // Make HTTP request to the server using Axios
      const response = await axios.post(
        `http://localhost:${TEST_PORT}/v1/completions`,
        {
          model: 'test-model',
          prompt: 'Say this is a test',
          max_tokens: 7,
          temperature: 0
        },
        {
          headers: {
            'Authorization': 'Bearer test-key',
            'Content-Type': 'application/json'
          },
          timeout: 5000
        }
      );

      // Verify status code
      expect(response.status).toBe(200);

      // Verify response structure
      expect(response.data).toHaveProperty('id');
      expect(response.data).toHaveProperty('choices');
      expect(Array.isArray(response.data.choices)).toBe(true);
      expect(response.data.choices.length).toBeGreaterThan(0);
      expect(response.data.choices[0]).toHaveProperty('text');

      console.log('Response validated successfully');
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('API Error:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
      }
      throw error; // Re-throw to fail the test
    }
  });
});