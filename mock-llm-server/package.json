{"name": "@bodhiapp/mock-llm-server", "version": "0.1.0", "description": "Mock LLM server for testing Bodhi browser components", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "type": "module", "files": ["dist", "bin"], "bin": {"mock-llm-server": "dist/bin/server.js"}, "scripts": {"build": "tsc && npm run check-types", "check-types": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint --config eslint.config.js src --ext .ts", "lint-fix": "eslint --config eslint.config.js src --ext .ts --fix", "test": "vitest run", "test:watch": "vitest", "setup": "npm install", "all": "npm run clean && npm run lint && npm run build && npm run test", "server": "node dist/bin/server.js", "server-ext": "concurrently --kill-others-on-fail --names \"LLM-SERVER,TEST-PAGE\" \"npm run server -- -p 1135\" \"npx http-server ../bodhi-browser-ext/tests/test-page/ -p 8080\"", "server-js": "concurrently --kill-others-on-fail --names \"LLM-SERVER,TEST-PAGE\" \"npm run server -- -p 1135\" \"npx http-server ../bodhi-js/tests/test-page/ -p 8080\""}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.3", "serve-static": "^1.15.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/serve-static": "^1.15.5", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "axios": "^1.8.4", "concurrently": "^8.2.2", "eslint": "^9.22.0", "globals": "^13.24.0", "http-server": "^14.1.1", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.0.8"}, "author": "Bodhi App", "license": "MIT"}