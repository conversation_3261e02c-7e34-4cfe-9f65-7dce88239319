# Bodhi Browser Monorepo Makefile
# This Makefile delegates to project-specific Makefiles

.PHONY: all clean build test setup install lint lint-fix format help bodhi-js bodhi-browser-ext mock-llm-server bodhi-js-test-stub mock-llm-server-test-stub

# Default target - builds and tests everything
all: install lint-fix test ## Default target, builds and tests everything

# Setup - install dependencies for all components
setup: ## Install dependencies for all components
	@echo "Installing dependencies for all components..."
	$(MAKE) -C mock-llm-server setup
	$(MAKE) -C setup-modal setup
	$(MAKE) -C test-iframe-srcdoc setup
	$(MAKE) -C bodhi-browser-ext setup
	$(MAKE) -C bodhi-js setup

# Install - install dependencies (using npm install) for all components
install: ## Install dependencies for all components using npm install
	@echo "Installing dependencies for all components..."
	$(MAKE) -C mock-llm-server install
	$(MAKE) -C setup-modal install
	$(MAKE) -C test-iframe-srcdoc install
	$(MAKE) -C bodhi-browser-ext install
	$(MAKE) -C bodhi-js install

# Clean all build artifacts
clean: ## Clean all build artifacts
	$(MAKE) -C mock-llm-server clean
	$(MAKE) -C setup-modal clean
	$(MAKE) -C test-iframe-srcdoc clean
	$(MAKE) -C bodhi-js clean
	$(MAKE) -C bodhi-browser-ext clean


# Build all components
build: ## Build all components
	$(MAKE) -C mock-llm-server build
	$(MAKE) -C setup-modal build
	$(MAKE) -C test-iframe-srcdoc build
	$(MAKE) -C bodhi-browser-ext build
	$(MAKE) -C bodhi-js build

# Run all tests
test: ## Run all tests
	$(MAKE) -C mock-llm-server build
	$(MAKE) -C setup-modal build
	$(MAKE) -C test-iframe-srcdoc build
	$(MAKE) -C bodhi-browser-ext test
	$(MAKE) -C bodhi-js test

# Run all tests
ci.test: ## Run all tests
	$(MAKE) -C mock-llm-server build 
	$(MAKE) -C setup-modal ci.test 
	$(MAKE) -C test-iframe-srcdoc ci.test
	$(MAKE) -C bodhi-browser-ext ci.test
	$(MAKE) -C bodhi-js test

# Run ESLint on all components
lint: ## Run ESLint checks
	$(MAKE) -C mock-llm-server lint
	$(MAKE) -C setup-modal lint
	$(MAKE) -C test-iframe-srcdoc lint
	$(MAKE) -C bodhi-js lint
	$(MAKE) -C bodhi-browser-ext lint


# Fix ESLint issues automatically where possible
lint-fix: ## Fix ESLint and formatting issues automatically
	$(MAKE) -C mock-llm-server lint-fix
	$(MAKE) -C setup-modal lint-fix
	$(MAKE) -C test-iframe-srcdoc lint-fix
	$(MAKE) -C bodhi-browser-ext lint-fix
	$(MAKE) -C bodhi-js lint-fix

# mock-llm-server project targets
mock-llm-server: ## Build and test mock-llm-server
	$(MAKE) -C mock-llm-server all

mock-llm-server-build: ## Build mock-llm-server
	$(MAKE) -C mock-llm-server build

mock-llm-server-test: ## Test mock-llm-server
	$(MAKE) -C mock-llm-server test

# bodhi-js project targets
bodhi-js: ## Build and test bodhi-js
	$(MAKE) -C bodhi-js all


bodhi-js-test: ## Test bodhi-js library
	$(MAKE) -C bodhi-js test

bodhi-js-test-stub: ## Test bodhi-js library using the stub bodhi server
	$(MAKE) -C bodhi-js test-stub

bodhi-js-test-all: ## Test bodhi-js library (including all tests)
	$(MAKE) -C bodhi-js test-all

bodhi-js-build: ## Build bodhi-js library
	$(MAKE) -C bodhi-js build

bodhi-js-validate: ## Run bodhi-js validation
	$(MAKE) -C bodhi-js validate

bodhi-js-release: ## Create and push tag for bodhi-js package release
	$(MAKE) -C bodhi-js release

# bodhi-browser-ext project targets
ext: ## Build and test browser extension
	$(MAKE) -C bodhi-browser-ext all

ext-build: ## Build browser extension
	$(MAKE) -C bodhi-browser-ext build

ext-test: ## Test browser extension
	$(MAKE) -C bodhi-browser-ext test

ext-test-brave: ## Test browser extension with Brave browser
	$(MAKE) -C bodhi-browser-ext test-brave

ext-release: ## Create and push tag for Chrome extension release
	$(MAKE) -C bodhi-browser-ext release

server-ext: ## Run the mock-llm-server and a local web server for testing
	$(MAKE) -C mock-llm-server server-ext

server-js: ## Run the mock-llm-server and a local web server for testing
	$(MAKE) -C mock-llm-server server-js

.DEFAULT_GOAL := help
.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z0-9._-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ''
	@echo 'Project-specific help:'
	@echo '  make -C mock-llm-server help   Show mock-llm-server targets'
	@echo '  make -C bodhi-js help          Show bodhi-js targets'
	@echo '  make -C bodhi-browser-ext help Show bodhi-browser-ext targets' 