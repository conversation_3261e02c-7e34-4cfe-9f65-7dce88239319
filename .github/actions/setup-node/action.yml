name: 'Setup Node.js with caching'
description: 'Sets up Node.js and configures caching for monorepo packages'

inputs:
  node-version:
    description: 'Version of Node.js to use'
    required: false
    default: '22'

runs:
  using: "composite"
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          bodhi-js/package-lock.json
          bodhi-browser-ext/package-lock.json
          integration-tests/package-lock.json
        registry-url: 'https://registry.npmjs.org' 