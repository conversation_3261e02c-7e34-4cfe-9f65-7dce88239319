name: Publish bodhi-browser-ext

on:
  push:
    tags:
      - 'bodhi-browser-ext/v*'

# Prevent concurrent releases
concurrency:
  group: extension-release-${{ github.ref }}
  cancel-in-progress: false

permissions:
  contents: write
  id-token: write

jobs:
  publish:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Set up Node
        uses: ./.github/actions/setup-node

      - name: Extract version information
        id: version
        working-directory: ./bodhi-browser-ext
        run: |
          # Extract the version from the tag
          TAG_VERSION=${GITHUB_REF#refs/tags/bodhi-browser-ext/v}
          echo "tag_version=$TAG_VERSION" >> $GITHUB_OUTPUT
          echo "release_version=$TAG_VERSION" >> $GITHUB_OUTPUT

          # Calculate the next patch version for post-release
          IFS='.' read -ra VERSION_PARTS <<< "$TAG_VERSION"
          NEXT_VERSION="${VERSION_PARTS[0]}.${VERSION_PARTS[1]}.$(( ${VERSION_PARTS[2]} + 1 ))"
          echo "next_version=$NEXT_VERSION" >> $GITHUB_OUTPUT

      - name: Update version for release
        working-directory: ./bodhi-browser-ext
        run: |
          # Update both manifest.json and package.json
          jq '.version = "${{ steps.version.outputs.tag_version }}"' manifest.json > manifest.json.tmp
          mv manifest.json.tmp manifest.json
          npm version ${{ steps.version.outputs.tag_version }} --no-git-tag-version
          npx prettier --write manifest.json

      - name: Install dependencies
        run: make setup

      - name: Build extension
        working-directory: ./bodhi-browser-ext
        run: make ci.release-build

      - name: Package extension
        working-directory: ./bodhi-browser-ext
        run: |
          if [ ! -d "dist" ]; then
            echo "Extension build artifacts not found"
            exit 1
          fi
          cd dist
          zip -r bodhi-browser-ext.zip ./*
          cd ..
          mv dist/bodhi-browser-ext.zip ./

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            bodhi-browser-ext/bodhi-browser-ext.zip
            bodhi-browser-ext/README.md
          name: "bodhi-browser-ext v${{ steps.version.outputs.tag_version }}"
          body: |
            Chrome Extension Release v${{ steps.version.outputs.tag_version }}

            ## Installation
            1. Download the extension zip file
            2. Unzip the file
            3. Load the extension in Chrome from chrome://extensions/ using "Load unpacked"
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TAG_NAME: ${{ github.ref_name }}

      - name: Configure Git for version bump
        run: |
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

      - name: Pull latest changes
        run: |
          # Fetch only the latest commit from main
          git fetch --depth=1 origin main
          git checkout -B main origin/main

      - name: Bump version
        run: |
          cd ./bodhi-browser-ext
          # Update both manifest.json and package.json
          jq '.version = "${{ steps.version.outputs.next_version }}"' manifest.json > manifest.json.tmp
          mv manifest.json.tmp manifest.json
          npm version ${{ steps.version.outputs.next_version }} --no-git-tag-version
          cd ..
          git add bodhi-browser-ext/manifest.json bodhi-browser-ext/package.json
          git commit -m "chore: bump bodhi-browser-ext version to ${{ steps.version.outputs.next_version }} after release [skip ci]"
          git push origin main
