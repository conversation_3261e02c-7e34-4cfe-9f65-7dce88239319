name: Publish bodhijs

on:
  push:
    tags:
      - "bodhi-js/v*"

# Prevent concurrent releases
concurrency:
  group: npm-release-${{ github.ref }}
  cancel-in-progress: false

permissions:
  contents: write
  packages: write
  id-token: write

jobs:
  publish:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Set up Node
        uses: ./.github/actions/setup-node

      - name: Extract version information
        id: version
        working-directory: ./bodhi-js
        run: |
          # Extract the version from the tag
          TAG_VERSION=${GITHUB_REF#refs/tags/bodhi-js/v}
          echo "tag_version=$TAG_VERSION" >> $GITHUB_OUTPUT
          echo "release_version=$TAG_VERSION" >> $GITHUB_OUTPUT

          # Calculate the next patch version for post-release
          IFS='.' read -ra VERSION_PARTS <<< "$TAG_VERSION"
          NEXT_PATCH=$((${VERSION_PARTS[2]} + 1))
          NEXT_VERSION="${VERSION_PARTS[0]}.${VERSION_PARTS[1]}.$NEXT_PATCH-dev"
          echo "next_version=$NEXT_VERSION" >> $GITHUB_OUTPUT

      - name: Update version for release
        working-directory: ./bodhi-js
        run: |
          # Set version from tag directly
          npm version ${{ steps.version.outputs.tag_version }} --no-git-tag-version

      - name: Install dependencies
        working-directory: ./bodhi-js
        run: npm ci

      - name: Generate types and build
        working-directory: ./bodhi-js
        run: |
          # Compile and build
          npm run build

      - name: Verify build artifacts
        working-directory: ./bodhi-js
        run: |
          if [ ! -d "dist" ]; then
            echo "Build artifacts not found"
            exit 1
          fi

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            bodhi-js/dist/*
            bodhi-js/README.md
            bodhi-js/package.json
          name: "bodhijs v${{ steps.version.outputs.tag_version }}"
          body: |
            bodhijs Release v${{ steps.version.outputs.tag_version }}
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TAG_NAME: ${{ github.ref_name }}

      - name: Publish to npm
        working-directory: ./bodhi-js
        run: |
          echo "//registry.npmjs.org/:_authToken=${NPM_TOKEN}" > .npmrc
          npm publish
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Verify NPM Package
        working-directory: ./bodhi-js
        run: |
          # Wait for NPM to index the package
          sleep 10
          PUBLISHED_VERSION=$(npm view @bodhiapp/bodhijs version 2>/dev/null || echo "not found")
          if [ "$PUBLISHED_VERSION" = "not found" ]; then
            echo "Package not found on NPM"
            exit 1
          fi
          echo "Successfully published version $PUBLISHED_VERSION"

      - name: Configure Git for version bump
        run: |
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

      - name: Pull latest changes
        run: |
          # Fetch only the latest commit from main
          git fetch --depth=1 origin main
          git checkout -B main origin/main

      - name: Bump version and add -dev suffix
        run: |
          cd ./bodhi-js
          npm version ${{ steps.version.outputs.next_version }} --no-git-tag-version
          cd ..
          git add bodhi-js/package.json
          git commit -m "chore: bump bodhi-js version to ${{ steps.version.outputs.next_version }} after release [skip ci]"
          git push origin main 