name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      commit_sha:
        description: 'Git commit SHA to build and test'
        required: true
        type: string

# Prevent concurrent runs on same PR/branch
concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  checks: write
  pull-requests: write

jobs:
  build-and-test:
    name: Build and Test (${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    timeout-minutes: 20
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
          # Use specific commit SHA if provided via workflow_dispatch
          ref: ${{ github.event.inputs.commit_sha || '' }}

      - name: Setup Node.js
        uses: ./.github/actions/setup-node

      - name: Install Chrome
        uses: browser-actions/setup-chrome@v1.4.0
        with:
          chrome-version: stable

      # Quick feedback steps
      - name: Install Dependencies
        id: setup
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: make setup

      - name: Run ESLint
        id: lint
        run: make lint

      - name: Run Tests
        id: test
        env:
          INTEG_TEST_AUTH_URL: ${{ vars.INTEG_TEST_AUTH_URL }}
          INTEG_TEST_AUTH_REALM: ${{ vars.INTEG_TEST_AUTH_REALM }}
          INTEG_TEST_CLIENT_ID: ${{ vars.INTEG_TEST_CLIENT_ID }}
          INTEG_TEST_CLIENT_SECRET: ${{ secrets.INTEG_TEST_CLIENT_SECRET }}
        run: make ci.test

      # Artifact upload (only from linux build to save space)
      - name: Upload bodhi-js artifact
        if: success() && matrix.os == 'ubuntu-latest'
        uses: actions/upload-artifact@v4
        with:
          name: bodhi-js-${{ github.event.inputs.commit_sha || github.sha }}
          path: bodhi-js/dist/
          retention-days: 30

      - name: Upload extension artifact
        if: success() && matrix.os == 'ubuntu-latest'
        uses: actions/upload-artifact@v4
        with:
          name: bodhi-browser-ext-${{ github.event.inputs.commit_sha || github.sha }}
          path: bodhi-browser-ext/dist/*.zip
          retention-days: 30

  # Status reporting as a separate job that runs after all builds complete
  report-status:
    name: Report Status
    runs-on: ubuntu-latest
    needs: build-and-test
    if: always()
    steps:
      - name: Report Status
        uses: actions/github-script@v7
        with:
          script: |
            const { owner, repo } = context.repo;
            const run_id = context.runId;
            const run_link = `https://github.com/${owner}/${repo}/actions/runs/${run_id}`;

            const buildStatus = '${{ needs.build-and-test.result }}';
            const commitSha = '${{ github.event.inputs.commit_sha || github.sha }}';

            let statusEmoji;
            switch(buildStatus) {
              case 'success':
                statusEmoji = '✅';
                break;
              case 'failure':
                statusEmoji = '❌';
                break;
              case 'cancelled':
                statusEmoji = '⚠️';
                break;
              default:
                statusEmoji = '❓';
            }

            let message = `## Multi-Platform CI Status\n\nBuild and Test: ${statusEmoji}\n\n`;

            if ('${{ github.event.inputs.commit_sha }}') {
              message += `Commit: ${commitSha}\n\n`;
            }

            message += `Platforms tested: Windows, macOS, and Ubuntu\n\nDetails: ${run_link}`;

            if (context.eventName === 'pull_request') {
              await github.rest.issues.createComment({
                owner,
                repo,
                issue_number: context.issue.number,
                body: message
              });
            }
